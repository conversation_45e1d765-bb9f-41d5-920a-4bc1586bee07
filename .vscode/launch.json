{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "SqlProcessingExample",
            "request": "launch",
            "mainClass": "com.zhihaoscm.service.config.intceptor.SqlProcessingExample",
            "projectName": "com.zhihaoscm-supplier-service"
        },
        {
            "type": "java",
            "name": "SupplierServiceBoot",
            "request": "launch",
            "mainClass": "com.zhihaoscm.service.SupplierServiceBoot",
            "projectName": "com.zhihaoscm-supplier-service"
        },
        {
            "type": "java",
            "name": "TesterApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.hmg.sdk.TesterApplication",
            "projectName": "com.zhihaoscm-hmg-sdk"
        },
        {
            "type": "java",
            "name": "AppApplication",
            "request": "launch",
            "mainClass": "AppApplication",
            "projectName": "zhihaoscm-application"
        },
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "HotWordSdkApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.HotWordSdkApplication",
            "projectName": "com.zhihaoscm-aliyun-hotword-sdk"
        },
        {
            "type": "java",
            "name": "AliyunOCRApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.aliyun.ocr.sdk.AliyunOCRApplication",
            "projectName": "com.zhihaoscm-aliyun-ocr-sdk"
        },
        {
            "type": "java",
            "name": "AliyunSdkApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.aliyun.oss.sdk.AliyunSdkApplication",
            "projectName": "com.zhihaoscm-aliyun-oss-sdk"
        },
        {
            "type": "java",
            "name": "AliyunSmsSdkApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.aliyun.sms.sdk.AliyunSmsSdkApplication",
            "projectName": "com.zhihaoscm-aliyun-sms-sdk"
        },
        {
            "type": "java",
            "name": "MultipartFileUtils",
            "request": "launch",
            "mainClass": "com.zhihaoscm.common.api.util.multipart.file.MultipartFileUtils",
            "projectName": "com.zhihaoscm-common-api"
        },
        {
            "type": "java",
            "name": "TesterApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.common.log.TesterApplication",
            "projectName": "com.zhihaoscm-common-log"
        },
        {
            "type": "java",
            "name": "Generator",
            "request": "launch",
            "mainClass": "com.zhihaoscm.common.mybatis.plus.util.Generator",
            "projectName": "com.zhihaoscm-common-mybatis-plus"
        },
        {
            "type": "java",
            "name": "GeofenceUtil",
            "request": "launch",
            "mainClass": "com.zhihaoscm.common.util.utils.GeofenceUtil",
            "projectName": "com.zhihaoscm-common-utils"
        },
        {
            "type": "java",
            "name": "HMACSHA256Utils",
            "request": "launch",
            "mainClass": "com.zhihaoscm.common.util.utils.HMACSHA256Utils",
            "projectName": "com.zhihaoscm-common-utils"
        },
        {
            "type": "java",
            "name": "CozeSdkApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.coze.CozeSdkApplication",
            "projectName": "com.zhihaoscm-coze-sdk"
        },
        {
            "type": "java",
            "name": "GaodeSdkApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.gaode.GaodeSdkApplication",
            "projectName": "com.zhihaoscm-gaode-sdk"
        },
        {
            "type": "java",
            "name": "HkwsApplication",
            "request": "launch",
            "mainClass": "test.HkwsApplication",
            "projectName": "com.zhihaoscm-hkws-sdk"
        },
        {
            "type": "java",
            "name": "MinioSdkApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.minio.sdk.MinioSdkApplication",
            "projectName": "com.zhihaoscm-minio-sdk"
        },
        {
            "type": "java",
            "name": "TesterApplication(1)",
            "request": "launch",
            "mainClass": "sdk.api.TesterApplication",
            "projectName": "com.zhihaoscm-openai-sdk"
        },
        {
            "type": "java",
            "name": "TestApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.sanyi.sdk.TestApplication",
            "projectName": "com.zhihaoscm-sanyi-sdk"
        },
        {
            "type": "java",
            "name": "AdminPermissionDef$AdminPermission",
            "request": "launch",
            "mainClass": "com.zhihaoscm.domain.meta.AdminPermissionDef$AdminPermission",
            "projectName": "com.zhihaoscm-supplier-domain"
        },
        {
            "type": "java",
            "name": "BusinessContractDef",
            "request": "launch",
            "mainClass": "com.zhihaoscm.domain.meta.biz.BusinessContractDef",
            "projectName": "com.zhihaoscm-supplier-domain"
        },
        {
            "type": "java",
            "name": "PdfUtils",
            "request": "launch",
            "mainClass": "com.zhihaoscm.domain.utils.PdfUtils",
            "projectName": "com.zhihaoscm-supplier-domain"
        },
        {
            "type": "java",
            "name": "HolidayUtils",
            "request": "launch",
            "mainClass": "com.zhihaoscm.domain.utils.holiday.HolidayUtils",
            "projectName": "com.zhihaoscm-supplier-domain"
        },
        {
            "type": "java",
            "name": "NoticeApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.notice.NoticeApplication",
            "projectName": "com.zhihaoscm-supplier-notice"
        },
        {
            "type": "java",
            "name": "remote",
            "request": "attach",
            "hostName": "***************",
            "port": 9080,
            "projectName": "com.zhihaoscm-supplier-service"
        },
        {
            "type": "java",
            "name": "RepaymentServiceImpl",
            "request": "launch",
            "mainClass": "com.zhihaoscm.service.core.service.impl.RepaymentServiceImpl",
            "projectName": "com.zhihaoscm-supplier-service"
        },
        {
            "type": "java",
            "name": "TimeScheduleApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.time.schedule.TimeScheduleApplication",
            "projectName": "com.zhihaoscm-supplier-time-schedule"
        },
        {
            "type": "java",
            "name": "UserCenterApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.usercenter.UserCenterApplication",
            "projectName": "com.zhihaoscm-supplier-user-center"
        },
        {
            "type": "java",
            "name": "WsApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.ws.WsApplication",
            "projectName": "com.zhihaoscm-supplier-ws"
        },
        {
            "type": "java",
            "name": "TesterApplication(2)",
            "request": "launch",
            "mainClass": "com.zhihaoscm.tianditu.TesterApplication",
            "projectName": "com.zhihaoscm-tianditu-sdk"
        },
        {
            "type": "java",
            "name": "WxBotSdkApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.wxbotsdk.WxBotSdkApplication",
            "projectName": "com.zhihaoscm-wx-bot-sdk"
        },
        {
            "type": "java",
            "name": "TesterApplication(3)",
            "request": "launch",
            "mainClass": "com.zhihaoscm.common.sdk.wx.miniapp.api.TesterApplication",
            "projectName": "com.zhihaoscm-wx-miniapp-sdk"
        },
        {
            "type": "java",
            "name": "WxwBotSdkApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.wxwbotsdk.WxwBotSdkApplication",
            "projectName": "com.zhihaoscm-wxw-bot-sdk"
        },
        {
            "type": "java",
            "name": "TesterApplication(4)",
            "request": "launch",
            "mainClass": "com.zhihaoscm.common.sdk.wxw.api.TesterApplication",
            "projectName": "com.zhihaoscm-wxw-sdk"
        },
        {
            "type": "java",
            "name": "TestApplication(1)",
            "request": "launch",
            "mainClass": "com.zhihaoscm.zhilink.sdk.TestApplication",
            "projectName": "com.zhihaoscm-zhi-link-sdk"
        },
        {
            "type": "java",
            "name": "GatewayBoot",
            "request": "launch",
            "mainClass": "com.zhihaoscm.gateway.GatewayBoot",
            "projectName": "com.zhihaoscm-zhihaoscm-gateway"
        },
        {
            "type": "java",
            "name": "JedisExample",
            "request": "launch",
            "mainClass": "com.zhihaoscm.service.JedisExample",
            "projectName": "com.zhihaoscm-zhihaoscm-service"
        },
        {
            "type": "java",
            "name": "Otms3ApiManager",
            "request": "launch",
            "mainClass": "com.zhihaoscm.service.Otms3ApiManager",
            "projectName": "com.zhihaoscm-zhihaoscm-service"
        },
        {
            "type": "java",
            "name": "ShipWeightInference",
            "request": "launch",
            "mainClass": "com.zhihaoscm.service.ShipWeightInference",
            "projectName": "com.zhihaoscm-zhihaoscm-service"
        },
        {
            "type": "java",
            "name": "ShipWeightPrediction",
            "request": "launch",
            "mainClass": "com.zhihaoscm.service.ShipWeightPrediction",
            "projectName": "com.zhihaoscm-zhihaoscm-service"
        },
        {
            "type": "java",
            "name": "ZhihaoscmServiceBoot",
            "request": "launch",
            "mainClass": "com.zhihaoscm.service.ZhihaoscmServiceBoot",
            "projectName": "com.zhihaoscm-zhihaoscm-service"
        },
        {
            "type": "java",
            "name": "AiTest",
            "request": "launch",
            "mainClass": "com.zhihaoscm.service.ai_test.AiTest",
            "projectName": "com.zhihaoscm-zhihaoscm-service"
        },
        {
            "type": "java",
            "name": "ExtractContentWithStyle",
            "request": "launch",
            "mainClass": "com.zhihaoscm.service.ai_test.ExtractContentWithStyle",
            "projectName": "com.zhihaoscm-zhihaoscm-service"
        },
        {
            "type": "java",
            "name": "SpeechRecognizerDemo",
            "request": "launch",
            "mainClass": "com.zhihaoscm.service.ai_test.SpeechRecognizerDemo",
            "projectName": "com.zhihaoscm-zhihaoscm-service"
        },
        {
            "type": "java",
            "name": "WaterLevelScraper",
            "request": "launch",
            "mainClass": "com.zhihaoscm.service.ai_test.WaterLevelScraper",
            "projectName": "com.zhihaoscm-zhihaoscm-service"
        },
        {
            "type": "java",
            "name": "Otms3ApiManager(1)",
            "request": "launch",
            "mainClass": "com.zhihaoscm.Otms3ApiManager",
            "projectName": "com.zhihaoscm-zhihaoscm-user-center"
        },
        {
            "type": "java",
            "name": "WatermarkDemo",
            "request": "launch",
            "mainClass": "com.zhihaoscm.WatermarkDemo",
            "projectName": "com.zhihaoscm-zhihaoscm-user-center"
        },
        {
            "type": "java",
            "name": "UserCenterApplication(1)",
            "request": "launch",
            "mainClass": "com.zhihaoscm.usercenter.UserCenterApplication",
            "projectName": "com.zhihaoscm-zhihaoscm-user-center"
        },
        {
            "type": "java",
            "name": "ZhongJiaoApplication",
            "request": "launch",
            "mainClass": "com.zhihaoscm.zhongjiao.sdk.ZhongJiaoApplication",
            "projectName": "com.zhihaoscm-zhongjiao-sdk"
        },
        {
            "type": "java",
            "name": "ContextManagerBenchmark",
            "request": "launch",
            "mainClass": "org.apache.skywalking.apm.agent.core.context.ContextManagerBenchmark",
            "projectName": "org.apache.skywalking-apm-agent-core"
        },
        {
            "type": "java",
            "name": "HierarchyMatchExceptionBenchmark",
            "request": "launch",
            "mainClass": "org.apache.skywalking.apm.agent.core.context.status.HierarchyMatchExceptionBenchmark",
            "projectName": "org.apache.skywalking-apm-agent-core"
        },
        {
            "type": "java",
            "name": "ClassProviderBenchmark",
            "request": "launch",
            "mainClass": "org.apache.skywalking.apm.agent.core.jvm.clazz.ClassProviderBenchmark",
            "projectName": "org.apache.skywalking-apm-agent-core"
        },
        {
            "type": "java",
            "name": "ThreadProviderBenchmark",
            "request": "launch",
            "mainClass": "org.apache.skywalking.apm.agent.core.jvm.thread.ThreadProviderBenchmark",
            "projectName": "org.apache.skywalking-apm-agent-core"
        },
        {
            "type": "java",
            "name": "LoggingBenchmark",
            "request": "launch",
            "mainClass": "org.apache.skywalking.apm.agent.core.logging.core.LoggingBenchmark",
            "projectName": "org.apache.skywalking-apm-agent-core"
        },
        {
            "type": "java",
            "name": "StringFormatBenchmarkTest",
            "request": "launch",
            "mainClass": "org.apache.skywalking.apm.plugin.solrj.StringFormatBenchmarkTest",
            "projectName": "org.apache.skywalking-apm-solrj-7.x-plugin"
        },
        {
            "type": "java",
            "name": "ArbitrarySetTest",
            "request": "launch",
            "mainClass": "org.apache.skywalking.apm.plugin.ArbitrarySetTest",
            "projectName": "org.apache.skywalking-apm-test-tools"
        },
        {
            "type": "java",
            "name": "LinkedArrayBenchmark",
            "request": "launch",
            "mainClass": "org.apache.skywalking.apm.commons.datacarrier.LinkedArrayBenchmark",
            "projectName": "org.apache.skywalking-java-agent-datacarrier"
        },
        {
            "type": "java",
            "name": "AtomicRangeIntegerTest",
            "request": "launch",
            "mainClass": "org.apache.skywalking.apm.commons.datacarrier.common.AtomicRangeIntegerTest",
            "projectName": "org.apache.skywalking-java-agent-datacarrier"
        },
        {
            "type": "java",
            "name": "GRPCNoServerTest",
            "request": "launch",
            "mainClass": "org.apache.skywalking.apm.network.trace.proto.GRPCNoServerTest",
            "projectName": "org.apache.skywalking-java-agent-network"
        },
        {
            "type": "java",
            "name": "CtClass",
            "request": "launch",
            "mainClass": "org.hotswap.agent.javassist.CtClass",
            "projectName": "org.hotswapagent-hotswap-agent-core"
        },
        {
            "type": "java",
            "name": "Loader",
            "request": "launch",
            "mainClass": "org.hotswap.agent.javassist.Loader",
            "projectName": "org.hotswapagent-hotswap-agent-core"
        },
        {
            "type": "java",
            "name": "Dump",
            "request": "launch",
            "mainClass": "org.hotswap.agent.javassist.tools.Dump",
            "projectName": "org.hotswapagent-hotswap-agent-core"
        },
        {
            "type": "java",
            "name": "framedump",
            "request": "launch",
            "mainClass": "org.hotswap.agent.javassist.tools.framedump",
            "projectName": "org.hotswapagent-hotswap-agent-core"
        },
        {
            "type": "java",
            "name": "Compiler",
            "request": "launch",
            "mainClass": "org.hotswap.agent.javassist.tools.reflect.Compiler",
            "projectName": "org.hotswapagent-hotswap-agent-core"
        },
        {
            "type": "java",
            "name": "Loader(1)",
            "request": "launch",
            "mainClass": "org.hotswap.agent.javassist.tools.reflect.Loader",
            "projectName": "org.hotswapagent-hotswap-agent-core"
        },
        {
            "type": "java",
            "name": "Viewer",
            "request": "launch",
            "mainClass": "org.hotswap.agent.javassist.tools.web.Viewer",
            "projectName": "org.hotswapagent-hotswap-agent-core"
        },
        {
            "type": "java",
            "name": "Webserver",
            "request": "launch",
            "mainClass": "org.hotswap.agent.javassist.tools.web.Webserver",
            "projectName": "org.hotswapagent-hotswap-agent-core"
        },
        {
            "type": "java",
            "name": "ComparableVersion",
            "request": "launch",
            "mainClass": "org.hotswap.agent.versions.ComparableVersion",
            "projectName": "org.hotswapagent-hotswap-agent-core"
        },
        {
            "type": "java",
            "name": "Application",
            "request": "launch",
            "mainClass": "org.hotswap.agent.plugin.mybatis.springboot.Application",
            "projectName": "org.hotswapagent-hotswap-agent-mybatis-plugin"
        },
        {
            "type": "java",
            "name": "Application(1)",
            "request": "launch",
            "mainClass": "org.hotswap.agent.plugin.mybatisplus.Application",
            "projectName": "org.hotswapagent-hotswap-agent-mybatis-plus-plugin"
        },
        {
            "type": "java",
            "name": "Application(2)",
            "request": "launch",
            "mainClass": "org.hotswap.agent.plugin.spring.boot.properties.app.Application",
            "projectName": "org.hotswapagent-hotswap-agent-spring-boot-plugin"
        },
        {
            "type": "java",
            "name": "PluginDocs",
            "request": "launch",
            "mainClass": "org.hotswap.agent.distribution.PluginDocs",
            "projectName": "org.hotswapagent-hotswap-agent"
        },
        {
            "type": "java",
            "name": "UserCenterApplication(2)",
            "request": "launch",
            "mainClass": "com.zhihaoscm.usercenter.UserCenterApplication",
            "projectName": "zhihaoscm-user-center"
        }
    ]
}