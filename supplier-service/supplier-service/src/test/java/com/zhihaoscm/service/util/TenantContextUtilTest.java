package com.zhihaoscm.service.util;

import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <p>TenantContextUtil 测试类</p>
 * 
 * <AUTHOR>
 * @since 2024-07-31
 */
public class TenantContextUtilTest {

    @BeforeEach
    void setUp() {
        // 清理上下文
        UserInfoContextHolder.removeContext();
    }

    @AfterEach
    void tearDown() {
        // 清理上下文
        UserInfoContextHolder.removeContext();
    }

    @Test
    void testExecuteWithoutTenant() {
        // 设置初始租户ID
        UserInfoContext context = new UserInfoContext();
        context.setTenantId(100L);
        UserInfoContextHolder.setContextHolder(context);

        // 验证初始状态
        assertEquals(100L, UserInfoContextHolder.getContext().getTenantId());

        // 在无租户上下文中执行操作
        String result = TenantContextUtil.executeWithoutTenant(() -> {
            // 在执行过程中，tenantId应该为null
            UserInfoContext currentContext = UserInfoContextHolder.getContext();
            assertNotNull(currentContext);
            assertNull(currentContext.getTenantId());
            return "success";
        });

        // 验证返回值
        assertEquals("success", result);

        // 验证租户ID已恢复
        assertEquals(100L, UserInfoContextHolder.getContext().getTenantId());
    }

    @Test
    void testExecuteWithTenant() {
        // 设置初始租户ID
        UserInfoContext context = new UserInfoContext();
        context.setTenantId(100L);
        UserInfoContextHolder.setContextHolder(context);

        // 在指定租户上下文中执行操作
        String result = TenantContextUtil.executeWithTenant(200L, () -> {
            // 在执行过程中，tenantId应该为200L
            UserInfoContext currentContext = UserInfoContextHolder.getContext();
            assertNotNull(currentContext);
            assertEquals(200L, currentContext.getTenantId());
            return "success";
        });

        // 验证返回值
        assertEquals("success", result);

        // 验证租户ID已恢复为原始值
        assertEquals(100L, UserInfoContextHolder.getContext().getTenantId());
    }

    @Test
    void testExecuteWithoutTenantWhenNoOriginalContext() {
        // 确保没有初始上下文
        assertNull(UserInfoContextHolder.getContext());

        // 在无租户上下文中执行操作
        String result = TenantContextUtil.executeWithoutTenant(() -> {
            // 在执行过程中，应该没有上下文
            UserInfoContext currentContext = UserInfoContextHolder.getContext();
            assertNull(currentContext);
            return "success";
        });

        // 验证返回值
        assertEquals("success", result);

        // 验证执行后仍然没有上下文
        assertNull(UserInfoContextHolder.getContext());
    }

    @Test
    void testExecuteWithTenantWhenNoOriginalContext() {
        // 确保没有初始上下文
        assertNull(UserInfoContextHolder.getContext());

        // 在指定租户上下文中执行操作
        String result = TenantContextUtil.executeWithTenant(300L, () -> {
            // 在执行过程中，应该有新创建的上下文
            UserInfoContext currentContext = UserInfoContextHolder.getContext();
            assertNotNull(currentContext);
            assertEquals(300L, currentContext.getTenantId());
            return "success";
        });

        // 验证返回值
        assertEquals("success", result);

        // 验证执行后上下文已被清理
        assertNull(UserInfoContextHolder.getContext());
    }

    @Test
    void testExecuteWithoutTenantRunnable() {
        // 设置初始租户ID
        UserInfoContext context = new UserInfoContext();
        context.setTenantId(100L);
        UserInfoContextHolder.setContextHolder(context);

        // 使用Runnable版本
        TenantContextUtil.executeWithoutTenant(() -> {
            // 在执行过程中，tenantId应该为null
            UserInfoContext currentContext = UserInfoContextHolder.getContext();
            assertNotNull(currentContext);
            assertNull(currentContext.getTenantId());
        });

        // 验证租户ID已恢复
        assertEquals(100L, UserInfoContextHolder.getContext().getTenantId());
    }

    @Test
    void testExecuteWithFunctionAndParameter() {
        // 设置初始租户ID
        UserInfoContext context = new UserInfoContext();
        context.setTenantId(100L);
        UserInfoContextHolder.setContextHolder(context);

        // 使用Function版本
        String result = TenantContextUtil.executeWithoutTenant(
            (String input) -> {
                // 在执行过程中，tenantId应该为null
                UserInfoContext currentContext = UserInfoContextHolder.getContext();
                assertNotNull(currentContext);
                assertNull(currentContext.getTenantId());
                return "processed: " + input;
            },
            "test"
        );

        // 验证返回值
        assertEquals("processed: test", result);

        // 验证租户ID已恢复
        assertEquals(100L, UserInfoContextHolder.getContext().getTenantId());
    }

    @Test
    void testExceptionHandling() {
        // 设置初始租户ID
        UserInfoContext context = new UserInfoContext();
        context.setTenantId(100L);
        UserInfoContextHolder.setContextHolder(context);

        // 测试异常情况下的上下文恢复
        assertThrows(RuntimeException.class, () -> {
            TenantContextUtil.executeWithoutTenant(() -> {
                // 验证在异常抛出前tenantId为null
                assertNull(UserInfoContextHolder.getContext().getTenantId());
                throw new RuntimeException("Test exception");
            });
        });

        // 验证即使发生异常，租户ID也能正确恢复
        assertEquals(100L, UserInfoContextHolder.getContext().getTenantId());
    }
}
