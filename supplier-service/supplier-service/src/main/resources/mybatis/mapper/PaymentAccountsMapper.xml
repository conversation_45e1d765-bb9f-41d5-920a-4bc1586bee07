<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhihaoscm.service.core.mapper.PaymentAccountsMapper">

    <resultMap id="paymentAccountsMap" type="com.zhihaoscm.domain.bean.entity.PaymentAccounts">
        <id property="id" column="id"/>
        <result property="paymentId" column="payment_id"/>
        <result property="accountsId" column="accounts_id"/>
        <result property="paymentInfo" column="payment_info"/>
        <result property="reconciliationId" column="reconciliation_id"/>
        <result property="amount" column="amount"/>
        <result property="paymentDate" column="payment_date"/>
        <result property="paymentWay" column="payment_way"/>
        <result property="del" column="del"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="origin" column="origin"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="paymentInfo" column="payment_info"/>
    </resultMap>


</mapper>
