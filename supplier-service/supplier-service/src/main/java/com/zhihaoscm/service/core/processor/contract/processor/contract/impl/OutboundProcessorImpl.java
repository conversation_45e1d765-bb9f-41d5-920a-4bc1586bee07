package com.zhihaoscm.service.core.processor.contract.processor.contract.impl;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.DealingsEnterprise;
import com.zhihaoscm.domain.bean.entity.Outbound;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.UserVo;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.core.processor.contract.processor.contract.ContractProcessor;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.core.service.usercenter.UserService;

@Service
public class OutboundProcessorImpl implements ContractProcessor {

	@Autowired
	private OutboundService outboundService;

	@Autowired
	private ContractRecordService contractRecordService;

	@Autowired
	private FileService fileService;

	@Autowired
	private WarehouseGoodsInfoService warehouseGoodsInfoService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private SMSProperties wxSubscriptionProperties;

	@Autowired
	private UserService userService;

	@Autowired
	private StorageFeeService storageFeeService;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	@Override
	public Boolean support(Integer type) {
		return PurchaseContractDef.CorrelationTable.OUTBOUND.match(type);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void signComplete(String tableId) {
		outboundService.findOne(tableId).ifPresent(outbound -> {
			// 创建库存明细
			outboundService.handleCreateInventoryDetails(outbound);
			outbound.setSignState(
					BusinessContractDef.CommonSignState.COMPLETED.getCode());

			outbound.setState(OutboundDef.Status.OUTBOUND.getCode());

			if (InboundDef.Type.JXC.match(outbound.getType())) {
				outbound.setReviewTime(LocalDateTime.now());
			}

			Customer customer = null;
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(outbound.getPurchaserBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}

			if (Objects.nonNull(customer)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(wxSubscriptionProperties
								.getFinishOutboundCode())
						.params(Map.of("order_id", outbound.getId()))
						.mobile(customer.getMobile()).build());

				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.INVENTORY.getCode())
						.title(MessageFormat.format(
								UserMessageConstants.OUTBOUND_CONFIRMED_TEMPLATE,
								outbound.getId()))
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.OUTBOUND_DETAIL_PAGE)
						.detailId(String.valueOf(outbound.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}

			outboundService.notice(outbound, 3);

			// 修改状态
			// 重新获取新的签署文件
			Long newFileId = contractRecordService.download(tableId,
					PurchaseContractDef.CorrelationTable.OUTBOUND);
			outbound.setSignFileId(newFileId);
			// 货物信息存储到入库货物信息表中
			outboundService.setGoodsInfo(outbound, outbound.getGoodsInfo());
			outboundService.update(outbound);
			outboundService.setStorageFee(outbound);
		});
	}

	@Override
	public void signReject(String tableId, String contact) {
		outboundService.findOne(tableId).ifPresent(outbound -> {
			if (Objects.nonNull(outbound.getSignFileId())
					&& OutboundDef.SignMode.ONLINE
							.match(outbound.getSignType())) {
				fileService.batchUnActive(List.of(outbound.getSignFileId()));
				outbound.setSignFileId(null);
			}
			outbound.setState(OutboundDef.Status.REJECTED.getCode());
			outbound.setSignState(null);
			outboundService.update(outbound);
		});
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void signing(String correlationId, String name,
			String callbackType) {
		outboundService.findOne(correlationId).ifPresent(outbound -> {

			Long newFileId = contractRecordService.download(correlationId,
					PurchaseContractDef.CorrelationTable.OUTBOUND);
			fileService.batchUnActive(List.of(outbound.getSignFileId()));
			outbound.setSignFileId(newFileId);

			// 防止契约锁回调顺序有问题的处理
			if (Objects.equals(outbound.getState(),
					OutboundDef.Status.OUTBOUND.getCode())) {
				return;
			}

			boolean isBuyer = false;
			if (PurchaseContractDef.CallbackType.SEAL.name()
					.equals(callbackType)) {
				isBuyer = name
						.equals(outbound.getPurchaserEnterprise().getName());
			} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
					.equals(callbackType)) {
				isBuyer = name.equals(
						outbound.getPurchaserEnterprise().getRealName());
			}
			boolean isSupplier = false;
			if (PurchaseContractDef.CallbackType.SEAL.name()
					.equals(callbackType)) {
				isSupplier = name
						.equals(outbound.getSellerEnterprise().getName());
			} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
					.equals(callbackType)) {
				isSupplier = name
						.equals(outbound.getSellerEnterprise().getRealName());
			}
			if (isBuyer) {
				if (BusinessContractDef.CommonSignState.SUPPLY_SIGNED
						.match(outbound.getSignState())) {
					outbound.setSignState(
							BusinessContractDef.CommonSignState.COMPLETED
									.getCode());
				} else {
					outbound.setSignState(
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode());
				}
			} else if (isSupplier) {
				if (BusinessContractDef.CommonSignState.BUYER_SIGNED
						.match(outbound.getSignState())) {
					outbound.setSignState(
							BusinessContractDef.CommonSignState.COMPLETED
									.getCode());
				} else {
					outbound.setSignState(
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode());
				}
				// 设置出库人
				String signMobile = outbound.getSellerEnterprise()
						.getSignMobile();
				if (Objects.nonNull(signMobile)) {
					UserVo userVo = userService.findByMobile(signMobile)
							.orElse(null);
					if (Objects.nonNull(userVo)) {
						outbound.setReviewer(userVo.getUser().getId());
					}
				}
				if (InboundDef.Type.STORAGE.match(outbound.getType())) {
					outbound.setReviewTime(LocalDateTime.now());
				}
			}
			outboundService.update(outbound);
		});

	}

	@Override
	public void sendInvalid(String tableId, String name) {
		outboundService.findOne(tableId).ifPresent(outbound -> {
			if (OutboundDef.Status.INVALIDING.match(outbound.getState())
					&& Objects.nonNull(outbound.getInvalidFileId())) {
				return;
			}
			// 作废后获取作废合同id
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.OUTBOUND);
			Outbound outbound1 = new Outbound();
			outbound1.setId(outbound.getId());
			outbound1.setInvalidFileId(fileId);
			outbound1.setState(OutboundDef.Status.INVALIDING.getCode());
			outbound1.setInvalidSignState(
					PurchaseContractDef.CommonSignState.UNSIGNED.getCode());
			outboundService.updateNotNull(outbound1);
		});
	}

	@Override
	public void invaliding(String tableId, String name) {
		outboundService.findOne(tableId).ifPresent(outbound -> {
			// 防止契约锁回调顺序有问题的处理
			if (Objects.equals(OutboundDef.Status.INVALIDED.getCode(),
					outbound.getState())) {
				return;
			}
			boolean isBuyer;
			boolean isSupplier;
			isBuyer = name.equals(outbound.getPurchaserEnterprise().getName())
					|| name.equals(
							outbound.getPurchaserEnterprise().getRealName());

			isSupplier = name.equals(outbound.getSellerEnterprise().getName())
					|| name.equals(
							outbound.getSellerEnterprise().getRealName());

			if (isBuyer) {
				outbound.setInvalidSignState(
						PurchaseContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				outbound.setPurchaseInvalidTime(LocalDateTime.now());
			} else if (isSupplier) {
				outbound.setInvalidSignState(
						PurchaseContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
				outbound.setSellerInvalidTime(LocalDateTime.now());
			}

			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.OUTBOUND);
			outbound.setInvalidFileId(fileId);
			outboundService.update(outbound);
		});
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void invalided(String tableId, String name) {
		outboundService.findOne(tableId).ifPresent(outbound -> {
			outbound.setState(OutboundDef.Status.INVALIDED.getCode());
			outbound.setInvalidSignState(
					PurchaseContractDef.CommonSignState.COMPLETED.getCode());
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.OUTBOUND);
			outbound.setInvalidFileId(fileId);
			warehouseGoodsInfoService.deleteByRelateId(outbound.getId());
			outboundService.update(outbound);
			storageFeeService.deleteByRelateId(List.of(outbound.getId()));
		});
	}

	@Override
	public void rejectInvalid(String tableId, String name) {
		outboundService.findOne(tableId).ifPresent(outbound -> {

			outbound.setState(OutboundDef.Status.OUTBOUND.getCode());
			outbound.setInvalidSignState(null);
			outbound.setInvalidRevokeTime(LocalDateTime.now());
			outboundService.updateAllProperties(outbound);
			outboundService.setGoodsInfo(outbound, outbound.getGoodsInfo());
			// 处理库存明细数据
			outboundService.handleUpdateInventoryDetails(outbound,
					CommonDef.Symbol.YES.getCode());
		});

	}
}
