package com.zhihaoscm.service.resource.form.signreceipt;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import org.hibernate.validator.constraints.Length;

import com.zhihaoscm.domain.bean.entity.DeliverGoods;
import com.zhihaoscm.domain.bean.entity.SignReceipt;
import com.zhihaoscm.domain.meta.biz.DeliverGoodsDef;
import com.zhihaoscm.domain.meta.biz.OrderDef;
import com.zhihaoscm.domain.meta.biz.SignReceiptDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SignReceiptBuyCreateForm {

	/**
	 * 项目id
	 */
	@Schema(description = "项目id")
	@NotNull(message = ErrorCode.CODE_30151002)
	private String projectId;

	/**
	 * 项目名称
	 */
	@Schema(description = "项目名称")
	private String projectName;

	/**
	 * 合同id
	 */
	@Schema(description = "合同id")
	@NotNull(message = ErrorCode.CODE_30149003)
	private String contractId;

	/**
	 * 签收重量
	 */
	@Schema(description = "签收重量")
	@NotNull(message = ErrorCode.CODE_30097028)
	private BigDecimal receiptWeight;

	/**
	 * 签收确认日期
	 */
	@Schema(description = "签收确认日期")
	@NotNull(message = ErrorCode.CODE_30097005)
	private LocalDateTime signConfirmDate;

	/**
	 * 签收单据文件id
	 */
	@Schema(description = "签收单据文件id")
	private Long signReceiptFileId;

	/**
	 * 备注
	 */
	@Schema(description = "备注")
	@Length(max = 200, message = ErrorCode.CODE_30097026)
	private String remark;

	/**
	 * 签署方式
	 */
	@Schema(description = "签署方式")
	private Integer signType;

	/**
	 * 发货单列表
	 */
	@Schema(description = "发货单列表")
	List<DeliverGoods> deliverGoodsList;

	/**
	 * 保存类型
	 */
	@Schema(description = "保存类型 1：保存为草稿 2：保存并提交")
	private Integer saveType;

	public SignReceipt convertEntity() {
		SignReceipt signReceipt = new SignReceipt();
		signReceipt.setProjectId(this.projectId);
		signReceipt.setProjectName(this.getProjectName());
		signReceipt.setContractId(this.getContractId());
		signReceipt.setSignConfirmDate(this.signConfirmDate);
		signReceipt.setSignReceiptFileId(this.signReceiptFileId);
		signReceipt.setRemark(this.remark);
		signReceipt.setType(SignReceiptDef.Type.BUY.getCode());
		signReceipt.setSignType(
				Objects.nonNull(this.signType) ? this.signType : null);
		signReceipt.setReceiptWeight(this.receiptWeight);
		if (Objects.nonNull(this.saveType)) {
			if (DeliverGoodsDef.SignMode.OFFLINE.match(this.signType)) {
				signReceipt.setStatus(DeliverGoodsDef.SaveType.SAVE_TO_DRAFT
						.match(this.saveType)
								? SignReceiptDef.Status.DRAFT.getCode()
								: SignReceiptDef.Status.CONFIRMING.getCode());
			} else {
				signReceipt.setStatus(DeliverGoodsDef.SaveType.SAVE_TO_DRAFT
						.match(this.saveType)
								? SignReceiptDef.Status.DRAFT.getCode()
								: SignReceiptDef.Status.TO_BE_INITIATE
										.getCode());
			}
		} else {
			signReceipt.setStatus(SignReceiptDef.Status.FINISHED.getCode());
			signReceipt.setReconciliationStatus(
					OrderDef.BusinessStatus.NOT_STARTED.getCode());
		}
		return signReceipt;
	}

}
