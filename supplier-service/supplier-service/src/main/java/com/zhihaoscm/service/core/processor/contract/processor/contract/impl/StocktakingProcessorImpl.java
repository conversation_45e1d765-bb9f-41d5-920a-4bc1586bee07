package com.zhihaoscm.service.core.processor.contract.processor.contract.impl;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.DealingsEnterprise;
import com.zhihaoscm.domain.bean.entity.Stocktaking;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.core.processor.contract.processor.contract.ContractProcessor;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

@Service
public class StocktakingProcessorImpl implements ContractProcessor {

	@Autowired
	private StocktakingService stocktakingService;

	@Autowired
	private ContractRecordService contractRecordService;

	@Autowired
	private FileService fileService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private SMSProperties wxSubscriptionProperties;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	@Override
	public Boolean support(Integer type) {
		return PurchaseContractDef.CorrelationTable.STOCKTAKING.match(type);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void signComplete(String tableId) {
		stocktakingService.findOne(tableId).ifPresent(stocktaking -> {
			if (StocktakingDef.State.FINISHED.match(stocktaking.getState())) {
				return;
			}
			stocktaking.setSignStatus(
					BusinessContractDef.CommonSignState.COMPLETED.getCode());
			stocktaking.setState(StocktakingDef.State.FINISHED.getCode());
			// 修改状态
			// 重新获取新的签署文件
			Long newFileId = contractRecordService.download(tableId,
					PurchaseContractDef.CorrelationTable.STOCKTAKING);
			stocktaking.setStocktakingFileId(newFileId);

			Customer customer = null;
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(stocktaking.getPurchaserBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}

			if (Objects.nonNull(customer)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(wxSubscriptionProperties
								.getFinishStocktakingCode())
						.params(Map.of("order_id", stocktaking.getId()))
						.mobile(customer.getMobile()).build());

				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.INVENTORY.getCode())
						.title(MessageFormat.format(
								UserMessageConstants.STOCKTAKING_CONFIRMED_TEMPLATE,
								stocktaking.getId()))
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(StocktakingDef.Module.JXC
								.match(stocktaking.getModule())
										? UserMessageConstants.STOCKTAKING_DETAIL_PAGE
										: UserMessageConstants.SUPERVISION_STOCKTAKING_DETAIL_PAGE)
						.detailId(String.valueOf(stocktaking.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}

			if (StocktakingDef.Module.JXC.match(stocktaking.getModule())) {
				stocktakingService.notice(stocktaking, 3);
			} else {
				stocktakingService.notice(stocktaking, 8);
			}
			stocktakingService.update(stocktaking);
		});
	}

	@Override
	public void signReject(String tableId, String contact) {
		stocktakingService.findOne(tableId).ifPresent(stocktaking -> {
			if (Objects.nonNull(stocktaking.getStocktakingFileId())
					&& OutboundDef.SignMode.ONLINE
							.match(stocktaking.getSignType())) {
				fileService.batchUnActive(
						List.of(stocktaking.getStocktakingFileId()));
				stocktaking.setStocktakingFileId(null);
			}
			stocktaking.setState(StocktakingDef.State.REJECTED.getCode());
			stocktaking.setSignStatus(null);
			stocktakingService.update(stocktaking);
		});
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void signing(String correlationId, String name,
			String callbackType) {
		stocktakingService.findOne(correlationId).ifPresent(stocktaking -> {

			Long newFileId = contractRecordService.download(correlationId,
					PurchaseContractDef.CorrelationTable.STOCKTAKING);
			fileService
					.batchUnActive(List.of(stocktaking.getStocktakingFileId()));
			stocktaking.setStocktakingFileId(newFileId);

			// 防止契约锁回调顺序有问题的处理
			if (Objects.equals(stocktaking.getState(),
					StocktakingDef.State.FINISHED.getCode())) {
				return;
			}

			boolean isBuyer = false;
			if (PurchaseContractDef.CallbackType.SEAL.name()
					.equals(callbackType)) {
				isBuyer = name
						.equals(stocktaking.getPurchaserEnterprise().getName());
			} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
					.equals(callbackType)) {
				isBuyer = name.equals(
						stocktaking.getPurchaserEnterprise().getRealName());
			}
			boolean isSupplier = false;
			if (PurchaseContractDef.CallbackType.SEAL.name()
					.equals(callbackType)) {
				isSupplier = name
						.equals(stocktaking.getSellerEnterprise().getName());
			} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
					.equals(callbackType)) {
				isSupplier = name.equals(
						stocktaking.getSellerEnterprise().getRealName());
			}
			if (isBuyer) {
				if (BusinessContractDef.CommonSignState.SUPPLY_SIGNED
						.match(stocktaking.getSignStatus())) {
					stocktaking.setSignStatus(
							BusinessContractDef.CommonSignState.COMPLETED
									.getCode());
				} else {
					stocktaking.setSignStatus(
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode());
				}
			} else if (isSupplier) {
				if (BusinessContractDef.CommonSignState.BUYER_SIGNED
						.match(stocktaking.getSignStatus())) {
					stocktaking.setSignStatus(
							BusinessContractDef.CommonSignState.COMPLETED
									.getCode());
				} else {
					stocktaking.setSignStatus(
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode());
				}
			}
			stocktakingService.update(stocktaking);
		});

	}

	@Override
	public void sendInvalid(String tableId, String name) {
		stocktakingService.findOne(tableId).ifPresent(stocktaking -> {
			if (StocktakingDef.State.INVALIDING.match(stocktaking.getState())
					&& Objects.nonNull(stocktaking.getInvalidFileId())) {
				return;
			}
			// 作废后获取作废合同id
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.STOCKTAKING);
			Stocktaking stocktaking1 = new Stocktaking();
			stocktaking1.setInvalidFileId(fileId);
			stocktaking1.setState(StocktakingDef.State.INVALIDING.getCode());
			stocktaking1.setInvalidSignState(
					PurchaseContractDef.CommonSignState.UNSIGNED.getCode());
			stocktakingService.updateNotNull(stocktaking1);
		});
	}

	@Override
	public void invaliding(String tableId, String name) {
		stocktakingService.findOne(tableId).ifPresent(stocktaking -> {
			// 防止契约锁回调顺序有问题的处理
			if (Objects.equals(StocktakingDef.State.INVALID.getCode(),
					stocktaking.getState())) {
				return;
			}
			boolean isBuyer;
			boolean isSupplier;
			isBuyer = name
					.equals(stocktaking.getPurchaserEnterprise().getName())
					|| name.equals(
							stocktaking.getPurchaserEnterprise().getRealName());

			isSupplier = name
					.equals(stocktaking.getSellerEnterprise().getName())
					|| name.equals(
							stocktaking.getSellerEnterprise().getRealName());

			if (isBuyer) {
				stocktaking.setInvalidSignState(
						PurchaseContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				stocktaking.setPurchaseInvalidTime(LocalDateTime.now());
			} else if (isSupplier) {
				stocktaking.setInvalidSignState(
						PurchaseContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
				stocktaking.setSellerInvalidTime(LocalDateTime.now());
			}

			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.STOCKTAKING);
			stocktaking.setInvalidFileId(fileId);
			stocktakingService.update(stocktaking);
		});
	}

	@Override
	public void invalided(String tableId, String name) {
		stocktakingService.findOne(tableId).ifPresent(stocktaking -> {
			stocktaking.setState(StocktakingDef.State.INVALID.getCode());
			stocktaking.setInvalidSignState(
					PurchaseContractDef.CommonSignState.COMPLETED.getCode());
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.STOCKTAKING);
			stocktaking.setInvalidFileId(fileId);
			stocktakingService.update(stocktaking);
		});
	}

	@Override
	public void rejectInvalid(String tableId, String name) {
		stocktakingService.findOne(tableId).ifPresent(stocktaking -> {
			stocktaking.setState(StocktakingDef.State.FINISHED.getCode());
			stocktaking.setInvalidSignState(null);
			stocktaking.setInvalidRevokeTime(LocalDateTime.now());
			stocktakingService.updateAllProperties(stocktaking);
		});

	}
}
