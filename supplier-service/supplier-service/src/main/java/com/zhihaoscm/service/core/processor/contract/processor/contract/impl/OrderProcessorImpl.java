package com.zhihaoscm.service.core.processor.contract.processor.contract.impl;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.DealingsEnterprise;
import com.zhihaoscm.domain.bean.entity.Order;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.core.processor.contract.processor.contract.ContractProcessor;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

@Service
public class OrderProcessorImpl implements ContractProcessor {

	@Autowired
	private OrderService orderService;

	@Autowired
	private FileService fileService;

	@Autowired
	private ContractRecordService contractRecordService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private SMSProperties wxSubscriptionProperties;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	@Autowired
	private ContractService contractService;

	@Override
	public Boolean support(Integer type) {
		return PurchaseContractDef.CorrelationTable.ORDER.match(type);
	}

	@Override
	public void signComplete(String tableId) {
		orderService.findOne(tableId).ifPresent(order -> {
			Integer completeStatus = BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.FINISHED.getCode(),
					BusinessContractDef.CommonSignState.COMPLETED.getCode());
			// 修改状态
			// 重新获取新的签署文件
			Long newFileId = contractRecordService.download(tableId,
					PurchaseContractDef.CorrelationTable.ORDER);
			order.setVoucherFileId(newFileId);
			order.setStatus(completeStatus);
			if (OrderDef.OrderType.DELIVER_APPLY.match(order.getType())) {
				order.setStatus(BusinessContractDef.SET_STATE.apply(
						OrderDef.Status.CONFIRMING.getCode(),
						BusinessContractDef.CommonSignState.BUYER_SIGNED
								.getCode()));
			}

			contractService.findOne(order.getContractId())
					.ifPresent(contract -> {
						if (ContractDef.ContractType.SALES
								.match(contract.getContractType())) {
							DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
									.findOne(order.getPurchaserBusinessId())
									.orElse(null);

							if (Objects.nonNull(dealingsEnterprise)
									&& Objects.nonNull(dealingsEnterprise
											.getCustomerId())) {
								Customer customer = customerService.findOne(
										dealingsEnterprise.getCustomerId())
										.orElse(null);

								if (Objects.nonNull(customer)) {
									messageService.sendNotice(AliMessage
											.builder()
											.receiptors(List.of(String
													.valueOf(customer.getId())))
											.templateCode(
													wxSubscriptionProperties
															.getFinishOrderCode())
											.params(Map.of("order_id",
													order.getId()))
											.mobile(customer.getMobile())
											.build());

									messageService.sendNotice(UserMessage
											.builder()
											.type(UserMessageDef.MessageType.ORDER
													.getCode())
											.title(MessageFormat.format(
													UserMessageConstants.ORDER_CONFIRMA_TEMPLATE,
													order.getId()))
											.receiptors(List.of(String
													.valueOf(customer.getId())))
											.url(UserMessageConstants.FIND_BOAT_DETAIL_PAGE)
											.detailId(String
													.valueOf(order.getId()))
											.initiator(
													UserMessageDef.BusinessInitiator.receipt
															.getCode())
											.build());
								}
							}

						}
					});
			orderService.update(order);
		});
	}

	@Override
	public void signReject(String tableId, String contact) {
		orderService.findOne(tableId)
				.ifPresent(order -> orderService.revert(order, Boolean.FALSE,
						CommonDef.AccountSource.INNER.getCode()));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void signing(String correlationId, String name,
			String callbackType) {
		orderService.findOne(correlationId).ifPresent(order -> {
			Long newFileId = contractRecordService.download(correlationId,
					PurchaseContractDef.CorrelationTable.ORDER);
			fileService.batchUnActive(List.of(order.getVoucherFileId()));
			order.setVoucherFileId(newFileId);

			// 防止契约锁回调顺序有问题的处理
			Integer completeStatus = BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.FINISHED.getCode(),
					BusinessContractDef.CommonSignState.COMPLETED.getCode());

			Integer apply = BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.CONFIRMING.getCode(),
					BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode());

			if (Objects.equals(completeStatus, order.getStatus())) {
				return;
			}

			if (Objects.equals(apply, order.getStatus())
					&& OrderDef.OrderType.DELIVER_APPLY
							.match(order.getType())) {
				return;
			}

			boolean isBuyer = false;
			if (PurchaseContractDef.CallbackType.SEAL.name()
					.equals(callbackType)) {
				isBuyer = name.equals(order.getPurchaserEnterprise().getName());
			} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
					.equals(callbackType)) {
				isBuyer = name
						.equals(order.getPurchaserEnterprise().getRealName());
			}
			boolean isSupplier = false;
			if (PurchaseContractDef.CallbackType.SEAL.name()
					.equals(callbackType)) {
				isSupplier = name.equals(order.getSellerEnterprise().getName());
			} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
					.equals(callbackType)) {
				isSupplier = name
						.equals(order.getSellerEnterprise().getRealName());
			}

			if (isBuyer) {
				if (BusinessContractDef.SET_STATE.apply(
						OrderDef.Status.SIGNING.getCode(),
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode())
						.equals(order.getStatus())) {
					order.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.FINISHED.getCode(),
							BusinessContractDef.CommonSignState.COMPLETED
									.getCode()));
				} else {
					order.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.SIGNING.getCode(),
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode()));
				}
			} else if (isSupplier) {
				if (BusinessContractDef.SET_STATE
						.apply(OrderDef.Status.SIGNING.getCode(),
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode())
						.equals(order.getStatus())) {
					order.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.FINISHED.getCode(),
							BusinessContractDef.CommonSignState.COMPLETED
									.getCode()));
				} else {
					order.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.SIGNING.getCode(),
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode()));
				}
			}
			orderService.update(order);
		});
	}

	@Override
	public void sendInvalid(String tableId, String name) {
		orderService.findOne(tableId).ifPresent(order -> {
			if (OrderDef.Status.INVALIDING.match(
					BusinessContractDef.GET_HIGH_STATE.apply(order.getStatus()))
					&& Objects.nonNull(order.getInvalidFileId())) {
				return;
			}
			// 作废后获取作废合同id
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.ORDER);
			Order order1 = new Order();
			order1.setInvalidFileId(fileId);
			order1.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.INVALIDING.getCode(),
					BusinessContractDef.CommonSignState.UNSIGNED.getCode()));
			orderService.updateNotNull(order1);
		});
	}

	@Override
	public void invaliding(String tableId, String name) {
		orderService.findOne(tableId).ifPresent(order -> {

			// 防止契约锁回调顺序有问题的处理
			if (Objects.equals(ContractDef.State.INVALID.getCode(),
					BusinessContractDef.GET_HIGH_STATE
							.apply(order.getStatus()))) {
				return;
			}
			boolean isBuyer;
			boolean isSupplier;
			isBuyer = name.equals(order.getPurchaserEnterprise().getName())
					|| name.equals(
							order.getPurchaserEnterprise().getRealName());

			isSupplier = name.equals(order.getSellerEnterprise().getName())
					|| name.equals(order.getSellerEnterprise().getRealName());

			if (isBuyer) {
				order.setStatus(BusinessContractDef.SET_STATE.apply(
						OrderDef.Status.INVALIDING.getCode(),
						BusinessContractDef.CommonSignState.BUYER_SIGNED
								.getCode()));
				order.setPurchaseInvalidTime(LocalDateTime.now());
			} else if (isSupplier) {
				order.setStatus(BusinessContractDef.SET_STATE.apply(
						OrderDef.Status.INVALIDING.getCode(),
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode()));
				order.setSellerInvalidTime(LocalDateTime.now());
			}

			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.ORDER);
			order.setInvalidFileId(fileId);
			orderService.update(order);
		});
	}

	@Override
	public void invalided(String tableId, String name) {
		orderService.findOne(tableId).ifPresent(order -> {
			order.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.INVALID.getCode(),
					BusinessContractDef.CommonSignState.COMPLETED.getCode()));
			if (OrderDef.OrderType.DELIVER_APPLY.match(order.getType())) {
				if (CommonDef.UserType.OUTER
						.match(order.getInvalidInitiator())) {
					order.setStatus(BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.INVALIDING.getCode(),
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode()));
				}
				order.setPurchaseInvalidTime(LocalDateTime.now());
			}
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.ORDER);
			order.setInvalidFileId(fileId);
			orderService.update(order);
		});
	}

	@Override
	public void rejectInvalid(String tableId, String name) {
		orderService.findOne(tableId).ifPresent(order -> {
			order.setStatus(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.FINISHED.getCode(),
					BusinessContractDef.CommonSignState.COMPLETED.getCode()));
			order.setInvalidRevokeTime(LocalDateTime.now());
			orderService.updateAllProperties(order);
		});
	}
}
