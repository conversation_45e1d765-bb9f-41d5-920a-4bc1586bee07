package com.zhihaoscm.service.core.processor.contract.processor.contract.impl;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.DealingsEnterprise;
import com.zhihaoscm.domain.bean.entity.Reconciliation;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.core.processor.contract.processor.contract.ContractProcessor;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

@Service
public class PreReconciliationProcessorImpl implements ContractProcessor {

	@Autowired
	private ReconciliationService reconciliationService;
	@Autowired
	private FileService fileService;
	@Autowired
	private ContractRecordService contractRecordService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	@Override
	public Boolean support(Integer type) {
		return PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION
				.match(type);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void signComplete(String tableId) {
		reconciliationService.findOne(tableId).ifPresent(reconciliation -> {
			if (ReconciliationDef.State.PRE_FINISHED
					.match(reconciliation.getState())) {
				return;
			}
			// 修改状态 签署状态双方已完成
			reconciliation.setPreSignStatus(
					BusinessContractDef.CommonSignState.COMPLETED.getCode());
			// 对账状态改为已完成
			reconciliation
					.setState(ReconciliationDef.State.PRE_FINISHED.getCode());
			// 重新获取新的签署文件
			Long newFileId = contractRecordService.download(tableId,
					PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION);
			reconciliation.setPreReconciliationFileId(newFileId);
			reconciliationService.update(reconciliation);
			// 预对账完成 订单和签收单的状态还是预对账
			// 不需要更新订单和签收单 的对账状态

			Customer customer = null;
			if (ReconciliationDef.Type.SELL.match(reconciliation.getType())) {
				DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
						.findOne(reconciliation.getPurchaserBusinessId())
						.orElse(null);
				if (Objects.nonNull(dealingsEnterprise) && Objects
						.nonNull(dealingsEnterprise.getCustomerId())) {
					customer = customerService
							.findOne(dealingsEnterprise.getCustomerId())
							.orElse(null);
				}
			} else {
				DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
						.findOne(reconciliation.getSellerBusinessId())
						.orElse(null);
				if (Objects.nonNull(dealingsEnterprise) && Objects
						.nonNull(dealingsEnterprise.getCustomerId())) {
					customer = customerService
							.findOne(dealingsEnterprise.getCustomerId())
							.orElse(null);
				}
			}
			if (Objects.nonNull(customer)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(wxSubscriptionProperties
								.getPreFinishedReconciliationCode())
						.params(Map.of("settle_id", reconciliation.getId()))
						.mobile(customer.getMobile()).build());

				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.ORDER.getCode())
						.title(MessageFormat.format(
								UserMessageConstants.RECONCILIATION_PRE_FINISHED_TEMPLATE,
								reconciliation.getId()))
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.RECONCILIATION_DETAIL_PAGE)
						.detailId(String.valueOf(reconciliation.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}

			reconciliationService.notice(reconciliation, 7);
		});
	}

	@Override
	public void signReject(String tableId, String contact) {
		reconciliationService.findOne(tableId)
				.ifPresent(reconciliation -> reconciliationService
						.reject(reconciliation, Boolean.FALSE));
	}

	@Override
	public void signing(String correlationId, String name,
			String callbackType) {
		reconciliationService.findOne(correlationId)
				.ifPresent(reconciliation -> {
					// 防止契约锁回调顺序有问题的处理
					Integer completeStatus = ReconciliationDef.State.PRE_FINISHED
							.getCode();
					if (Objects.equals(completeStatus,
							reconciliation.getState())) {
						return;
					}
					Long newFileId = contractRecordService.download(
							correlationId,
							PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION);
					fileService.batchUnActive(List
							.of(reconciliation.getPreReconciliationFileId()));
					reconciliation.setPreReconciliationFileId(newFileId);

					boolean isBuyer = false;
					if (PurchaseContractDef.CallbackType.SEAL.name()
							.equals(callbackType)) {
						isBuyer = name.equals(reconciliation
								.getPurchaserEnterprise().getName());
					} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
							.equals(callbackType)) {
						isBuyer = name.equals(reconciliation
								.getPurchaserEnterprise().getRealName());
					}
					boolean isSupplier = false;
					if (PurchaseContractDef.CallbackType.SEAL.name()
							.equals(callbackType)) {
						isSupplier = name.equals(
								reconciliation.getSellerEnterprise().getName());
					} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
							.equals(callbackType)) {
						isSupplier = name.equals(reconciliation
								.getSellerEnterprise().getRealName());
					}
					if (isBuyer) {
						if (BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.match(reconciliation.getPreSignStatus())) {
							reconciliation.setPreSignStatus(
									BusinessContractDef.CommonSignState.COMPLETED
											.getCode());
						} else {
							reconciliation.setPreSignStatus(
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode());
						}
					} else if (isSupplier) {
						if (BusinessContractDef.CommonSignState.BUYER_SIGNED
								.match(reconciliation.getPreSignStatus())) {
							reconciliation.setPreSignStatus(
									BusinessContractDef.CommonSignState.COMPLETED
											.getCode());
						} else {
							reconciliation.setPreSignStatus(
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode());
						}
					}
					reconciliationService.update(reconciliation);
				});

	}

	@Override
	public void sendInvalid(String tableId, String name) {
		reconciliationService.findOne(tableId).ifPresent(reconciliation -> {
			if (ReconciliationDef.State.INVALIDING
					.match(reconciliation.getState())
					&& Objects.nonNull(reconciliation.getPreInvalidFileId())) {
				return;
			}

			// 作废后获取作废合同id
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION);
			Reconciliation reconciliation1 = new Reconciliation();
			reconciliation1.setPreInvalidFileId(fileId);
			reconciliation1
					.setState(ReconciliationDef.State.INVALIDING.getCode());
			reconciliation1.setPreInvalidSignState(
					PurchaseContractDef.CommonSignState.UNSIGNED.getCode());
			reconciliationService.updateNotNull(reconciliation1);
		});
	}

	@Override
	public void invaliding(String tableId, String name) {
		reconciliationService.findOne(tableId).ifPresent(reconciliation -> {
			// 防止契约锁回调顺序有问题的处理
			if (Objects.equals(ReconciliationDef.State.INVALID.getCode(),
					reconciliation.getState())) {
				return;
			}
			boolean isBuyer;
			boolean isSupplier;
			isBuyer = name
					.equals(reconciliation.getPurchaserEnterprise().getName())
					|| name.equals(reconciliation.getPurchaserEnterprise()
							.getRealName());

			isSupplier = name
					.equals(reconciliation.getSellerEnterprise().getName())
					|| name.equals(
							reconciliation.getSellerEnterprise().getRealName());

			if (isBuyer) {
				reconciliation.setPreInvalidSignState(
						PurchaseContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				reconciliation.setPrePurchaseInvalidTime(LocalDateTime.now());
			} else if (isSupplier) {
				reconciliation.setPreInvalidSignState(
						PurchaseContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
				reconciliation.setPreSellerInvalidTime(LocalDateTime.now());
			}

			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION);
			reconciliation.setPreInvalidFileId(fileId);
			reconciliationService.update(reconciliation);
		});
	}

	@Override
	public void invalided(String tableId, String name) {
		reconciliationService.findOne(tableId).ifPresent(reconciliation -> {
			reconciliation.setState(ReconciliationDef.State.INVALID.getCode());
			reconciliation.setPreInvalidSignState(
					PurchaseContractDef.CommonSignState.COMPLETED.getCode());
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.PRE_RECONCILIATION);
			reconciliation.setPreInvalidFileId(fileId);
			// 处理预对账作废后的作废信息
			reconciliationService.changePreRelate(reconciliation);
			reconciliationService.update(reconciliation);
		});
	}

	@Override
	public void rejectInvalid(String tableId, String name) {
		reconciliationService.findOne(tableId).ifPresent(reconciliation -> {
			reconciliation
					.setState(ReconciliationDef.State.PRE_FINISHED.getCode());
			reconciliation.setPreInvalidSignState(null);
			reconciliation.setPreInvalidRevokeTime(LocalDateTime.now());
			reconciliationService.changePreRejectInvalid(reconciliation);
			reconciliationService.updateAllProperties(reconciliation);
		});
	}
}
