package com.zhihaoscm.service.resource.validator.reconciliation;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.meta.biz.BusinessContractDef;
import com.zhihaoscm.domain.meta.biz.ContractDef;
import com.zhihaoscm.domain.meta.biz.ReconciliationDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.core.service.BillPaymentService;
import com.zhihaoscm.service.core.service.ContractService;
import com.zhihaoscm.service.core.service.ReconciliationService;
import com.zhihaoscm.service.resource.form.reconciliation.*;
import com.zhihaoscm.service.resource.validator.contract.ContractValidator;
import com.zhihaoscm.service.resource.validator.customer.CustomerValidator;
import com.zhihaoscm.service.resource.validator.project.ProjectValidator;

@Component
public class ReconciliationValidator {
	@Autowired
	private ProjectValidator projectValidator;
	@Autowired
	private ReconciliationService reconciliationService;
	@Autowired
	private BillPaymentService billPaymentService;
	@Autowired
	private CustomerValidator customerValidator;
	@Autowired
	private ContractService contractService;
	@Autowired
	private ContractValidator contractValidator;

	/**
	 * 销售对账创建校验
	 *
	 * @param form
	 */
	public Reconciliation validateCreate(ReconciliationForm form,
			Integer initiator) {
		Project project = projectValidator
				.validateExistProject(form.getProjectId());
		Contract contract = contractValidator
				.validateExist(form.getContractId());
		if (ReconciliationDef.Type.SELL.match(contract.getContractType())) {
			if (CommonDef.Symbol.YES.match(form.getIsPreReconciliation())) {
				// 存在预对账时 就是新增预对账
				if (Objects.isNull(form.getPreSignType())) {
					throw new BadRequestException(ErrorCode.CODE_30154003);
				}
				// 线上需要校验下游采购方是否授权电子签
				if (ReconciliationDef.SignType.ONLINE
						.match(form.getPreSignType())) {
					customerValidator.validateSealAdmin(
							contract.getDownstreamPurchasersId());
				}
			} else {
				// 新增对账时
				// 项目的下游不为自录数据时 注册企业签署方式不能为空
				if (!CommonDef.Symbol.YES
						.match(project.getCustomerIsRecorded())) {
					if (Objects.isNull(form.getSignType())) {
						throw new BadRequestException(ErrorCode.CODE_30154003);
					}
					// 线上需要校验下游采购方是否授权电子签
					if (ReconciliationDef.SignType.ONLINE
							.match(form.getSignType())) {
						customerValidator.validateSealAdmin(
								contract.getDownstreamPurchasersId());
					}
				}
			}
		}

		if (CommonDef.Symbol.YES.match(form.getIsPreReconciliation())) {
			// 新增预对账时
			// 预对账日期不能为空
			if (Objects.isNull(form.getPreReconciliationDate())) {
				throw new BadRequestException(ErrorCode.CODE_30154027);
			}
			// 关联合同为先货后款时需要校验订单保证金和（预）对账金额
			this.validateAmount(form.getContractId(),
					form.getDepositTransferAmount(),
					form.getPreReconciliationAmount(), form.getOrderDeposit(),
					form.getIsPreReconciliation());
		} else {

			// 新增对账
			// 对账数量不能为空
			if (Objects.isNull(form.getReconciliationWeight())) {
				throw new BadRequestException(ErrorCode.CODE_30154018);
			}
			// 对账日期不能为空
			if (Objects.isNull(form.getReconciliationDate())) {
				throw new BadRequestException(ErrorCode.CODE_30154006);
			}
			// 关联合同为先货后款时需要校验订单保证金和（预）对账金额
			this.validateAmount(form.getContractId(),
					form.getDepositTransferAmount(),
					form.getReconciliationAmount(), form.getOrderDeposit(),
					form.getIsPreReconciliation());
		}
		return form.convertEntity(project, contract, initiator,
				ReconciliationDef.Type.SELL.getCode());
	}

	/**
	 * 采购对账创建校验
	 *
	 * @param form
	 */
	public Reconciliation validateCreateBuy(ReconciliationBuyForm form,
			Integer initiator) {
		Project project = projectValidator
				.validateExistProject(form.getProjectId());
		Contract contract = contractValidator
				.validateExist(form.getContractId());
		// 只有注册企业才存在是否存在预对账
		if (CommonDef.Symbol.YES.match(form.getIsPreReconciliation())) {
			// 存在预对账时 就是新增预对账
			if (Objects.isNull(form.getPreSignType())) {
				throw new BadRequestException(ErrorCode.CODE_30154003);
			}
			// 线上需要校验下游采购方是否授权电子签
			if (ReconciliationDef.SignType.ONLINE
					.match(form.getPreSignType())) {
				customerValidator.validateSealAdmin(
						contract.getDownstreamPurchasersId());
			}
			// 存在预对账时 结算预付款不能为空
			if (Objects.isNull(form.getSettlementPayment())) {
				throw new BadRequestException(ErrorCode.CODE_30154031);
			}
			// 存在预对账时 预结算方式不能为空
			if (Objects.nonNull(form.getPreSettleWay())) {
				// 按比例 填百分比，大于等于0小于100
				if (ReconciliationDef.PreSettleWay.BY_RATIO
						.match(form.getPreSettleWay())) {
					if (Objects.nonNull(form.getPrePaymentValue())) {
						if (form.getPrePaymentValue()
								.compareTo(BigDecimal.ZERO) < 0
								&& form.getPrePaymentValue()
										.compareTo(new BigDecimal(100)) >= 0) {
							// 预付比例 应大于等于0且小于100
							throw new BadRequestException(
									ErrorCode.CODE_30154033);
						}
					}
				}
				// 按金额 大于等于0小于预对账金额
				else if (ReconciliationDef.PreSettleWay.BY_AMOUNT
						.match(form.getPreSettleWay())) {
					if (form.getPrePaymentValue().compareTo(BigDecimal.ZERO) < 0
							&& form.getPrePaymentValue().compareTo(
									form.getPreReconciliationAmount()) >= 0) {
						// 预付金额应大于等于0小于预对账金额
						throw new BadRequestException(ErrorCode.CODE_30154034);
					}
				}
			} else {
				throw new BadRequestException(ErrorCode.CODE_30154032);
			}

		} else {
			// 新增对账时
			// 项目的上游不为自录数据时 注册企业签署方式不能为空
			if (!CommonDef.Symbol.YES.match(project.getSupplierIsRecorded())) {
				if (Objects.isNull(form.getSignType())) {
					throw new BadRequestException(ErrorCode.CODE_30154003);
				}
				// 线上需要校验下游采购方是否授权电子签
				if (ReconciliationDef.SignType.ONLINE
						.match(form.getSignType())) {
					customerValidator.validateSealAdmin(
							contract.getDownstreamPurchasersId());
				}
			}
		}
		return form.convertEntity(project, contract, initiator);
	}

	/**
	 * 修改校验
	 *
	 * @param id
	 * @param form
	 */
	public Reconciliation validateUpdate(String id,
			ReconciliationUpdateForm form, Integer initiator) {
		Reconciliation reconciliation = this.validateExist(id);
		// 管理后台的操作 需要校验当前操作人是否是项目指派人员
		if (Objects.nonNull(UserContextHolder.getUser())) {
			projectValidator
					.validateProjectPeople(reconciliation.getProjectId());
		}
		// 销售类型的对账 修改时是注册企业时需要校验对账状态
		if (ReconciliationDef.Type.SELL.match(reconciliation.getType())) {
			Integer state = reconciliation.getState();
			// 注册企业时 校验草稿和已驳回状态可以修改
			if (!CommonDef.Symbol.YES.match(reconciliation.getIsRecord())) {
				if (!state.equals(ReconciliationDef.State.DRAFT.getCode())
						&& !state.equals(
								ReconciliationDef.State.REJECTED.getCode())) {
					throw new BadRequestException(ErrorCode.CODE_30154010);
				}
				if (CommonDef.Symbol.YES.match(form.getIsPreReconciliation())) {
					// 新增预对账时
					// 线上需要校验采购方是否授权电子签
					if (ReconciliationDef.SignType.ONLINE
							.match(form.getPreSignType())) {
						customerValidator.validateSealAdmin(
								reconciliation.getPurchaserId());
					}
				} else {
					// 线上需要校验采购方是否授权电子签
					if (ReconciliationDef.SignType.ONLINE
							.match(form.getSignType())) {
						customerValidator.validateSealAdmin(
								reconciliation.getPurchaserId());
					}
				}
			} else {
				// 录入企业时 已完成状态可以修改
				if (!state.equals(ReconciliationDef.State.FINISHED.getCode())) {
					throw new BadRequestException(ErrorCode.CODE_30154010);
				}
			}
		}
		// 关联的合同为先货后款时，需校验(预)对账金额是否大于订单保证金
		if (CommonDef.Symbol.YES.match(form.getIsPreReconciliation())) {
			// 更新预对账时
			// 预对账日期不能为空
			if (Objects.isNull(form.getPreReconciliationDate())) {
				throw new BadRequestException(ErrorCode.CODE_30154027);
			}
			this.validateAmount(reconciliation.getContractId(),
					form.getDepositTransferAmount(),
					form.getPreReconciliationAmount(), form.getOrderDeposit(),
					form.getIsPreReconciliation());
		} else {
			// 更新对账
			// 对账数量不能为空
			if (Objects.isNull(form.getReconciliationWeight())) {
				throw new BadRequestException(ErrorCode.CODE_30154018);
			}
			// 对账日期不能为空
			if (Objects.isNull(form.getReconciliationDate())) {
				throw new BadRequestException(ErrorCode.CODE_30154006);
			}
			this.validateAmount(reconciliation.getContractId(),
					form.getDepositTransferAmount(),
					form.getReconciliationAmount(), form.getOrderDeposit(),
					form.getIsPreReconciliation());
		}

		return form.convertEntity(reconciliation, initiator);
	}

	/**
	 * 预对账完成之后新增修改 对账校验
	 *
	 * @param id
	 * @param form
	 */
	public Reconciliation validateUpdateReconciliation(String id,
			ReconciliationPredForm form, Integer initiator) {
		Reconciliation reconciliation = this.validateExist(id);
		// 管理后台的操作 需要校验当前操作人是否是项目指派人员
		if (Objects.nonNull(UserContextHolder.getUser())) {
			projectValidator
					.validateProjectPeople(reconciliation.getProjectId());
		}
		// 新增对账时 签署方式为线下需要校验对账单据不为空
		if (ReconciliationDef.SignType.OFFLINE.match(form.getSignType())) {
			if (Objects.isNull(form.getReconciliationFileId())) {
				throw new BadRequestException(ErrorCode.CODE_30154028);
			}

		}
		return form.convertEntity(reconciliation, initiator);
	}

	/**
	 * 预对账完成之后新增修改-对账校验
	 *
	 * @param id
	 * @param form
	 */
	public Reconciliation validateUpdateBuyReconciliation(String id,
			ReconciliationBuyPredForm form, Integer initiator) {
		Reconciliation reconciliation = this.validateExist(id);
		// 管理后台的操作 需要校验当前操作人是否是项目指派人员
		if (Objects.nonNull(UserContextHolder.getUser())) {
			projectValidator
					.validateProjectPeople(reconciliation.getProjectId());
		}
		// 新增对账时 签署方式为线下需要校验对账单据不为空
		if (ReconciliationDef.SignType.OFFLINE.match(form.getSignType())) {
			if (Objects.isNull(form.getReconciliationFileId())) {
				throw new BadRequestException(ErrorCode.CODE_30154028);
			}

		}
		return form.convertEntity(reconciliation, initiator);
	}

	/**
	 * 修改采购对账校验
	 *
	 * @param id
	 * @param form
	 */
	public Reconciliation validateUpdateBuy(String id,
			ReconciliationUpdateBuyForm form, Integer initiator) {
		Reconciliation reconciliation = this.validateExist(id);
		// 管理后台的操作 需要校验当前操作人是否是项目指派人员
		if (Objects.nonNull(UserContextHolder.getUser())) {
			projectValidator
					.validateProjectPeople(reconciliation.getProjectId());
		}
		Integer state = reconciliation.getState();
		// 注册企业时 校验草稿和已驳回状态可以修改
		if (!CommonDef.Symbol.YES.match(reconciliation.getIsRecord())) {
			if (!state.equals(ReconciliationDef.State.DRAFT.getCode()) && !state
					.equals(ReconciliationDef.State.REJECTED.getCode())) {
				throw new BadRequestException(ErrorCode.CODE_30154010);
			}
			if (CommonDef.Symbol.YES.match(form.getIsPreReconciliation())) {
				// 修改预对账时
				// 线上需要校验采购方是否授权电子签
				if (ReconciliationDef.SignType.ONLINE
						.match(form.getPreSignType())) {
					customerValidator
							.validateSealAdmin(reconciliation.getPurchaserId());
				}
			} else {
				// 线上需要校验采购方是否授权电子签
				if (ReconciliationDef.SignType.ONLINE
						.match(form.getSignType())) {
					customerValidator
							.validateSealAdmin(reconciliation.getPurchaserId());
				}
			}
		}
		return form.convertEntity(reconciliation, initiator);
	}

	/**
	 * 删除校验
	 *
	 * @param id
	 */
	public Reconciliation validateDelete(String id) {
		Reconciliation reconciliation = this.validateExist(id);
		Integer state = reconciliation.getState();
		// 管理后台的操作 需要校验当前操作人是否是项目指派人员
		if (Objects.nonNull(UserContextHolder.getUser())) {
			projectValidator
					.validateProjectPeople(reconciliation.getProjectId());
		}
		// 如果是注册企业时 需要是草稿或者已驳回状态才能删除
		if (!CommonDef.Symbol.YES.match(reconciliation.getIsRecord())) {
			if (!state.equals(ReconciliationDef.State.DRAFT.getCode()) && !state
					.equals(ReconciliationDef.State.REJECTED.getCode())) {
				throw new BadRequestException(ErrorCode.CODE_30154009);
			}
		}
		// 如果是录入企业时 需要校验是否被开票关联了
		else {
			// 录入企业时 已完成状态可以删除
			if (!state.equals(ReconciliationDef.State.FINISHED.getCode())) {
				throw new BadRequestException(ErrorCode.CODE_30154009);
			}
			// 校验是否被开票关联了
			List<BillPayment> billPaymentList = billPaymentService
					.findByReconciliationIds(List.of(id));
			if (CollectionUtils.isNotEmpty(billPaymentList)) {
				throw new BadRequestException(ErrorCode.CODE_30154016);
			}
		}
		return reconciliation;
	}

	/**
	 * 驳回校验
	 *
	 * @return
	 */
	public Reconciliation validateReject(ReconciliationRejectForm form,
			Integer initiator) {
		Reconciliation reconciliation = this.validateExist(form.getId());
		Integer state = reconciliation.getState();
		// 管理后台驳回 待签署和待确认才能驳回
		if (CommonDef.AccountSource.INNER.match(initiator)) {
			// 对账阶段
			if (CommonDef.Symbol.YES
					.match(reconciliation.getIsConductReconciliation())) {
				// 销售对账
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					// 管理后台驳回 待签署和待确认才能驳回
					if ((!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.BUYER_SIGNED
									.match(reconciliation.getSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getSignStatus()))))
							&& ((Objects.equals(reconciliation.getInitiator(),
									initiator))
									|| !ReconciliationDef.State.CONFIRMING
											.match(reconciliation
													.getState()))) {
						throw new BadRequestException(ErrorCode.CODE_30154011);
					}
				} else {
					// 管理后台驳回 待签署和待确认才能驳回
					if ((!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.match(reconciliation.getSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getSignStatus()))))
							&& ((Objects.equals(reconciliation.getInitiator(),
									initiator))
									|| !ReconciliationDef.State.CONFIRMING
											.match(reconciliation
													.getState()))) {
						throw new BadRequestException(ErrorCode.CODE_30154011);
					}
				}
			} else {
				// 预对账阶段
				// 管理后台驳回 待签署和待确认才能驳回
				// 销售对账
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					if ((!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.BUYER_SIGNED
									.match(reconciliation.getPreSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getPreSignStatus()))))
							&& ((Objects.equals(
									reconciliation.getPreInitiator(),
									initiator))
									|| !ReconciliationDef.State.CONFIRMING
											.match(reconciliation
													.getState()))) {
						throw new BadRequestException(ErrorCode.CODE_30154011);
					}
				} else {
					if ((!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.match(reconciliation.getPreSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getPreSignStatus()))))
							&& ((Objects.equals(
									reconciliation.getPreInitiator(),
									initiator))
									|| !ReconciliationDef.State.CONFIRMING
											.match(reconciliation
													.getState()))) {
						throw new BadRequestException(ErrorCode.CODE_30154011);
					}
				}
			}
			// 管理后台需要校验当前操作人是否是项目指派人员
			projectValidator
					.validateProjectPeople(reconciliation.getProjectId());
		} else {
			// 对账阶段
			if (CommonDef.Symbol.YES
					.match(reconciliation.getIsConductReconciliation())) {
				// 销售对账
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					// pc端驳回 待签署和待确认才能驳回
					if ((!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.match(reconciliation.getSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getSignStatus()))))
							&& ((Objects.equals(reconciliation.getInitiator(),
									initiator))
									|| !ReconciliationDef.State.CONFIRMING
											.match(reconciliation
													.getState()))) {
						throw new BadRequestException(ErrorCode.CODE_30154011);
					}
				} else {
					// pc端驳回 待签署和待确认才能驳回
					if ((!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.BUYER_SIGNED
									.match(reconciliation.getSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getSignStatus()))))
							&& ((Objects.equals(reconciliation.getInitiator(),
									initiator))
									|| !ReconciliationDef.State.CONFIRMING
											.match(reconciliation
													.getState()))) {
						throw new BadRequestException(ErrorCode.CODE_30154011);
					}
				}
			} else {
				// 预对账阶段
				// pc端驳回 待签署和待确认才能驳回
				// 销售对账
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					if ((!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.match(reconciliation.getPreSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getPreSignStatus()))))
							&& ((Objects.equals(
									reconciliation.getPreInitiator(),
									initiator))
									|| !ReconciliationDef.State.CONFIRMING
											.match(reconciliation
													.getState()))) {
						throw new BadRequestException(ErrorCode.CODE_30154011);
					}
				} else {
					if ((!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.BUYER_SIGNED
									.match(reconciliation.getPreSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getPreSignStatus()))))
							&& ((Objects.equals(
									reconciliation.getPreInitiator(),
									initiator))
									|| !ReconciliationDef.State.CONFIRMING
											.match(reconciliation
													.getState()))) {
						throw new BadRequestException(ErrorCode.CODE_30154011);
					}
				}
			}
		}
		reconciliation.setState(ReconciliationDef.State.REJECTED.getCode());
		return form.convertToEntity(reconciliation);
	}

	/**
	 * 验证销售合同是否可确认
	 *
	 * @param id
	 */
	public Reconciliation validateConfirm(String id) {
		Reconciliation reconciliation = this.validateExist(id);
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			// 对账阶段
			// 线下才有确认
			if (!ReconciliationDef.SignType.OFFLINE
					.match(reconciliation.getSignType())) {
				throw new BadRequestException(ErrorCode.CODE_30154017);
			}
		} else {
			// 预对账阶段
			// 线下才有确认
			if (!ReconciliationDef.SignType.OFFLINE
					.match(reconciliation.getPreSignType())) {
				throw new BadRequestException(ErrorCode.CODE_30154017);
			}
		}
		// 管理后台的操作 需要校验当前操作人是否是项目指派人员
		if (Objects.nonNull(UserContextHolder.getUser())) {
			projectValidator
					.validateProjectPeople(reconciliation.getProjectId());
		}
		return reconciliation;
	}

	/**
	 * 验证销售合同是否可提交
	 *
	 * @param id
	 * @param initiator
	 * @return
	 */
	public Reconciliation validateSubmit(String id, Integer initiator,
			Long fileId) {
		Reconciliation reconciliation = this.validateExist(id);
		Integer recInitiator;
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsPreReconciliation())) {
			// 新增预对账时
			recInitiator = reconciliation.getPreInitiator();
		} else {
			recInitiator = reconciliation.getInitiator();
		}
		// 发起方是自己
		if (!recInitiator.equals(initiator)) {
			throw new BadRequestException(ErrorCode.CODE_30151028);
		}
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			// 在对账阶段提交，签署方式为线下时 对账单据不能为空
			if (ReconciliationDef.SignType.OFFLINE
					.match(reconciliation.getSignType())
					&& Objects.isNull(fileId) && Objects
							.isNull(reconciliation.getReconciliationFileId())) {
				throw new BadRequestException(ErrorCode.CODE_30154025);
			}
			reconciliation.setReconciliationFileId(fileId);
		} else {
			// 在预对账阶段提交，签署方式为线下时 预对账单据不能为空
			if (ReconciliationDef.SignType.OFFLINE
					.match(reconciliation.getPreSignType())
					&& Objects
							.isNull(reconciliation.getPreReconciliationFileId())
					&& Objects.isNull(fileId)) {
				throw new BadRequestException(ErrorCode.CODE_30154025);
			}
			reconciliation.setPreReconciliationFileId(fileId);
		}

		// 管理后台提交 草稿状态就能提交
		if (CommonDef.AccountSource.INNER.match(initiator)) {
			if (!ReconciliationDef.State.DRAFT
					.match(reconciliation.getState())) {
				throw new BadRequestException(ErrorCode.CODE_30154022);
			}
			// 管理后台需要校验当前操作人是否是项目指派人员
			projectValidator
					.validateProjectPeople(reconciliation.getProjectId());
		}
		// pc端提交 签署方式为线下并且草稿状态才能提交
		if (CommonDef.AccountSource.CUSTOM.match(initiator)) {
			if (CommonDef.Symbol.YES
					.match(reconciliation.getIsConductReconciliation())) {
				// 进入对账阶段时
				// 签署方式为线下并且草稿状态才能提交
				if (!ReconciliationDef.SignType.OFFLINE
						.match(reconciliation.getSignType())
						|| !ReconciliationDef.State.DRAFT
								.match(reconciliation.getState())) {
					throw new BadRequestException(ErrorCode.CODE_30154021);
				}
			} else {
				// 处于预对账阶段时 校验预对账数据信息
				// 签署方式为线下并且草稿状态才能提交
				if (!ReconciliationDef.SignType.OFFLINE
						.match(reconciliation.getPreSignType())
						|| !ReconciliationDef.State.DRAFT
								.match(reconciliation.getState())) {
					throw new BadRequestException(ErrorCode.CODE_30154021);
				}
			}
		}
		return reconciliation;
	}

	/**
	 * 验证是否可撤回
	 *
	 * @param id
	 * @return
	 */
	public Reconciliation validateRetract(String id, Integer initiator) {
		Reconciliation reconciliation = this.validateExist(id);
		Integer recInitiator;
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			// 新增预对账时
			recInitiator = reconciliation.getInitiator();
		} else {
			recInitiator = reconciliation.getPreInitiator();
		}
		// 管理后台的操作 需要校验当前操作人是否是项目指派人员
		if (Objects.nonNull(UserContextHolder.getUser())) {
			projectValidator
					.validateProjectPeople(reconciliation.getProjectId());
		}
		// 发起方是自己
		if (!recInitiator.equals(initiator)) {
			throw new BadRequestException(ErrorCode.CODE_30151028);
		}
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			// 对账阶段时
			// 签署方式为线上并且待发起状态才能撤回
			if (!ReconciliationDef.SignType.ONLINE
					.match(reconciliation.getSignType())
					|| !ReconciliationDef.State.TO_BE_INITIATE
							.match(reconciliation.getState())) {
				throw new BadRequestException(ErrorCode.CODE_30154023);
			}
		} else {
			// 处于预对账阶段时
			// 签署方式为线上并且待发起状态才能撤回
			if (!ReconciliationDef.SignType.ONLINE
					.match(reconciliation.getPreSignType())
					|| !ReconciliationDef.State.TO_BE_INITIATE
							.match(reconciliation.getState())) {
				throw new BadRequestException(ErrorCode.CODE_30154023);
			}
		}
		return reconciliation;
	}

	/**
	 * 签署校验
	 *
	 * @param id
	 */
	public Reconciliation validateSigning(String id, Integer initiator) {
		Reconciliation reconciliation = this.validateExist(id);
		Integer state = reconciliation.getState();
		if (CommonDef.Symbol.YES
				.match(reconciliation.getIsConductReconciliation())) {
			// 对账阶段
			// 管理后台待签署状态才能签署 待签署状态是状态为签署中 签署状态为买方已签署
			if (CommonDef.AccountSource.INNER.match(initiator)) {
				// 销售对账
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					if (state.equals(
							ReconciliationDef.State.INVALIDING.getCode())
							&& (BusinessContractDef.CommonSignState.BUYER_SIGNED
									.match(reconciliation.getInvalidSignState())
									|| (BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getInvalidSignState())))) {
						return reconciliation;
					}
					if (!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.BUYER_SIGNED
									.match(reconciliation.getSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getSignStatus())))) {
						throw new BadRequestException(ErrorCode.CODE_30154012);
					}
				} else {
					if (state.equals(
							ReconciliationDef.State.INVALIDING.getCode())
							&& (BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.match(reconciliation.getInvalidSignState())
									|| (BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getInvalidSignState())))) {
						return reconciliation;
					}
					if (!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.match(reconciliation.getSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getSignStatus())))) {
						throw new BadRequestException(ErrorCode.CODE_30154012);
					}
				}
			}
			// pc端待签署状态才能签署 待签署状态是状态为签署中 签署状态为卖方已签署
			else {
				// 销售对账
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					if (state.equals(
							ReconciliationDef.State.INVALIDING.getCode())
							&& (BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.match(reconciliation.getInvalidSignState())
									|| (BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getInvalidSignState())))) {
						return reconciliation;
					}
					if (!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.match(reconciliation.getSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getSignStatus())))) {
						throw new BadRequestException(ErrorCode.CODE_30154012);
					}
				} else {
					if (state.equals(
							ReconciliationDef.State.INVALIDING.getCode())
							&& (BusinessContractDef.CommonSignState.BUYER_SIGNED
									.match(reconciliation.getInvalidSignState())
									|| (BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getInvalidSignState())))) {
						return reconciliation;
					}
					if (!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.BUYER_SIGNED
									.match(reconciliation.getSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getSignStatus())))) {
						throw new BadRequestException(ErrorCode.CODE_30154012);
					}
				}

			}
		} else {
			// 预对账阶段
			// 管理后台待签署状态才能签署 待签署状态是状态为签署中 签署状态为买方已签署
			if (CommonDef.AccountSource.INNER.match(initiator)) {
				// 销售对账
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					if (state.equals(
							ReconciliationDef.State.INVALIDING.getCode())
							&& (BusinessContractDef.CommonSignState.BUYER_SIGNED
									.match(reconciliation
											.getPreInvalidSignState())
									|| (BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getPreInvalidSignState())))) {
						return reconciliation;
					}
					if (!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.BUYER_SIGNED
									.match(reconciliation.getPreSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getPreSignStatus())))) {
						throw new BadRequestException(ErrorCode.CODE_30154012);
					}
				} else {
					if (state.equals(
							ReconciliationDef.State.INVALIDING.getCode())
							&& (BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.match(reconciliation
											.getPreInvalidSignState())
									|| (BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getPreInvalidSignState())))) {
						return reconciliation;
					}
					if (!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.match(reconciliation.getPreSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getPreSignStatus())))) {
						throw new BadRequestException(ErrorCode.CODE_30154012);
					}
				}
			}
			// pc端待签署状态才能签署 待签署状态是状态为签署中 签署状态为卖方已签署
			else {// 销售对账
				if (ReconciliationDef.Type.SELL
						.match(reconciliation.getType())) {
					if (state.equals(
							ReconciliationDef.State.INVALIDING.getCode())
							&& (BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.match(reconciliation
											.getPreInvalidSignState())
									|| (BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getPreInvalidSignState())))) {
						return reconciliation;
					}
					if (!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.match(reconciliation.getPreSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getPreSignStatus())))) {
						throw new BadRequestException(ErrorCode.CODE_30154012);
					}
				} else {
					if (state.equals(
							ReconciliationDef.State.INVALIDING.getCode())
							&& (BusinessContractDef.CommonSignState.BUYER_SIGNED
									.match(reconciliation
											.getPreInvalidSignState())
									|| (BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getPreInvalidSignState())))) {
						return reconciliation;
					}
					if (!(state
							.equals(ReconciliationDef.State.SIGNING.getCode()))
							|| (!BusinessContractDef.CommonSignState.BUYER_SIGNED
									.match(reconciliation.getPreSignStatus())
									&& (!BusinessContractDef.CommonSignState.UNSIGNED
											.match(reconciliation
													.getPreSignStatus())))) {
						throw new BadRequestException(ErrorCode.CODE_30154012);
					}
				}
			}
		}
		return reconciliation;
	}

	/**
	 * 校验发起
	 *
	 * @param id
	 * @return
	 */
	public Reconciliation validateInitiateSigning(String id,
			Integer initiator) {
		Reconciliation reconciliation = this.validateExist(id);
		Integer state = reconciliation.getState();
		// 管理后台待发起状态 才能发起签署
		if (CommonDef.AccountSource.INNER.match(initiator)) {
			if (!(state.equals(
					ReconciliationDef.State.TO_BE_INITIATE.getCode()))) {
				throw new BadRequestException(ErrorCode.CODE_30154012);
			}
		}
		// pc端草稿状态并且签署方式为线上时才能发起签署
		if (CommonDef.AccountSource.CUSTOM.match(initiator)) {
			if (CommonDef.Symbol.YES
					.match(reconciliation.getIsConductReconciliation())) {
				// 对账阶段
				if (!ReconciliationDef.SignType.ONLINE
						.match(reconciliation.getSignType())
						|| !ReconciliationDef.State.DRAFT
								.match(reconciliation.getState())) {
					throw new BadRequestException(ErrorCode.CODE_30154013);
				}
			} else {
				// 预对账阶段
				if (!ReconciliationDef.SignType.ONLINE
						.match(reconciliation.getPreSignType())
						|| !ReconciliationDef.State.DRAFT
								.match(reconciliation.getState())) {
					throw new BadRequestException(ErrorCode.CODE_30154013);
				}
			}
		}
		return reconciliation;
	}

	/**
	 * 校验是否存在
	 *
	 * @param id
	 */
	public Reconciliation validateExist(String id) {
		Reconciliation reconciliation = reconciliationService.findOne(id)
				.orElse(null);
		if (Objects.isNull(reconciliation)) {
			throw new BadRequestException(ErrorCode.CODE_30154008);
		}
		return reconciliation;
	}

	// 关联的合同为先货后款时,对账金额需大于订单保证金,保证金是否转货款必填
	private void validateAmount(String contractId,
			Integer depositTransferAmount, BigDecimal reconciliationAmount,
			BigDecimal orderDeposit, Integer isPreReconciliation) {
		Contract contract = contractService.findOne(contractId).orElse(null);
		if (Objects.nonNull(contract)) {
			// 关联的合同为先货后款时
			if (ContractDef.SettleWay.GOODS_FIRST
					.match(contract.getSettleWay())) {
				// 是保证金转货款是 对账金额需大于订单保证金
				if (CommonDef.Symbol.YES.match(depositTransferAmount)) {
					// 对账金额需大于订单保证金
					if (reconciliationAmount.compareTo(orderDeposit) <= 0) {
						if (CommonDef.Symbol.YES.match(isPreReconciliation)) {
							throw new BadRequestException(
									ErrorCode.CODE_30154026);
						} else {
							throw new BadRequestException(
									ErrorCode.CODE_30154020);
						}
					}
				}
			}
		}
	}

	/**
	 * 校验线下发起作废
	 */
	public Reconciliation validateInvalid(String id, Integer initiator,
			Boolean flag, String reasons, Long fileId) {
		Reconciliation reconciliation = this.validateExist(id);
		// 注册企业 对账完成/预对账完成状态才能发起作废
		if (!CommonDef.Symbol.YES.match(reconciliation.getIsRecord())) {
			// 只有对账完成和预对账完成才能发起作废
			if (!ReconciliationDef.State.FINISHED
					.match(reconciliation.getState())
					&& !ReconciliationDef.State.PRE_FINISHED
							.match(reconciliation.getState())) {
				throw new BadRequestException(ErrorCode.CODE_30009046);
			}
			// 管理后台发起的
			if (CommonDef.UserType.INNER.match(initiator)) {
				// 进入对账阶段后 设置作废原因
				if (CommonDef.Symbol.YES
						.match(reconciliation.getIsConductReconciliation())) {
					reconciliation.setInvalidReason(reasons);
					reconciliation.setInvalidInitiator(
							CommonDef.UserType.INNER.getCode());
					reconciliation.setInvalidFileId(fileId);
				} else {
					// 否则设置预对账作废原因和发起方
					reconciliation.setPreInvalidReason(reasons);
					reconciliation.setPreInvalidInitiator(
							CommonDef.UserType.INNER.getCode());
					reconciliation.setPreInvalidFileId(fileId);
				}
				// 管理后台发起的需要校验当前操作人是否是项目的负责人或项目经理
				if (flag) {
					projectValidator.validateProjectPeople(
							reconciliation.getProjectId());
				}
			}
			// 客户端发起
			else {
				// 进入对账阶段后 设置作废原因
				if (CommonDef.Symbol.YES
						.match(reconciliation.getIsConductReconciliation())) {
					reconciliation.setInvalidReason(reasons);
					reconciliation.setInvalidInitiator(
							CommonDef.UserType.OUTER.getCode());
					reconciliation.setInvalidFileId(fileId);
				} else {
					// 否则设置预对账作废原因和发起方
					reconciliation.setPreInvalidReason(reasons);
					reconciliation.setPreInvalidInitiator(
							CommonDef.UserType.OUTER.getCode());
					reconciliation.setPreInvalidFileId(fileId);
				}
			}
		}
		reconciliationService.update(reconciliation);
		return reconciliation;
	}

	/**
	 * 校验作废
	 *
	 * @param id
	 * @param initiator
	 * @return
	 */
	public Reconciliation validateInvalidConfirm(String id, Integer initiator,
			String reasons) {
		Reconciliation reconciliation = this.validateExist(id);
		// 注册企业 才能确认作废
		if (!CommonDef.Symbol.YES.match(reconciliation.getIsRecord())) {
			if (CommonDef.UserType.INNER.match(initiator)) {
				projectValidator
						.validateProjectPeople(reconciliation.getProjectId());
			}
			if (StringUtils.isNotBlank(reasons)) {
				// 进入对账阶段后 设置驳回作废原因
				if (CommonDef.Symbol.YES
						.match(reconciliation.getIsConductReconciliation())) {
					reconciliation.setInvalidRevokeReason(reasons);
				} else {
					// 否则设置预对账驳回作废原因
					reconciliation.setPreInvalidRevokeReason(reasons);
				}
			}
		}
		return reconciliation;
	}
}
