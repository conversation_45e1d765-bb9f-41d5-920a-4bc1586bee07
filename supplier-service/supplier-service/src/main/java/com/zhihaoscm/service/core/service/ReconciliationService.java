package com.zhihaoscm.service.core.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.mybatis.plus.service.MpStringIdBaseService;
import com.zhihaoscm.domain.bean.ContractPageResponse;
import com.zhihaoscm.domain.bean.entity.DeliverGoods;
import com.zhihaoscm.domain.bean.entity.Order;
import com.zhihaoscm.domain.bean.entity.Reconciliation;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.service.resource.form.reconciliation.ReconciliationAssociaForm;

import jakarta.servlet.http.HttpServletResponse;

/**
 * <p>
 * 项目 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-16
 */
public interface ReconciliationService
		extends MpStringIdBaseService<Reconciliation> {

	/**
	 * 后台-采购-分页查询对账
	 *
	 * @return
	 */
	Page<ReconciliationVo> buyPaging(Integer page, Integer size, String sortKey,
			String sortOrder, String param, String goodsName, String seller,
			LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, Boolean hasAll, Long userId);

	/**
	 * 后台-销售-分页查询对账
	 *
	 * @return
	 */
	Page<ReconciliationVo> salePaging(Integer page, Integer size,
			String sortKey, String sortOrder, String param, String goodsName,
			String buyer, LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, Integer origin, Boolean hasAll, Long userId);

	/**
	 * 客户端-采购-分页查询对账
	 *
	 * @return
	 */
	Page<ReconciliationVo> customBuyPaging(Integer page, Integer size,
			String sortKey, String sortOrder, String param, String goodsName,
			String seller, LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, Long customerId, Integer origin);

	/**
	 * 客户端-销售-分页查询对账
	 *
	 * @return
	 */
	Page<ReconciliationVo> customSalePaging(Integer page, Integer size,
			String sortKey, String sortOrder, String param, String goodsName,
			String buyer, LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, Long customerId, Integer origin);

	/**
	 * 分页-根据项目id和类型查询对账列表
	 *
	 * @return
	 */
	Page<ReconciliationVo> pagingFindByProjectIdAndType(Integer page,
			Integer size, String sortKey, String sortOrder, String projectId,
			Integer type, LocalDateTime beginTime, LocalDateTime endTime,
			Long customerId, String param, String buyer, String seller,
			List<Integer> states);

	/**
	 * 根据项目id和类型查询对账列表合计
	 *
	 * @return
	 */
	Optional<BigDecimal> totalFindByProjectIdAndType(String projectId,
			Integer type, LocalDateTime beginTime, LocalDateTime endTime,
			Long customerId, String param, String buyer, String seller,
			List<Integer> states);

	/**
	 * 分页-根据订单id和类型查询对账列表
	 *
	 * @return
	 */
	Page<ReconciliationVo> pagingFindByOrderIdAndType(Integer page,
			Integer size, String sortKey, String sortOrder, String orderId,
			Integer type, LocalDateTime beginTime, LocalDateTime endTime,
			Long customerId);

	/**
	 * 根据订单id和类型查询对账列表合计
	 *
	 * @return
	 */
	Optional<BigDecimal> totalFindByOrderIdAndType(String orderId, Integer type,
			LocalDateTime beginTime, LocalDateTime endTime, Long customerId);

	/**
	 * 付款管理-关联对账单下拉列表
	 *
	 * @param projectId
	 * @param contractId
	 * @param reconciliationId
	 * @return
	 */
	Page<Reconciliation> selectorAssociated(Integer page, Integer size,
			String projectId, String contractId, String reconciliationId);

	/**
	 * 查询交易对账详情
	 *
	 * @param id
	 * @return
	 */
	Optional<ReconciliationVo> findVoById(String id);

	/**
	 * 查询采购对账详情
	 *
	 * @param id
	 * @return
	 */
	Optional<ReconciliationVo> findBuyVoById(String id);

	/**
	 * 根据订单id查询对账单
	 *
	 * @param orderIds
	 * @return
	 */
	List<Reconciliation> findByOrderIds(List<String> orderIds,
			boolean filterReconciliation);

	/**
	 * 根据签收单id查询
	 *
	 * @return
	 */
	List<Reconciliation> findBySignReceiptId(String signReceiptId);

	/**
	 * 根据项目id查询状态不为已完成的对账单
	 *
	 * @param projectId
	 * @return
	 */
	List<Reconciliation> findUnfinished(String projectId);

	/**
	 * 根据项目id、对账类型查询对账列表
	 */
	List<Reconciliation> findByProjectIdsAndType(List<String> projectIds,
			Integer type);

	/**
	 * 根据项目id、对账类型查询对账列表
	 */
	List<Reconciliation> findByProjectIdsAndType(List<String> projectIds,
			Integer type, LocalDateTime beginTime, LocalDateTime endTime);

	/**
	 * 创建对账
	 *
	 */
	Optional<Reconciliation> create(Reconciliation reconciliation,
			List<DeliverGoodsVo> deliverGoodsVoList);

	/**
	 * 创建采购对账
	 *
	 */
	Optional<Reconciliation> createBuy(Reconciliation reconciliation,
			List<DeliverGoods> deliverGoodsList,
			List<DeliverGoodsVo> deliverGoodsVoList);

	/**
	 * 根据合同id查询
	 *
	 * @param contractId
	 * @return
	 */
	List<Reconciliation> findByContractId(String contractId);

	/**
	 * 根据采购商id和状态查询
	 *
	 * @param customId
	 * @param state
	 * @return
	 */
	List<Reconciliation> findByPurchaserIdAndState(Long customId,
			Integer state);

	/**
	 * 根据合同id和类型查询
	 *
	 * @param contractId
	 * @return
	 */
	List<Reconciliation> findByContractId(String contractId,
			ReconciliationAssociaForm form, Integer type);

	/**
	 * 是否显示保证金转货款那几个字段
	 *
	 * @return
	 */
	Optional<Integer> validateDisplay(String contractId,
			List<String> signReceiptIds);

	/**
	 * 根据项目id以及状态查询 对账总金额
	 *
	 * @return
	 */
	Optional<BigDecimal> findRecAmount(String contractId, List<Integer> states,
			Integer type);

	/**
	 * 根据项目类型和状态查询
	 *
	 * @param type
	 * @param state
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	List<CustomerTransactionRankTopVo> findTopVosByTypeAndState(Integer type,
			Integer state, LocalDateTime beginTime, LocalDateTime endTime);

	/**
	 * 根据项目id查询对账单
	 *
	 * @param projectId
	 * @return
	 */
	List<Reconciliation> findByProjectId(String projectId);

	/**
	 * 根据状态查询
	 *
	 * @param projectId
	 * @param type
	 * @param state
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	List<Reconciliation> findProjectIdByState(String projectId, Integer type,
			Integer state, LocalDateTime beginTime, LocalDateTime endTime);

	/**
	 * 根据项目状态和对账类型查询
	 *
	 * @param states
	 * @param reconciliationType
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	List<GoodsStatisticsVo> findGoodsStatisticsVoByProjectStatesAndReconciliationType(
			List<Integer> states, Integer reconciliationType,
			LocalDateTime beginTime, LocalDateTime endTime);

	/**
	 * 根据项目状态和对账类型查询
	 *
	 * @param states
	 * @param reconciliationType
	 * @return
	 */
	List<GoodsStatisticsVo> findGoodsStatisticsVoByProjectStatesAndReconciliationTypeWithNoTime(
			List<Integer> states, Integer reconciliationType);

	/**
	 * 根据对账类型和货物ID集合，项目ID集合查询
	 *
	 * @param goodsIdList
	 * @param projectStates
	 * @param beginTime
	 * @param endTime
	 * @return
	 */
	List<GoodsStatisticsVo> findByReconciliationTypeAndGoodsIdsAndProjectStates(
			Integer reconciliationType, List<Long> goodsIdList,
			List<Integer> projectStates, LocalDateTime beginTime,
			LocalDateTime endTime);

	/**
	 * 根据对账类型和货物ID集合，项目ID集合查询
	 *
	 * @param goodsIdList
	 * @param projectStates
	 * @return
	 */
	List<GoodsStatisticsVo> findByReconciliationTypeAndGoodsIdsAndProjectStatesWithNoTime(
			Integer reconciliationType, List<Long> goodsIdList,
			List<Integer> projectStates);

	/**
	 * 修改对账
	 *
	 */
	Optional<Reconciliation> update(Reconciliation reconciliation,
			List<DeliverGoodsVo> deliverGoodsVoList, Integer saveType,
			Integer origin);

	/**
	 * 修改对账
	 *
	 */
	Optional<Reconciliation> updateReconciliation(Reconciliation reconciliation,
			List<DeliverGoodsVo> deliverGoodsVoList);

	/**
	 * 修改采购对账
	 *
	 */
	Optional<Reconciliation> updateBuyReconciliation(
			Reconciliation reconciliation, List<DeliverGoods> deliverGoodsList,
			List<DeliverGoodsVo> deliverGoodsVoList);

	/**
	 * 修改采购对账
	 *
	 */
	Optional<Reconciliation> updateBuy(Reconciliation reconciliation,
			List<DeliverGoods> deliverGoodsList,
			List<DeliverGoodsVo> deliverGoodsVoList, Integer saveType,
			Integer origin);

	/**
	 * 删除对账
	 *
	 */
	void delete(Reconciliation resource);

	/**
	 * 预对账作废删除预对账信息时的处理
	 *
	 */
	void changeRelate(Reconciliation resource);

	/**
	 * 对账作废删除对账信息时的处理
	 *
	 */
	void changePreRelate(Reconciliation resource);

	/**
	 * 处理对账驳回作废的数据
	 *
	 */
	void changeRejectInvalid(Reconciliation resource);

	/**
	 * 处理预对账驳回作废的数据
	 *
	 */
	void changePreRejectInvalid(Reconciliation resource);

	/**
	 * 删除采购对账
	 *
	 */
	void deleteBuy(Reconciliation resource);

	/**
	 * 确认对账
	 *
	 */
	Optional<Reconciliation> confirm(Reconciliation resource);

	/**
	 * 提交对账
	 *
	 */
	Optional<Reconciliation> submit(Reconciliation reconciliation);

	/**
	 * 撤回
	 *
	 * @param reconciliation
	 */
	Optional<Reconciliation> retract(Reconciliation reconciliation);

	/**
	 * 发起签署
	 *
	 * @param reconciliation
	 * @param origin
	 * @return
	 */
	Optional<ContractPageResponse> initiateSign(Reconciliation reconciliation,
			Integer origin, Integer initiateType);

	/**
	 * 获取签署链接
	 *
	 * @param reconciliation
	 * @param origin
	 * @return
	 */
	Optional<ContractPageResponse> signing(Reconciliation reconciliation,
			Integer origin);

	/**
	 * 线上发起作废
	 */
	Optional<ContractPageResponse> invalid(Reconciliation reconciliation,
			Integer origin);

	/**
	 * 线下发起作废
	 */
	Optional<Reconciliation> invalidOffLine(Reconciliation reconciliation,
			Integer initiator);

	/**
	 * 确认作废
	 */
	Optional<Reconciliation> confirmInvalid(Reconciliation reconciliation);

	/**
	 * 驳回作废
	 */
	Optional<Reconciliation> revertInvalid(Reconciliation reconciliation);

	/**
	 * 驳回
	 *
	 * @param reconciliation
	 * @param isRevoke
	 */
	void reject(Reconciliation reconciliation, Boolean isRevoke);

	void notice(Reconciliation resource, Integer type);

	/**
	 * 下载/预览 pdf文件
	 *
	 * @param response
	 * @param id
	 * @throws IOException
	 */
	void downloadPdf(HttpServletResponse response, String id)
			throws IOException;

	/**
	 * 对账完成后判断签收单中的对账状态是否可以改为已完成状态
	 *
	 * @return: void
	 **/
	void completedChangeSignReceiptStatus(List<String> signReceiptIds);

	/**
	 * 
	 * /** 对账完成后判断支付记录里面的费用类型是否需要变更
	 *
	 * @return: void
	 **/
	void handlePayment(Reconciliation reconciliation, List<String> orderIds);

	/**
	 * 统计供应链对账管理
	 *
	 * @param isManage
	 * @return
	 */
	Optional<ReconciliationCountVo> staticsAdminReconciliation(boolean isManage,
			boolean isSeal);

	/**
	 * 统计客户对账管理
	 *
	 * @return
	 */
	Optional<ReconciliationCountVo> staticsCustomerReconciliation(
			boolean isSeal, boolean isPermission);

	/**
	 * 对账作废后 删除应收/应付款记录
	 *
	 * @return: void
	 **/
	void handleDelAccounts(Reconciliation reconciliation);

	/**
	 * 销售对账完成后生成应收/付款记录
	 *
	 * @return: void
	 **/
	void handleAccounts(Reconciliation reconciliation, List<Order> orders,
			List<DeliverGoodsVo> deliverGoodsVoList);

	/**
	 * 采购对账完成后生成应收/付款记录
	 *
	 * @return: void
	 **/
	void handleBuyAccounts(Reconciliation reconciliation, List<Order> orders,
			List<DeliverGoodsVo> deliverGoodsVoList);
}
