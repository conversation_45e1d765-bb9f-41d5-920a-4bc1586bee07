package com.zhihaoscm.service.core.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.api.util.multipart.file.CustomMultipartFile;
import com.zhihaoscm.common.api.util.multipart.file.MultipartFileUtils;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.ContractPageResponse;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.StockCheckCountVo;
import com.zhihaoscm.domain.bean.vo.StockCheckVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.pdf.SpotCheckPdfUtil;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.StockCheckMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.AdminSealService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 库存抽检管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Slf4j
@Service
public class StockCheckServiceImpl
		extends MpStringIdBaseServiceImpl<StockCheck, StockCheckMapper>
		implements StockCheckService {
	public StockCheckServiceImpl(StockCheckMapper repository) {
		super(repository);
	}

	@Autowired
	private ProjectService projectService;
	@Autowired
	private ContractService contractService;
	@Autowired
	private StockCheckDetailService stockCheckDetailService;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private ContractRecordService contractRecordService;
	@Autowired
	private FileService fileService;
	@Autowired
	private AdminSealService adminSealService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private StockProjectService stockProjectService;
	@Autowired
	private StockContractService stockContractService;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	@Override
	public Page<StockCheckVo> paging(Integer page, Integer size, String sortKey,
			String sortOrder, String param, List<String> warehouseIds,
			List<Long> storageIds, List<Integer> types, LocalDateTime beginTime,
			LocalDateTime endTime, List<Integer> states, Boolean hasAll,
			Long userId, Integer module) {
		LambdaQueryWrapper<StockCheck> wrapper = Wrappers
				.lambdaQuery(StockCheck.class)
				.eq(StockCheck::getDel, CommonDef.Symbol.NO.getCode());
		if (Objects.nonNull(module)) {
			wrapper.eq(StockCheck::getModule, module);
		}
		// 盘点编号或项目名称
		if (Objects.nonNull(param)) {
			List<String> projectIds;
			if (StockCheckDef.Module.JXC.match(module)) {
				projectIds = projectService.findByNameLike(param).stream()
						.map(Project::getId).distinct().toList();
			} else {
				projectIds = stockProjectService.findByNameLike(param).stream()
						.map(StockProject::getId).distinct().toList();
			}
			wrapper.and(x -> x.like(StockCheck::getId, param).or().in(
					CollectionUtils.isNotEmpty(projectIds),
					StockCheck::getProjectId, projectIds));
		}
		// 所属仓库/库位
		if (CollectionUtils.isNotEmpty(warehouseIds)
				&& CollectionUtils.isNotEmpty(storageIds)) {
			wrapper.and(e -> e.in(StockCheck::getWarehouseId, warehouseIds).or()
					.in(StockCheck::getStorageId, storageIds));
		} else {
			if (CollectionUtils.isNotEmpty(warehouseIds)) {
				wrapper.in(StockCheck::getWarehouseId, warehouseIds);
			}
			if (CollectionUtils.isNotEmpty(storageIds)) {
				wrapper.in(StockCheck::getStorageId, storageIds);
			}
		}
		// 类型
		wrapper.in(CollectionUtils.isNotEmpty(types), StockCheck::getType,
				types);
		// 开始时间
		wrapper.ge(Objects.nonNull(beginTime), StockCheck::getCheckDate,
				beginTime);
		// 结束时间
		wrapper.le(Objects.nonNull(endTime), StockCheck::getCheckDate, endTime);
		// 卖方未签署
		List<Integer> toBeSignedList = List.of(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
				BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode());
		// 没有传入状态时
		if (CollectionUtils.isEmpty(states)) {
			// 除了草稿和已驳回查询发起方是自己的 其他根据状态查询
			wrapper.and(
					x -> x.in(StockCheck::getState, StockCheckDef.State.SIGNING
							.getCode(),
							StockCheckDef.State.CONFIRMING.getCode(),
							StockCheckDef.State.FINISHED.getCode(),
							StockCheckDef.State.INVALIDING.getCode(),
							StockCheckDef.State.INVALID.getCode(),
							StockCheckDef.State.TO_BE_SAMPLED.getCode())
							.or(y -> y
									.eq(StockCheck::getInitiator,
											CommonDef.AccountSource.INNER
													.getCode())
									.in(StockCheck::getState,
											StockCheckDef.State.DRAFT.getCode(),
											StockCheckDef.State.REJECTED
													.getCode(),
											StockCheckDef.State.TO_BE_INITIATE
													.getCode())));
		} else {
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 草稿状态
					if (StockCheckDef.State.DRAFT.match(state)) {
						x.or(y -> y
								.eq(StockCheck::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(StockCheck::getState,
										StockCheckDef.State.DRAFT.getCode()));
					}
					// 已驳回状态
					else if (StockCheckDef.State.REJECTED.match(state)) {
						x.or(y -> y
								.eq(StockCheck::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(StockCheck::getState,
										StockCheckDef.State.REJECTED
												.getCode()));
					}
					// 待确认状态
					else if (StockCheckDef.State.WAIT_CONFIRM.match(state)) {
						x.or(y -> y
								.eq(StockCheck::getInitiator,
										CommonDef.AccountSource.CUSTOM
												.getCode())
								.eq(StockCheck::getState,
										StockCheckDef.State.CONFIRMING
												.getCode()));
					}
					// 确认中状态
					else if (StockCheckDef.State.CONFIRMING.match(state)) {
						x.or(y -> y
								.eq(StockCheck::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(StockCheck::getState,
										StockCheckDef.State.CONFIRMING
												.getCode()));
					}
					// 签署中状态 签署中状态 并且销售方已签署
					else if (StockCheckDef.State.SIGNING.match(state)) {
						x.or(y -> y.eq(StockCheck::getSignStatus,
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode())
								.eq(StockCheck::getState,
										StockCheckDef.State.SIGNING.getCode()));
					}
					// 待签署状态 签署中状态 并且买方已签署或者双方未签署
					else if (StockCheckDef.State.TO_BE_SIGNED.match(state)) {
						x.or(y -> y
								.in(StockCheck::getSignStatus, toBeSignedList)
								.eq(StockCheck::getState,
										StockCheckDef.State.SIGNING.getCode()));
					}
					// 已完成状态
					else if (StockCheckDef.State.FINISHED.match(state)) {
						x.or(y -> y.eq(StockCheck::getState,
								StockCheckDef.State.FINISHED.getCode()));
					}
					// 待发起
					else if (StockCheckDef.State.TO_BE_INITIATE.match(state)) {
						x.or(y -> y.eq(StockCheck::getState,
								StockCheckDef.State.TO_BE_INITIATE.getCode()));
					}
					// 作废中状态
					else if (StockCheckDef.State.INVALIDING.match(state)) {
						x.or(y -> y.eq(StockCheck::getState,
								StockCheckDef.State.INVALIDING.getCode()));
					}
					// 已作废状态
					else if (StockCheckDef.State.INVALID.match(state)) {
						x.or(y -> y.eq(StockCheck::getState,
								StockCheckDef.State.INVALID.getCode()));
					}
					// 待抽检状态
					else if (StockCheckDef.State.TO_BE_SAMPLED.match(state)) {
						x.or(y -> y.eq(StockCheck::getState,
								StockCheckDef.State.TO_BE_SAMPLED.getCode()));
					}
				}
			});

		}
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (!hasAll) {
			List<String> projectIdList;
			if (StocktakingDef.Module.JXC.match(module)) {
				// 处理人是自己在的
				projectIdList = projectService.findByUserId(userId, null);
			} else {
				// 处理人是自己在的
				projectIdList = stockProjectService.findByUserId(userId, null);
			}
			if (CollectionUtils.isNotEmpty(projectIdList)) {
				wrapper.in(StockCheck::getProjectId, projectIdList);
			} else {
				return Page.of(page, size, 0);
			}
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + ", id DESC");
		} else {
			// 默认按更新时间倒序排列
			wrapper.last("ORDER BY updated_time DESC");
		}
		Page<StockCheck> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<StockCheckVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<StockCheckVo> customPaging(Integer page, Integer size,
			String sortKey, String sortOrder, String param, String name,
			List<Integer> types, LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, Long customerId, Integer origin,
			Integer module) {
		LambdaQueryWrapper<StockCheck> wrapper = Wrappers
				.lambdaQuery(StockCheck.class)
				.eq(StockCheck::getDel, CommonDef.Symbol.NO.getCode())
				.eq(StockCheck::getPurchaserId, customerId);
		if (Objects.nonNull(module)) {
			wrapper.eq(StockCheck::getModule, module);
		}
		// 盘点编号或合同名称
		if (Objects.nonNull(param)) {
			List<String> contractIds;
			if (StockCheckDef.Module.JXC.match(module)) {
				contractIds = contractService.findByNameLike(param).stream()
						.map(Contract::getId).distinct().toList();
			} else {
				contractIds = stockContractService.findByNameLike(param)
						.stream().map(StockContract::getId).distinct().toList();
			}
			wrapper.and(x -> x.like(StockCheck::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					StockCheck::getContractId, contractIds));
		}
		// 所属仓库/库位
		if (StringUtils.isNotBlank(name)) {
			wrapper.and(x -> x.like(StockCheck::getWarehouseName, name).or()
					.like(StockCheck::getStorageName, name));
		}
		// 类型
		wrapper.in(CollectionUtils.isNotEmpty(types), StockCheck::getType,
				types);
		// 开始时间
		wrapper.ge(Objects.nonNull(beginTime), StockCheck::getCheckDate,
				beginTime);
		// 结束时间
		wrapper.le(Objects.nonNull(endTime), StockCheck::getCheckDate, endTime);
		// 买方未签署
		List<Integer> toBeSignedList = List.of(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
				BusinessContractDef.CommonSignState.SUPPLY_SIGNED.getCode());
		// 只有销售项目才需要判断状态
		// 查询所有数据，则查询除了pc端草稿和pc端驳回状态的所有状态
		if (CollectionUtils.isEmpty(states)) {
			// 除了草稿和已驳回,待发起 查询发起方是自己的 其他根据状态查询
			wrapper.and(x -> x
					.in(StockCheck::getState,
							StockCheckDef.State.SIGNING.getCode(),
							StockCheckDef.State.CONFIRMING.getCode(),
							StockCheckDef.State.FINISHED.getCode(),
							StockCheckDef.State.INVALIDING.getCode(),
							StockCheckDef.State.INVALID.getCode())
					.or(y -> y.eq(StockCheck::getInitiator, origin).in(
							StockCheck::getState,
							StockCheckDef.State.DRAFT.getCode(),
							StockCheckDef.State.REJECTED.getCode())));
		} else {
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 草稿状态
					if (StockCheckDef.State.DRAFT.match(state)) {
						x.or(y -> y.eq(StockCheck::getInitiator, origin).eq(
								StockCheck::getState,
								StockCheckDef.State.DRAFT.getCode()));
					}
					// 已驳回状态
					else if (StockCheckDef.State.REJECTED.match(state)) {
						x.or(y -> y.eq(StockCheck::getInitiator, origin).eq(
								StockCheck::getState,
								StockCheckDef.State.REJECTED.getCode()));
					}
					// 待确认状态 对方发起的确认中的状态
					else if (StockCheckDef.State.WAIT_CONFIRM.match(state)) {
						x.or(y -> y
								.eq(StockCheck::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(StockCheck::getState,
										StockCheckDef.State.CONFIRMING
												.getCode()));
					}
					// 确认中状态
					else if (StockCheckDef.State.CONFIRMING.match(state)) {
						x.or(y -> y.eq(StockCheck::getInitiator, origin).eq(
								StockCheck::getState,
								StockCheckDef.State.CONFIRMING.getCode()));
					}
					// 签署中状态 签署中状态 并且买方已签署
					else if (StockCheckDef.State.SIGNING.match(state)) {
						x.or(y -> y.eq(StockCheck::getSignStatus,
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode())
								.eq(StockCheck::getState,
										StockCheckDef.State.SIGNING.getCode()));
					}
					// 待签署状态 签署中状态 并且卖方已签署或者双方未签署
					else if (StockCheckDef.State.TO_BE_SIGNED.match(state)) {
						x.or(y -> y
								.in(StockCheck::getSignStatus, toBeSignedList)
								.eq(StockCheck::getState,
										StockCheckDef.State.SIGNING.getCode()));
					}
					// 已完成状态
					else if (StockCheckDef.State.FINISHED.match(state)) {
						x.or(y -> y.eq(StockCheck::getState,
								StockCheckDef.State.FINISHED.getCode()));
					}
					// 作废中状态
					else if (StockCheckDef.State.INVALIDING.match(state)) {
						x.or(y -> y.eq(StockCheck::getState,
								StockCheckDef.State.INVALIDING.getCode()));
					}
					// 已作废状态
					else if (StockCheckDef.State.INVALID.match(state)) {
						x.or(y -> y.eq(StockCheck::getState,
								StockCheckDef.State.INVALID.getCode()));
					}
				}
			});
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + ", id DESC");
		} else {
			// 默认按更新时间倒序排列
			wrapper.last("ORDER BY updated_time DESC");
		}
		Page<StockCheck> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<StockCheckVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public List<StockCheck> findByPurchaserIdAndState(Long customId,
			Integer state, Integer module) {
		LambdaQueryWrapper<StockCheck> wrapper = Wrappers
				.lambdaQuery(StockCheck.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(customId), StockCheck::getPurchaserId,
				customId)
				.eq(Objects.nonNull(state), StockCheck::getState, state)
				.eq(Objects.nonNull(module), StockCheck::getModule, module);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<StockCheckVo> findVoById(String id) {
		return this.findOne(id).map(this::packVo);
	}

	@Override
	public List<StockCheck> findByProjectId(String projectId,
			LocalDateTime startTime, LocalDateTime endTime) {
		LambdaQueryWrapper<StockCheck> wrapper = Wrappers
				.lambdaQuery(StockCheck.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(projectId), StockCheck::getProjectId,
				projectId);
		wrapper.ge(Objects.nonNull(startTime), StockCheck::getCheckDate,
				startTime);
		wrapper.le(Objects.nonNull(endTime), StockCheck::getCheckDate, endTime);
		return repository.selectList(wrapper);
	}

	@Override
	public List<StockCheck> findUnfinished(String projectId, Integer module) {
		LambdaQueryWrapper<StockCheck> queryWrapper = Wrappers
				.lambdaQuery(StockCheck.class);
		queryWrapper.eq(StockCheck::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(StockCheck::getProjectId, projectId);
		queryWrapper.eq(Objects.nonNull(module), StockCheck::getModule, module);
		queryWrapper.ne(StockCheck::getState,
				StockCheckDef.State.FINISHED.getCode());
		return repository.selectList(queryWrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<StockCheck> create(StockCheck stockCheck,
			List<StockCheckDetail> stockCheckDetails) {
		String projectCode = "";
		if (StockCheckDef.Module.JXC.match(stockCheck.getModule())) {
			Project project = projectService.findOne(stockCheck.getProjectId())
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30152013));
			projectCode = project.getCode();
		}
		if (StockCheckDef.Module.STORAGE.match(stockCheck.getModule())) {
			StockProject stockProject = stockProjectService
					.findOne(stockCheck.getProjectId())
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30152013));
			projectCode = stockProject.getCode();
		}
		// 所属项目编号+6（固定）+6（固定）+自增数（4位）
		String id = AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				projectCode, RedisKeys.Cache.STOCK_CHECK_CODE_GENERATOR,
				AutoCodeDef.BusinessRuleCode.STOCK_CHECK.getCode(), 4,
				AutoCodeDef.DATE_TYPE.yy);
		stockCheck.setId(id);
		if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
				.isNotEmpty(stockCheckDetails)) {
			for (StockCheckDetail stockCheckDetail : stockCheckDetails) {
				// 设置抽检明细的抽检编号
				stockCheckDetail.setStockCheckId(id);
			}
			// 批量创建抽检明细记录
			stockCheckDetailService.batchCreate(stockCheckDetails);
		}

		StockCheck result = super.create(stockCheck);

		if (StockCheckDef.State.CONFIRMING.match(result.getState())
				&& StockCheckDef.SignType.OFFLINE.match(result.getSignType())) {
			this.sendNotice(result,
					wxSubscriptionProperties.getUnConfirmStockCheckCode(),
					MessageFormat.format(
							UserMessageConstants.STOCK_CHECK_UNCONFIRMED_TEMPLATE,
							result.getId()));
		}

		return Optional.of(result);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<StockCheck> update(StockCheck stockCheck,
			List<StockCheckDetail> stocktakingDetailList) {
		// 更新前抽检对应的抽检明细
		List<StockCheckDetail> stockCheckDetails = stockCheckDetailService
				.findByStockCheckIds(List.of(stockCheck.getId()));
		// 删除之前的抽检明细
		if (CollectionUtils.isNotEmpty(stockCheckDetails)) {
			for (StockCheckDetail stockCheckDetail : stockCheckDetails) {
				// 物理删除
				stockCheckDetailService.deleteTure(stockCheckDetail.getId());
			}
		}
		// 批量创建抽检明细列表
		if (CollectionUtils.isNotEmpty(stocktakingDetailList)) {
			for (StockCheckDetail stockCheckDetail : stocktakingDetailList) {
				// 设置盘点明细的盘点编号
				stockCheckDetail.setStockCheckId(stockCheck.getId());
			}
			stockCheckDetailService.batchCreate(stocktakingDetailList);
		}
		return Optional.of(super.updateAllProperties(stockCheck));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(StockCheck stockCheck,
			List<StockCheckDetail> stockCheckDetails) {
		// 删除抽检记录信息
		super.delete(stockCheck.getId());
		// 删除对应的抽检明细信息
		if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
				.isNotEmpty(stockCheckDetails)) {
			for (StockCheckDetail stockCheckDetail : stockCheckDetails) {
				stockCheckDetailService.delete(stockCheckDetail.getId());
			}
		}
	}

	@Override
	public Optional<ContractPageResponse> signing(StockCheck stockCheck,
			Integer origin) {
		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				stockCheck.getPurchaserEnterprise().getSignMobile(),
				stockCheck.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}
		return contractRecordService.sign(stockCheck.getId(),
				PurchaseContractDef.CorrelationTable.STOCK_CHECK, origin);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<ContractPageResponse> initiateSign(StockCheck resource,
			Integer origin, Integer initiateType) {
		Long fileId = this.getFileId(resource);
		if (Objects.nonNull(fileId)) {
			// 设置抽检单据文件id
			resource.setCheckFileId(fileId);
			this.updateAllProperties(resource);
		} else {
			log.info("生成pdf文件失败");
			return Optional.empty();
		}
		// 设置供应链签署人
		String name = "";
		switch (CommonDef.AccountSource.from(initiateType)) {
			case CUSTOM -> {
				name = resource.getPurchaserEnterprise().getName() + "库存抽检单";
				User user1;
				if (StockCheckDef.Module.JXC.match(resource.getModule())) {
					user1 = adminSealService
							.findByType(SignerSettings.billType.stockCheck)
							.orElse(null);
				} else {
					user1 = adminSealService
							.findByType(SignerSettings.billType.stockStockCheck)
							.orElse(null);
				}
				if (Objects.nonNull(user1)) {
					resource.setSupplierSigner(user1.getName());
					resource.setSupplierSignerId(user1.getId());
				}
			}
			case INNER -> {
				name = resource.getSellerEnterprise().getName() + "库存抽检单";
				User user = UserContextHolder.getUser();
				if (Objects.nonNull(user)) {
					resource.setSupplierSigner(user.getName());
				}
			}
		}
		// 发起合同
		Map<Long, String> customerMap = contractRecordService.draft(name,
				List.of(resource.getPurchaserId(), resource.getSellerId()),
				List.of(resource.getCheckFileId()), resource.getId(),
				PurchaseContractDef.CorrelationTable.STOCK_CHECK, null,
				resource.getModule());
		// 设置文件id
		resource.setCheckFileId(contractRecordService.download(resource.getId(),
				PurchaseContractDef.CorrelationTable.STOCK_CHECK));

		resource.getPurchaserEnterprise()
				.setSignMobile(customerMap.get(resource.getPurchaserId()));
		resource.getSellerEnterprise()
				.setSignMobile(customerMap.get(resource.getSellerId()));
		// 状态设置为签署中
		resource.setState(StocktakingDef.State.SIGNING.getCode());
		resource.setSignStatus(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode());
		this.updateAllProperties(resource);

		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				resource.getPurchaserEnterprise().getSignMobile(),
				resource.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}
		// 获取签署链接
		return contractRecordService.sign(resource.getId(),
				PurchaseContractDef.CorrelationTable.STOCK_CHECK, origin);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void reject(StockCheck stockCheck, Boolean isRevoke) {
		if (Objects.nonNull(stockCheck.getCheckFileId())) {
			fileService.batchUnActive(List.of(stockCheck.getCheckFileId()));
			if (StocktakingDef.SignType.ONLINE
					.match(stockCheck.getSignType())) {
				stockCheck.setCheckFileId(null);
			}
			if (isRevoke && StocktakingDef.SignType.ONLINE
					.match(stockCheck.getSignType())) {
				// 撤销合同
				contractRecordService.revoke(stockCheck.getId(),
						PurchaseContractDef.CorrelationTable.STOCK_CHECK);
			}
		}

		if (StockCheckDef.Module.JXC.match(stockCheck.getModule())) {
			SpringUtil.getBean(StockCheckService.class).notice(stockCheck, 2);
		} else {
			SpringUtil.getBean(StockCheckService.class).notice(stockCheck, 7);
		}

		super.updateAllProperties(stockCheck);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<StockCheck> confirm(StockCheck resource) {
		// 更新对账单状态 对账状态改为已完成
		resource.setState(SignReceiptDef.Status.FINISHED.getCode());
		StockCheck stockCheck = this.updateAllProperties(resource);
		if (StockCheckDef.Module.JXC.match(stockCheck.getModule())) {
			SpringUtil.getBean(StockCheckService.class).notice(resource, 1);
		} else {
			SpringUtil.getBean(StockCheckService.class).notice(resource, 6);
		}
		return Optional.of(stockCheck);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<ContractPageResponse> invalid(StockCheck stockCheck,
			Integer origin) {
		// 调用契约锁撤销合同接口
		contractRecordService.revoke(stockCheck.getId(),
				PurchaseContractDef.CorrelationTable.STOCK_CHECK);
		// 作废后获取作废合同id
		Long fileId = contractRecordService.detail(stockCheck.getId(),
				PurchaseContractDef.CorrelationTable.STOCK_CHECK);

		stockCheck.setInvalidFileId(fileId);
		stockCheck.setState(ReconciliationDef.State.INVALIDING.getCode());
		stockCheck.setInvalidSignState(
				PurchaseContractDef.CommonSignState.UNSIGNED.getCode());
		stockCheck.setInvalidRevokeReason(null);
		stockCheck.setInvalidRevokeTime(null);
		stockCheck.setPurchaseInvalidTime(null);
		stockCheck.setSellerInvalidTime(null);
		super.updateAllProperties(stockCheck);
		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				stockCheck.getPurchaserEnterprise().getSignMobile(),
				stockCheck.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}

		if (CommonDef.UserType.OUTER.match(stockCheck.getInvalidInitiator())) {
			if (StockCheckDef.Module.JXC.match(stockCheck.getModule())) {
				SpringUtil.getBean(StockCheckService.class).notice(stockCheck,
						4);
			} else {
				SpringUtil.getBean(StockCheckService.class).notice(stockCheck,
						9);
			}
		} else {
			this.sendNotice(stockCheck,
					wxSubscriptionProperties.getStockCheckNullifyConfirmCode(),
					MessageFormat.format(
							UserMessageConstants.STOCK_CHECK_INVALID_UNCONFIRMED_TEMPLATE,
							stockCheck.getId()));
		}

		return contractRecordService.sign(stockCheck.getId(),
				PurchaseContractDef.CorrelationTable.STOCK_CHECK,
				CertificationDef.Origin.PC.getCode());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<StockCheck> invalidOffLine(StockCheck stockCheck,
			Integer initiator) {
		// 作废合同
		stockCheck.setState(ReconciliationDef.State.INVALIDING.getCode());
		stockCheck.setInvalidInitiator(initiator);
		stockCheck.setInvalidRevokeReason(null);
		stockCheck.setInvalidRevokeTime(null);
		stockCheck.setPurchaseInvalidTime(null);
		stockCheck.setSellerInvalidTime(null);

		if (CommonDef.UserType.OUTER.match(stockCheck.getInvalidInitiator())) {
			stockCheck.setInvalidSignState(
					BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode());
			stockCheck.setPurchaseInvalidTime(LocalDateTime.now());
			if (StockCheckDef.Module.JXC.match(stockCheck.getModule())) {
				SpringUtil.getBean(StockCheckService.class).notice(stockCheck,
						4);
			} else {
				SpringUtil.getBean(StockCheckService.class).notice(stockCheck,
						9);
			}
		} else {
			stockCheck.setInvalidSignState(
					BusinessContractDef.CommonSignState.SUPPLY_SIGNED
							.getCode());
			stockCheck.setSellerInvalidTime(LocalDateTime.now());
			// 如果销售合同中的采购方是录入企业，线下发起作废之后，直接为“已作废”状态，不会给采购方发短信和站内
			// 如果是进销存的
			if (StockCheckDef.Module.JXC.match(stockCheck.getModule())) {
				Project project = projectService
						.findOne(stockCheck.getProjectId())
						.orElseThrow(() -> new BadRequestException(
								ErrorCode.CODE_30152013));
				// 销售合同中的采购方 是 项目中的下游
				if (CommonDef.Symbol.YES
						.match(project.getCustomerIsRecorded())) {
					// 作废合同
					stockCheck.setState(StockCheckDef.State.INVALID.getCode());
					stockCheck.setInvalidSignState(
							BusinessContractDef.CommonSignState.COMPLETED
									.getCode());
					return Optional.of(super.updateAllProperties(stockCheck));
				}
			}

			this.sendNotice(stockCheck,
					wxSubscriptionProperties.getStockCheckNullifyConfirmCode(),
					MessageFormat.format(
							UserMessageConstants.STOCK_CHECK_INVALID_UNCONFIRMED_TEMPLATE,
							stockCheck.getId()));
		}
		return Optional.of(super.updateAllProperties(stockCheck));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<StockCheck> confirmInvalid(StockCheck stockCheck) {
		// 确认作废合同
		stockCheck.setState(ReconciliationDef.State.INVALID.getCode());
		stockCheck.setInvalidSignState(
				BusinessContractDef.CommonSignState.COMPLETED.getCode());
		if (CommonDef.UserType.INNER.match(stockCheck.getInvalidInitiator())) {
			stockCheck.setPurchaseInvalidTime(LocalDateTime.now());
		} else {
			stockCheck.setSellerInvalidTime(LocalDateTime.now());
		}
		return Optional.of(super.updateAllProperties(stockCheck));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<StockCheck> revertInvalid(StockCheck stockCheck) {
		// 撤销作废合同
		stockCheck.setState(ReconciliationDef.State.FINISHED.getCode());
		stockCheck.setInvalidSignState(null);
		stockCheck.setInvalidRevokeTime(LocalDateTime.now());

		if (CommonDef.UserType.INNER.match(stockCheck.getInvalidInitiator())) {
			if (StockCheckDef.Module.JXC.match(stockCheck.getModule())) {
				SpringUtil.getBean(StockCheckService.class).notice(stockCheck,
						5);
			} else {
				SpringUtil.getBean(StockCheckService.class).notice(stockCheck,
						10);
			}
		} else {
			this.sendNotice(stockCheck,
					wxSubscriptionProperties.getStockCheckNullifyDismissCode(),
					MessageFormat.format(
							UserMessageConstants.STOCK_CHECK_INVALID_DISMISS_TEMPLATE,
							stockCheck.getId()));
		}

		return Optional.of(super.updateAllProperties(stockCheck));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<StockCheck> confirmCheck(StockCheck stockCheck) {
		// 更新状态为抽检完成
		stockCheck.setState(StockCheckDef.State.FINISHED.getCode());
		return Optional.of(super.updateAllProperties(stockCheck));
	}

	@Override
	public void downloadPdf(HttpServletResponse response, String id,
			Boolean flag) throws IOException {
		setExportResponseFields(response, id);
		List<StockCheck> stockChecks = new ArrayList<>();
		// 抽检信息
		StockCheck stockCheck = this.findOne(id).orElseThrow(null);
		stockChecks.add(stockCheck);
		SpotCheckPdfUtil.generateStockCheckPdf(response.getOutputStream(),
				stockChecks, null, flag);
	}

	@Override
	public void printPdf(HttpServletResponse response, List<String> ids,
			String name) throws IOException {
		if (CollectionUtils.isNotEmpty(ids)) {
			setExportResponseFields(response, ids.get(0));
			// 抽检信息
			List<StockCheck> stockChecks = super.findByIds(ids);
			SpotCheckPdfUtil.generateStockCheckPdf(response.getOutputStream(),
					stockChecks, name, true);
		}
	}

	@Override
	public Optional<StockCheck> submit(StockCheck stockCheck) {
		if (StockCheckDef.SignType.OFFLINE.match(stockCheck.getSignType())) {
			stockCheck.setState(StocktakingDef.State.CONFIRMING.getCode());
			// 如果是进销存的
			if (StockCheckDef.Module.JXC.match(stockCheck.getModule())) {
				// 如果销售合同中的采购方是录入企业，提交后，状态设置为“待抽检”
				if (Boolean.TRUE.equals(contractService
						.validateIsRecorded(stockCheck.getContractId(),
								ContractDef.ContractType.SALES.getCode())
						.orElse(null))) {
					stockCheck.setState(
							StockCheckDef.State.TO_BE_SAMPLED.getCode());
				}
			}
		} else {
			if (CommonDef.AccountSource.INNER
					.match(stockCheck.getInitiator())) {
				stockCheck.setState(
						StocktakingDef.State.TO_BE_INITIATE.getCode());
			} else {
				stockCheck.setState(StocktakingDef.State.SIGNING.getCode());
			}
		}
		return Optional.of(super.updateAllProperties(stockCheck));
	}

	@Override
	public Optional<StockCheck> retract(StockCheck stockCheck) {
		stockCheck.setState(StockCheckDef.State.DRAFT.getCode());
		return Optional.of(super.updateAllProperties(stockCheck));
	}

	@Override
	public Optional<StockCheckCountVo> staticsAdminStockCheck(boolean isManage,
			boolean isSeal) {
		StockCheckCountVo stockCheckCountVo = new StockCheckCountVo();
		stockCheckCountVo.setInvalidingCount(0L);
		stockCheckCountVo.setRejectedCount(0L);
		stockCheckCountVo.setWaitSigningCount(0L);
		stockCheckCountVo.setWaitConfirmingCount(0L);
		stockCheckCountVo.setWaitCheckCount(0L);
		List<String> projectIds = projectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		LambdaQueryWrapper<StockCheck> wrapper = Wrappers
				.lambdaQuery(StockCheck.class);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计已驳回
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(StockCheck::getState,
						StockCheckDef.State.REJECTED.getCode());
				wrapper.in(StockCheck::getProjectId, projectIds);
				wrapper.eq(StockCheck::getModule,
						StockCheckDef.Module.JXC.getCode());
				stockCheckCountVo
						.setRejectedCount(repository.selectCount(wrapper));

				// 统计作废中
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(StockCheck::getState,
						StockCheckDef.State.INVALIDING.getCode());
				wrapper.in(StockCheck::getProjectId, projectIds);
				wrapper.eq(StockCheck::getModule,
						StockCheckDef.Module.JXC.getCode());
				stockCheckCountVo
						.setInvalidingCount(repository.selectCount(wrapper));

				// 统计待抽检
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(StockCheck::getState,
						StockCheckDef.State.TO_BE_SAMPLED.getCode());
				wrapper.in(StockCheck::getProjectId, projectIds);
				wrapper.eq(StockCheck::getModule,
						StockCheckDef.Module.JXC.getCode());
				stockCheckCountVo
						.setWaitCheckCount(repository.selectCount(wrapper));
			}
		}

		if (isSeal) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计待签署
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.in(StockCheck::getSignStatus, List.of(
						BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
						BusinessContractDef.CommonSignState.BUYER_SIGNED
								.getCode()))
						.eq(StockCheck::getState,
								StockCheckDef.State.SIGNING.getCode());
				wrapper.in(StockCheck::getProjectId, projectIds);
				wrapper.eq(StockCheck::getModule,
						StockCheckDef.Module.JXC.getCode());
				stockCheckCountVo
						.setWaitCheckCount(repository.selectCount(wrapper));
			}
		}
		return Optional.of(stockCheckCountVo);
	}

	@Override
	public Optional<StockCheckCountVo> staticsCustomerStockCheck(boolean isSeal,
			boolean isPermission) {
		StockCheckCountVo stockCheckCountVo = new StockCheckCountVo();
		stockCheckCountVo.setInvalidingCount(0L);
		stockCheckCountVo.setRejectedCount(0L);
		stockCheckCountVo.setWaitSigningCount(0L);
		stockCheckCountVo.setWaitConfirmingCount(0L);
		stockCheckCountVo.setWaitCheckCount(0L);
		LambdaQueryWrapper<StockCheck> wrapper = Wrappers
				.lambdaQuery(StockCheck.class);
		Long customerId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();

		if (isPermission) {
			// 统计待确认
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(StockCheck::getPurchaserId, customerId);
			wrapper.eq(StockCheck::getInitiator,
					CommonDef.AccountSource.INNER.getCode())
					.eq(StockCheck::getState,
							StockCheckDef.State.CONFIRMING.getCode());
			stockCheckCountVo
					.setWaitConfirmingCount(repository.selectCount(wrapper));

			// 统计作废中
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(StockCheck::getPurchaserId, customerId);
			wrapper.eq(StockCheck::getState,
					StockCheckDef.State.INVALIDING.getCode());
			stockCheckCountVo
					.setInvalidingCount(repository.selectCount(wrapper));
		}

		if (isSeal) {
			// 统计待签署
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(StockCheck::getPurchaserId, customerId);
			wrapper.in(StockCheck::getSignStatus, List.of(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
					BusinessContractDef.CommonSignState.SUPPLY_SIGNED
							.getCode()))
					.eq(StockCheck::getState,
							StockCheckDef.State.SIGNING.getCode());
			stockCheckCountVo
					.setWaitSigningCount(repository.selectCount(wrapper));
		}

		return Optional.of(stockCheckCountVo);
	}

	@Override
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = "{{#success}}", type = "{{#type}}", bizNo = "{{#resource.getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#resource.getId()}}"),
			@LogRecord.KeyValuePair(key = "#projectId#", value = "{{#code}}") }, messageType = LogDef.MESSAGE_TYPE_INVENTORY, permission = "{{#permission}}")
	public void notice(StockCheck resource, Integer type) {
		// 提前定义 type -> success 的映射
		Map<Integer, String> successMap = Map.of(

				1, LogDef.STOCK_CHECK_CUSTOMER_CONFIRMED,

				2, LogDef.STOCK_CHECK_CUSTOMER_REJECTED,

				3, LogDef.STOCK_CHECK_COMPLETED,

				4, LogDef.STOCK_CHECK_INVALID_UNCONFIRMED,

				5, LogDef.STOCK_CHECK_INVALID_REJECTED,

				6, LogDef.STOCK_CHECK_CUSTOMER_CONFIRMED,

				7, LogDef.STOCK_CHECK_CUSTOMER_REJECTED,

				8, LogDef.STOCK_CHECK_COMPLETED,

				9, LogDef.STOCK_CHECK_INVALID_UNCONFIRMED,

				10, LogDef.STOCK_CHECK_INVALID_REJECTED

		);

		// 设置 success
		if (successMap.containsKey(type)) {
			LogRecordContext.putVariable("success", successMap.get(type));

			// 判断是进销存还是仓储监管
			String typeLog = type <= 5 ? LogDef.STOCK_CHECK_INFO
					: LogDef.SUPERVISION_STOCK_CHECK_INFO;
			String permissionLog = type <= 5 ? LogDef.PROJECT_DEAL
					: LogDef.S_PROJECT_DEAL;

			if (type <= 5) {
				Project project = projectService
						.findOne(resource.getProjectId())
						.orElseThrow(() -> new BadRequestException(
								ErrorCode.CODE_30152013));
				LogRecordContext.putVariable("code", project.getName());
			} else {
				StockProject stockProject = stockProjectService
						.findOne(resource.getProjectId())
						.orElseThrow(() -> new BadRequestException(
								ErrorCode.CODE_30152013));
				LogRecordContext.putVariable("code", stockProject.getName());

			}

			LogRecordContext.putVariable("type", typeLog);
			LogRecordContext.putVariable("permission", permissionLog);
		}
		log.info("抽检发送通知:{}", resource.getId());
	}

	@Override
	public Optional<StockCheckCountVo> staticsStockAdminStockCheck(
			boolean isManage) {
		StockCheckCountVo stockCheckCountVo = new StockCheckCountVo();
		stockCheckCountVo.setInvalidingCount(0L);
		stockCheckCountVo.setWaitConfirmingCount(0L);
		stockCheckCountVo.setWaitSigningCount(0L);
		stockCheckCountVo.setWaitCheckCount(0L);
		stockCheckCountVo.setRejectedCount(0L);
		List<String> projectIds = stockProjectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计已驳回
				LambdaQueryWrapper<StockCheck> wrapper = Wrappers
						.lambdaQuery(StockCheck.class);
				this.filterDeleted(wrapper);
				wrapper.eq(StockCheck::getState,
						StockCheckDef.State.REJECTED.getCode());
				wrapper.eq(StockCheck::getModule,
						StockCheckDef.Module.STORAGE.getCode());
				wrapper.in(StockCheck::getProjectId, projectIds);
				stockCheckCountVo
						.setRejectedCount(repository.selectCount(wrapper));
				wrapper.clear();

				// 统计作废中
				this.filterDeleted(wrapper);
				wrapper.eq(StockCheck::getState,
						StockCheckDef.State.INVALIDING.getCode());
				wrapper.eq(StockCheck::getModule,
						StockCheckDef.Module.STORAGE.getCode());
				wrapper.in(StockCheck::getProjectId, projectIds);
				stockCheckCountVo
						.setInvalidingCount(repository.selectCount(wrapper));
				wrapper.clear();
			}
		}
		return Optional.of(stockCheckCountVo);
	}

	@Override
	public Optional<StockCheckCountVo> staticsStockCustomerStockCheck() {
		StockCheckCountVo stockCheckCountVo = new StockCheckCountVo();
		stockCheckCountVo.setInvalidingCount(0L);
		stockCheckCountVo.setWaitConfirmingCount(0L);
		stockCheckCountVo.setWaitSigningCount(0L);
		stockCheckCountVo.setWaitCheckCount(0L);
		stockCheckCountVo.setRejectedCount(0L);
		// 统计待确认
		LambdaQueryWrapper<StockCheck> wrapper = Wrappers
				.lambdaQuery(StockCheck.class);
		this.filterDeleted(wrapper);
		wrapper.eq(StockCheck::getPurchaserId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		wrapper.eq(StockCheck::getInitiator,
				CommonDef.AccountSource.INNER.getCode())
				.eq(StockCheck::getState,
						StockCheckDef.State.CONFIRMING.getCode());
		wrapper.eq(StockCheck::getModule,
				StockCheckDef.Module.STORAGE.getCode());
		stockCheckCountVo
				.setWaitConfirmingCount(repository.selectCount(wrapper));
		wrapper.clear();

		// 统计待签署
		this.filterDeleted(wrapper);
		wrapper.eq(StockCheck::getPurchaserId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		wrapper.in(StockCheck::getSignStatus,
				List.of(BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode()))
				.eq(StockCheck::getState,
						StockCheckDef.State.SIGNING.getCode());
		wrapper.eq(StockCheck::getModule,
				StockCheckDef.Module.STORAGE.getCode());
		stockCheckCountVo.setWaitSigningCount(repository.selectCount(wrapper));
		wrapper.clear();

		// 统计作废中
		this.filterDeleted(wrapper);
		wrapper.eq(StockCheck::getPurchaserId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		wrapper.eq(StockCheck::getState,
				StockCheckDef.State.INVALIDING.getCode());
		wrapper.eq(StockCheck::getModule,
				StockCheckDef.Module.STORAGE.getCode());
		stockCheckCountVo.setInvalidingCount(repository.selectCount(wrapper));
		wrapper.clear();
		return Optional.of(stockCheckCountVo);
	}

	/**
	 * @description: 获取文件id，生成pdf文件并上传文件服务器，返回文件id
	 * @param: [resource]
	 * @return: java.lang.Long
	 **/
	private Long getFileId(StockCheck stockCheck) {
		// 生成pdf
		OutputStream outputStream = new ByteArrayOutputStream();
		List<StockCheck> stockChecks = new ArrayList<>();
		// 抽检信息
		stockChecks.add(stockCheck);
		SpotCheckPdfUtil.generateStockCheckPdf(outputStream, stockChecks, null,
				false);
		try {
			CustomMultipartFile convert = MultipartFileUtils.convert(
					outputStream, "库存抽检合同" + stockCheck.getId(),
					"库存抽检合同" + stockCheck.getId() + ".pdf", "text/plain");
			File file = fileService
					.upload(convert, "库存抽检合同" + stockCheck.getId() + ".pdf")
					.orElse(null);
			if (Objects.nonNull(file)) {
				return file.getId();
			}
		} catch (Exception e) {
			log.error("生成pdf文件失败", e);
		}
		return null;
	}

	/**
	 * 封装抽检的vo信息
	 *
	 * @param
	 * @return
	 */
	private StockCheckVo packVo(StockCheck stockCheck) {
		StockCheckVo vo = new StockCheckVo();
		// 抽检信息
		vo.setStockCheck(stockCheck);
		// 设置项目信息
		if (ObjectUtils.isNotEmpty(stockCheck.getProjectId())) {
			if (StocktakingDef.Module.JXC.match(stockCheck.getModule())) {
				projectService.findOne(stockCheck.getProjectId())
						.ifPresent(vo::setProject);
			} else if (StocktakingDef.Module.STORAGE
					.match(stockCheck.getModule())) {
				stockProjectService.findOne(stockCheck.getProjectId())
						.ifPresent(vo::setStockProject);
			}
		}
		// 设置合同信息
		if (ObjectUtils.isNotEmpty(stockCheck.getContractId())) {
			if (StocktakingDef.Module.JXC.match(stockCheck.getModule())) {
				contractService.findOne(stockCheck.getContractId())
						.ifPresent(vo::setContract);
			} else if (StocktakingDef.Module.STORAGE
					.match(stockCheck.getModule())) {
				stockContractService.findOne(stockCheck.getContractId())
						.ifPresent(vo::setStockContract);
			}
		}
		return vo;
	}

	/**
	 * 封装分页查询的抽检vo信息
	 *
	 * @param
	 * @return
	 */
	private List<StockCheckVo> packPageVos(List<StockCheck> stockCheckList) {
		List<StockCheckVo> vos = new ArrayList<>();
		// 合同信息
		List<String> contracts = stockCheckList.stream()
				.map(StockCheck::getContractId).distinct().toList();
		Map<String, Contract> contractMap = contractService.findByIds(contracts)
				.stream().collect(Collectors.toMap(Contract::getId, e -> e));
		// 仓储合同信息
		Map<String, StockContract> stockContractMap = stockContractService
				.findByIds(contracts).stream()
				.collect(Collectors.toMap(StockContract::getId, e -> e));
		// 项目信息
		List<String> projectIds = stockCheckList.stream()
				.map(StockCheck::getProjectId).distinct().toList();
		Map<String, Project> projectMap = projectService.findByIds(projectIds)
				.stream().collect(Collectors.toMap(Project::getId, e -> e));
		// 仓储项目信息
		Map<String, StockProject> stockProjectMap = stockProjectService
				.findByIds(projectIds).stream()
				.collect(Collectors.toMap(StockProject::getId, e -> e));
		for (StockCheck stockCheck : stockCheckList) {
			StockCheckVo vo = new StockCheckVo();
			// 设置抽检信息
			vo.setStockCheck(stockCheck);
			// 设置合同信息
			vo.setContract(contractMap.get(stockCheck.getContractId()));
			// 设置项目信息
			vo.setProject(projectMap.get(stockCheck.getProjectId()));
			// 设置仓储合同信息
			vo.setStockContract(
					stockContractMap.get(stockCheck.getContractId()));
			// 设置仓储项目信息
			vo.setStockProject(stockProjectMap.get(stockCheck.getProjectId()));
			vos.add(vo);
		}
		return vos;
	}

	/**
	 * 设置导出响应头
	 *
	 * @param response
	 */
	private void setExportResponseFields(HttpServletResponse response,
			String id) {
		response.setContentType("application/pdf");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode(
				String.format("库存抽检_%s_%s",
						DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
								.format(LocalDateTime.now()),
						id),
				StandardCharsets.UTF_8).replaceAll("\\+", "%20");
		response.setHeader("Content-disposition",
				"attachment;filename*=utf-8''" + fileName + ".pdf");
	}

	/**
	 * 发送短信
	 *
	 * @param stockCheck
	 * @param templateCode
	 * @param title
	 */
	private void sendNotice(StockCheck stockCheck, String templateCode,
			String title) {
		Customer customer = null;
		DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
				.findOne(stockCheck.getPurchaserBusinessId()).orElse(null);
		if (Objects.nonNull(dealingsEnterprise)
				&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
			customer = customerService
					.findOne(dealingsEnterprise.getCustomerId()).orElse(null);
		}

		if (Objects.nonNull(customer)) {
			if (StringUtils.isNotBlank(templateCode)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(templateCode)
						.params(Map.of("order_id", stockCheck.getId()))
						.mobile(customer.getMobile()).build());
			}

			if (StringUtils.isNotBlank(title)) {
				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.INVENTORY.getCode())
						.title(title)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(StockCheckDef.Module.JXC
								.match(stockCheck.getModule())
										? UserMessageConstants.STOCK_CHECK_DETAIL_PAGE
										: UserMessageConstants.SUPERVISION_STOCK_CHECK_DETAIL_PAGE)
						.detailId(String.valueOf(stockCheck.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
		}
	}
}
