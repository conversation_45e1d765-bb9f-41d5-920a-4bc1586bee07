package com.zhihaoscm.service.resource.validator.transport.order.railway;

import com.zhihaoscm.domain.exception.DynamicsBadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.domain.bean.entity.DeliverGoods;
import com.zhihaoscm.domain.bean.entity.Project;
import com.zhihaoscm.domain.bean.entity.TransportOrderRailway;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.AutoCodeDef;
import com.zhihaoscm.domain.meta.biz.DeliverGoodsDef;
import com.zhihaoscm.domain.meta.biz.TransportOrderRailwayDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.TransportOrderRailwayService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.resource.form.transport.order.railway.TransportOrderRailwayCreateForm;
import com.zhihaoscm.service.resource.form.transport.order.railway.TransportOrderRailwayUpdateForm;
import com.zhihaoscm.service.resource.validator.delivergoods.DeliverGoodsValidator;
import com.zhihaoscm.service.resource.validator.order.OrderValidator;
import com.zhihaoscm.service.resource.validator.project.ProjectValidator;

import java.util.Objects;

@Component
public class TransportOrderRailwayValidator {

	@Autowired
	private TransportOrderRailwayService service;

	@Autowired
	private DeliverGoodsValidator deliverGoodsValidator;

	@Autowired
	private OrderValidator orderValidator;

	@Autowired
	private ProjectValidator projectValidator;

	@Autowired
	private StringRedisClient redisClient;

	@Autowired
	private CustomerService customerService;

	/**
	 * 校验是否存在
	 */
	public TransportOrderRailway validatorExist(String id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30162001));
	}

	/**
	 * 校验新增
	 * 
	 * @param form
	 * @return
	 */
	public TransportOrderRailway validatorCreate(
			TransportOrderRailwayCreateForm form) {
		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(form.getDeliverGoodsId());
		orderValidator.validateExist(form.getOrderId());
		// 发货完成不能新增铁运单
		if (DeliverGoodsDef.Status.DELIVER_COMPLETE
				.match(deliverGoods.getStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30162020);
		}
		Project project = projectValidator
				.validateExistProject(deliverGoods.getProjectId());
		projectValidator.validateProjectPeople(deliverGoods.getProjectId());
		if (form.getUnloadTime().isBefore(form.getDepartureTime())) {
			throw new BadRequestException(ErrorCode.CODE_30162017);
		}

		TransportOrderRailway transportOrderRailway = form.convertToEntity();
		transportOrderRailway.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(
				redisClient, project.getCode(),
				RedisKeys.Cache.TRANSPORT_ORDER_RAILWAY,
				AutoCodeDef.BusinessRuleCode.TRANSPORT_ORDER_RAILWAY.getCode(),
				4, AutoCodeDef.DATE_TYPE.yy));
		transportOrderRailway
				.setState(TransportOrderRailwayDef.State.UNLOADED.getCode());
		transportOrderRailway.setProjectId(deliverGoods.getProjectId());
		transportOrderRailway.setContractId(deliverGoods.getContractId());
		if(Objects.nonNull(deliverGoods.getPurchaserId())) {
            customerService.findOne(deliverGoods.getPurchaserId())
                    .ifPresent(customer -> {
                        transportOrderRailway.setOwnerId(customer.getId());
                        transportOrderRailway.setOwnerName(customer.getRealName());
                    });
        }
		return transportOrderRailway;
	}

	/**
	 * 校验修改
	 * 
	 * @param id
	 * @param form
	 * @return
	 */
	public TransportOrderRailway validatorUpdate(String id,
			TransportOrderRailwayUpdateForm form, Integer createType) {
		TransportOrderRailway transportOrderRailway = this.validatorExist(id);

        if(Objects.equals(createType, transportOrderRailway.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderRailway.getCreateType()).getName());
        }

		if (TransportOrderRailwayDef.State.CANCELED
				.match(transportOrderRailway.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30162018);
		}

		if (form.getUnloadTime().isBefore(form.getDepartureTime())) {
			throw new BadRequestException(ErrorCode.CODE_30162017);
		}

		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(transportOrderRailway.getDeliverGoodsId());
		projectValidator.validateProjectPeople(deliverGoods.getProjectId());
		if (!DeliverGoodsDef.Status.DELIVER_COMPLETE
				.match(deliverGoods.getStatus())) {
			transportOrderRailway.setTransportWeight(form.getTransportWeight());
		}
		transportOrderRailway.setModel(form.getModel());
		transportOrderRailway.setCarTypeNumber(form.getCarTypeNumber());
		transportOrderRailway.setDepartureAddress(form.getDepartureAddress());
		transportOrderRailway.setUnloadAddress(form.getUnloadAddress());
		transportOrderRailway.setDepartureTime(form.getDepartureTime());
		transportOrderRailway.setUnloadTime(form.getUnloadTime());
		transportOrderRailway.setAttachments(form.getAttachments());
		return transportOrderRailway;
	}

	/**
	 * 校验删除
	 * 
	 * @param id
	 */
	public void validatorDelete(String id, Integer createType) {
		TransportOrderRailway transportOrderRailway = this.validatorExist(id);
        if(Objects.equals(createType, transportOrderRailway.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderRailway.getCreateType()).getName());
        }
		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(transportOrderRailway.getDeliverGoodsId());
		if (!DeliverGoodsDef.Status.DELIVERING
				.match(deliverGoods.getStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30162021);
		}
	}
}
