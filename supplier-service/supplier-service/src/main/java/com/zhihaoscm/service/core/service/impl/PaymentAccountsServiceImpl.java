package com.zhihaoscm.service.core.service.impl;

 
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.domain.bean.entity.Payment;
import com.zhihaoscm.domain.bean.entity.PaymentAccounts;
import com.zhihaoscm.domain.meta.biz.PaymentDef;
import com.zhihaoscm.service.core.mapper.PaymentAccountsMapper;
import com.zhihaoscm.service.core.service.PaymentAccountsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 
 * @author: xiaYZ 2025/7/10
 * @version: 1.0 
 */
@Service
public class PaymentAccountsServiceImpl
        extends MpLongIdBaseServiceImpl<PaymentAccounts, PaymentAccountsMapper>
        implements PaymentAccountsService {

    @Autowired
   private PaymentServiceImpl paymentService;

    public PaymentAccountsServiceImpl(PaymentAccountsMapper repository) {
        super(repository);
    }

    @Override
    public Map<String, List<Payment>> mapByAccountsId(Collection<String> accountsIds) {
        List<Payment> paymentList = paymentService.getRepository().selectList(new LambdaQueryWrapper<>(Payment.class).eq(Payment::getDel, CommonDef.Symbol.NO.getCode())
                .eq(Payment::getState, PaymentDef.State.COMPLETED.getCode()).in(Payment::getAccountsId, accountsIds));

        Map<String, List<Payment>> map = new HashMap<>();
        for (Payment payment : paymentList) {
            List<Payment> payments = new ArrayList<>();
            List<PaymentAccounts> list = repository.selectList(new LambdaQueryWrapper<>(PaymentAccounts.class)
                    .eq(PaymentAccounts::getPaymentId, payment.getId())
                    .eq(PaymentAccounts::getDel, CommonDef.Symbol.NO.getCode()));

            BeanUtil.copyProperties(list, payments);
            payments.forEach(p -> {
                p.setPurchaserId(payment.getPurchaserId());
                p.setSellerId(payment.getSellerId());
                p.setPurchaserEnterprise(payment.getPurchaserEnterprise());
                p.setSellerEnterprise(payment.getSellerEnterprise());
                p.setPaymentWay(payment.getPaymentWay());
                p.setPaymentDate(payment.getPaymentDate());
            });

            map.put(payment.getAccountsId(), payments);
        }

        return map;
    }

}
