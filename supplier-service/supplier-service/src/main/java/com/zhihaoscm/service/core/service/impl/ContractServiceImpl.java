package com.zhihaoscm.service.core.service.impl;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.ContractPageResponse;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.PledgeInfo;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.ContractCountVo;
import com.zhihaoscm.domain.bean.vo.ContractGoodsInfoVo;
import com.zhihaoscm.domain.bean.vo.ContractVo;
import com.zhihaoscm.domain.meta.ProjectInceptionDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.LocalDateTimeAdapter;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.ContractMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.AdminSealService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 合同 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@Service
public class ContractServiceImpl
		extends MpStringIdBaseServiceImpl<Contract, ContractMapper>
		implements ContractService {

	@Autowired
	private ProjectService projectService;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private ContractRecordService contractRecordService;
	@Autowired
	private FileService fileService;
	@Autowired
	private FinancingService financingService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private AdminSealService adminSealService;
	@Autowired
	private PaymentService paymentService;
	@Autowired
	private RepaymentService repaymentService;
	@Autowired
	private OrderService orderService;
	@Autowired
	private SignReceiptService signReceiptService;
	@Autowired
	private ReconciliationService reconciliationService;
	@Autowired
	private ProjectInceptionDetailService projectInceptionDetailService;
	@Autowired
	private PledgeService pledgeService;
	@Autowired
	private StorageInceptionInboundDetailService storageInceptionInboundDetailService;
	@Autowired
	private StorageInceptionOutboundDetailService storageInceptionOutboundDetailService;
	@Autowired
	private GoodsService goodsService;
	@Autowired
	private InboundService inboundService;
	@Autowired
	private OutboundService outboundService;
	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;
	@Autowired
	private CustomerService customerService;

	public ContractServiceImpl(ContractMapper repository) {
		super(repository);
	}

	@Override
	public Page<ContractVo> paging(Integer page, Integer size, String param,
			List<Integer> contractType, String projectName, Integer signMode,
			LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, String sortKey, String sortOrder,
			String projectId, String subjectName, Boolean hasAll, Long userId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (!hasAll) {
			// 处理人是自己在的
			List<String> projectIdList = projectService.findByUserId(userId,
					null);
			if (CollectionUtils.isNotEmpty(projectIdList)) {
				wrapper.in(Contract::getProjectId, projectIdList);
			} else {
				return Page.of(page, size, 0);
			}
		}
		// 签约主体名称
		if (StringUtils.isNotBlank(subjectName)) {
			wrapper.and(i -> i.apply(
					"JSON_EXTRACT(supplier_chain_enterprise, '$.name') LIKE CONCAT('%',{0},'%')",
					subjectName).or()
					.apply("JSON_EXTRACT(upstream_suppliers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')",
							subjectName)
					.or()
					.apply("JSON_EXTRACT(downstream_purchasers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')",
							subjectName));
		}
		wrapper.ne(Contract::getState, ContractDef.State.COMPLETED.getCode());
		wrapper.eq(Objects.nonNull(projectId), Contract::getProjectId,
				projectId);
		wrapper.and(StringUtils.isNotBlank(param),
				w -> w.like(Contract::getId, param).or().like(Contract::getName,
						param));
		wrapper.in(CollectionUtils.isNotEmpty(contractType),
				Contract::getContractType, contractType);
		if (StringUtils.isNotBlank(projectName)) {
			List<String> projectIds = projectService.findByNameLike(projectName)
					.stream().map(Project::getId).distinct().toList();
			if (CollectionUtils.isEmpty(projectIds)) {
				return Page.of(page, size, 0);
			}
			wrapper.in(CollectionUtils.isNotEmpty(projectIds),
					Contract::getProjectId, projectIds);
		}
		wrapper.eq(Objects.nonNull(signMode), Contract::getSignMode, signMode)
				.ge(Objects.nonNull(beginTime), Contract::getSignDate,
						beginTime)
				.le(Objects.nonNull(endTime), Contract::getSignDate, endTime);
		// 状态
		if (CollectionUtils.isNotEmpty(states)) {
			// 卖方未签署
			List<Integer> sellerUnsigned = List.of(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
					BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode());
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 草稿
					if (ContractDef.QueryState.DRAFT.match(state)) {
						x.or(y -> y.eq(Contract::getState,
								ContractDef.State.DRAFT.getCode()));
					}
					// 签署中，卖方已签署
					else if (ContractDef.QueryState.SIGNING.match(state)) {
						x.or(y -> y
								.eq(Contract::getState,
										ContractDef.State.SIGNING.getCode())
								.eq(Contract::getSignStatus,
										BusinessContractDef.CommonSignState.SUPPLY_SIGNED
												.getCode()));
					}
					// 确认中
					else if (ContractDef.QueryState.CONFIRMING.match(state)) {
						x.or(y -> y.eq(Contract::getState,
								ContractDef.State.PENDING_CONFIRMATION
										.getCode()));
					}
					// 已驳回
					else if (ContractDef.QueryState.REJECTED.match(state)) {
						x.or(y -> y.eq(Contract::getState,
								ContractDef.State.REJECTED.getCode()));
					}
					// 待签署，卖方未签署
					else if (ContractDef.QueryState.TO_BE_SIGNED.match(state)) {
						x.or(y -> y
								.eq(Contract::getState,
										ContractDef.State.SIGNING.getCode())
								.in(Contract::getSignStatus, sellerUnsigned));

					}
					// 待发起
					else if (ContractDef.QueryState.TO_BE_INITIATE
							.match(state)) {
						x.or(y -> y.eq(Contract::getState,
								ContractDef.State.TO_BE_INITIATE.getCode()));
					}
				}
			});
		}

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 更新日期倒序排序
			wrapper.orderByDesc(Contract::getUpdatedTime)
					.orderByDesc(Contract::getId);
		}
		Page<Contract> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<ContractVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<ContractVo> customPaging(Integer page, Integer size,
			String keyword, List<Integer> contractType, Integer signMode,
			LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, String sortKey, String sortOrder,
			Long customId, String projectName, String projectId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.ne(Contract::getState, ContractDef.State.COMPLETED.getCode());
		wrapper.and(StringUtils.isNotBlank(keyword),
				w -> w.like(Contract::getId, keyword).or()
						.like(Contract::getName, keyword));
		wrapper.eq(Objects.nonNull(signMode), Contract::getSignMode, signMode)
				.ge(Objects.nonNull(beginTime), Contract::getSignDate,
						beginTime)
				.le(Objects.nonNull(endTime), Contract::getSignDate, endTime);
		if (CollectionUtils.isEmpty(states)) {
			// 状态筛选是空的，则全部状态:待签署、待确认、签署中
			List<Integer> defaultState = List.of(
					ContractDef.State.SIGNING.getCode(),
					ContractDef.State.PENDING_CONFIRMATION.getCode());
			wrapper.in(Contract::getState, defaultState);
		} else {
			// 买方未签署
			List<Integer> purchaserUnsigned = List.of(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
					BusinessContractDef.CommonSignState.SUPPLY_SIGNED
							.getCode());
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 签署中，买方已签署
					if (ContractDef.QueryState.SIGNING.match(state)) {
						x.or(y -> y
								.eq(Contract::getState,
										ContractDef.State.SIGNING.getCode())
								.eq(Contract::getSignStatus,
										BusinessContractDef.CommonSignState.BUYER_SIGNED
												.getCode()));

					}
					// 待确认
					else if (ContractDef.QueryState.PENDING_CONFIRMATION
							.match(state)) {
						x.or(y -> y.eq(Contract::getState,
								ContractDef.State.PENDING_CONFIRMATION
										.getCode()));
					}
					// 待签署，买方未签署
					else if (ContractDef.QueryState.TO_BE_SIGNED.match(state)) {
						x.or(y -> y
								.eq(Contract::getState,
										ContractDef.State.SIGNING.getCode())
								.in(Contract::getSignStatus,
										purchaserUnsigned));
					}
				}
			});
		}
		wrapper.eq(Objects.nonNull(projectId), Contract::getProjectId,
				projectId);
		// 传了合同类型查询时
		if (CollectionUtils.isNotEmpty(contractType)) {
			if (contractType
					.contains(ContractDef.ContractType.SALES.getCode())) {
				// 如果是销售合同 查询下游采购方为客户的数据
				wrapper.and(w -> w
						.eq(Objects.nonNull(customId),
								Contract::getDownstreamPurchasersId, customId)
						.eq(Contract::getContractType,
								ContractDef.ContractType.SALES.getCode()));
			}
			if (contractType
					.contains(ContractDef.ContractType.PURCHASE.getCode())) {
				// 如果是采购合同 查询上游供应商为客户的数据
				wrapper.and(w -> w
						.eq(Objects.nonNull(customId),
								Contract::getUpstreamSuppliersId, customId)
						.eq(Contract::getContractType,
								ContractDef.ContractType.PURCHASE.getCode()));
			}
			if (contractType.contains(
					ContractDef.ContractType.LOAN_AGREEMENT.getCode())) {
				// 如果是借款合同 查询下游采购方为客户的数据
				wrapper.and(w -> w
						.eq(Objects.nonNull(customId),
								Contract::getDownstreamPurchasersId, customId)
						.eq(Contract::getContractType,
								ContractDef.ContractType.LOAN_AGREEMENT
										.getCode()));
			} else {
				// 如果是其他合同类型 查询上游或者下游为客户的数据
				Set<Integer> excludedTypes = Set.of(
						ContractDef.ContractType.SALES.getCode(),
						ContractDef.ContractType.PURCHASE.getCode(),
						ContractDef.ContractType.LOAN_AGREEMENT.getCode());
				if (contractType.stream().noneMatch(excludedTypes::contains)) {
					wrapper.and(w -> w
							.eq(Objects.nonNull(customId),
									Contract::getUpstreamSuppliersId, customId)
							.or()
							.eq(Objects.nonNull(customId),
									Contract::getDownstreamPurchasersId,
									customId)
							.eq(Contract::getContractType, contractType));
				}
			}
		}
		// 没有传合同类型的时候
		else {
			if (Objects.nonNull(customId)) {
				// 客户id不为空时查询上游或下游id为自己的数据
				wrapper.and(w -> w
						.eq(Contract::getUpstreamSuppliersId, customId).or()
						.eq(Contract::getDownstreamPurchasersId, customId));
			}
		}
		if (StringUtils.isNotBlank(projectName)) {
			List<String> projectIds = projectService.findByNameLike(projectName)
					.stream().map(Project::getId).distinct().toList();
			if (CollectionUtils.isEmpty(projectIds)) {
				return Page.of(page, size, 0);
			}
			wrapper.in(CollectionUtils.isNotEmpty(projectIds),
					Contract::getProjectId, projectIds);
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 更新日期倒序排序
			wrapper.orderByDesc(Contract::getUpdatedTime)
					.orderByDesc(Contract::getId);
		}
		Page<Contract> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<ContractVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<ContractVo> pagingDone(Integer page, Integer size,
			String keyword, List<Integer> contractType, Integer signMode,
			LocalDateTime beginTime, LocalDateTime endTime, String sortKey,
			String sortOrder, Long customId, String projectName,
			String projectId, String subjectName, Boolean hasAll, Long userId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (Objects.nonNull(userId)) {
			if (!hasAll) {
				// 处理人是自己在的
				List<String> projectIdList = projectService.findByUserId(userId,
						null);
				if (CollectionUtils.isNotEmpty(projectIdList)) {
					wrapper.in(Contract::getProjectId, projectIdList);
				} else {
					return Page.of(page, size, 0);
				}
			}
		}
		// 签约主体名称
		if (StringUtils.isNotBlank(subjectName)) {
			wrapper.and(i -> i.apply(
					"JSON_EXTRACT(supplier_chain_enterprise, '$.name') LIKE CONCAT('%',{0},'%')",
					subjectName).or()
					.apply("JSON_EXTRACT(upstream_suppliers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')",
							subjectName)
					.or()
					.apply("JSON_EXTRACT(downstream_purchasers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')",
							subjectName));
		}
		wrapper.eq(Contract::getState, ContractDef.State.COMPLETED.getCode());
		wrapper.and(StringUtils.isNotBlank(keyword),
				w -> w.like(Contract::getId, keyword).or()
						.like(Contract::getName, keyword));
		wrapper.eq(Objects.nonNull(signMode), Contract::getSignMode, signMode)
				.ge(Objects.nonNull(beginTime), Contract::getSignDate,
						beginTime)
				.le(Objects.nonNull(endTime), Contract::getSignDate, endTime)
				.eq(Objects.nonNull(projectId), Contract::getProjectId,
						projectId);
		// 传了合同类型查询时
		if (CollectionUtils.isNotEmpty(contractType)) {
			if (contractType
					.contains(ContractDef.ContractType.SALES.getCode())) {
				// 如果是销售合同 查询下游采购方为客户的数据
				wrapper.and(w -> w
						.eq(Objects.nonNull(customId),
								Contract::getDownstreamPurchasersId, customId)
						.eq(Contract::getContractType,
								ContractDef.ContractType.SALES.getCode()));
			}
			if (contractType
					.contains(ContractDef.ContractType.PURCHASE.getCode())) {
				// 如果是采购合同 查询上游供应商为客户的数据
				wrapper.and(w -> w
						.eq(Objects.nonNull(customId),
								Contract::getUpstreamSuppliersId, customId)
						.eq(Contract::getContractType,
								ContractDef.ContractType.PURCHASE.getCode()));
			}
			if (contractType.contains(
					ContractDef.ContractType.LOAN_AGREEMENT.getCode())) {
				// 如果是借款合同 查询下游采购方为客户的数据
				wrapper.and(w -> w
						.eq(Objects.nonNull(customId),
								Contract::getDownstreamPurchasersId, customId)
						.eq(Contract::getContractType,
								ContractDef.ContractType.LOAN_AGREEMENT
										.getCode()));
			} else {
				// 如果是其他合同类型 查询上游或者下游为客户的数据
				Set<Integer> excludedTypes = Set.of(
						ContractDef.ContractType.SALES.getCode(),
						ContractDef.ContractType.PURCHASE.getCode(),
						ContractDef.ContractType.LOAN_AGREEMENT.getCode());
				if (contractType.stream().noneMatch(excludedTypes::contains)) {
					wrapper.and(w -> w
							.eq(Objects.nonNull(customId),
									Contract::getUpstreamSuppliersId, customId)
							.or()
							.eq(Objects.nonNull(customId),
									Contract::getDownstreamPurchasersId,
									customId)
							.eq(Contract::getContractType, contractType));
				}
			}
		}
		// 没有传合同类型的时候
		else {
			if (Objects.nonNull(customId)) {
				// 客户id不为空时查询上游或下游id为自己的数据
				wrapper.and(w -> w
						.eq(Contract::getUpstreamSuppliersId, customId).or()
						.eq(Contract::getDownstreamPurchasersId, customId));
			}
		}
		if (StringUtils.isNotBlank(projectName)) {
			List<String> projectIds = projectService.findByNameLike(projectName)
					.stream().map(Project::getId).distinct().toList();
			if (CollectionUtils.isEmpty(projectIds)) {
				return Page.of(page, size, 0);
			}
			wrapper.in(CollectionUtils.isNotEmpty(projectIds),
					Contract::getProjectId, projectIds);
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 更新日期倒序排序
			wrapper.orderByDesc(Contract::getUpdatedTime)
					.orderByDesc(Contract::getId);
		}
		Page<Contract> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<ContractVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<ContractGoodsInfoVo> findByGoods(Integer page, Integer size,
			String goodsName, String param, String buyerName, String sortKey,
			String sortOrder, boolean hasAll, Long userId) {
		// 只有采购合同的项目
		List<String> projectIdList1 = new ArrayList<>();
		// 有销售合同的项目
		List<String> projectIdList2 = new ArrayList<>();
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (Objects.nonNull(userId)) {
			if (!hasAll) {
				// 处理人是自己在的
				List<String> projectIdList = projectService
						.findByUserIdAndIsExistStorage(userId, null,
								CommonDef.Symbol.YES.getCode());
				if (CollectionUtils.isEmpty(projectIdList)) {
					return Page.of(page, size, 0);
				}
				List<Contract> contractList = this
						.findByProjectId(projectIdList);
				if (CollectionUtils.isNotEmpty(contractList)) {
					Map<String, List<Contract>> map = contractList.stream()
							.collect(Collectors
									.groupingBy(Contract::getProjectId));
					for (List<Contract> contracts : map.values()) {
						if (contracts.stream()
								.filter(contract -> ContractDef.State.COMPLETED
										.match(contract.getState()))
								.anyMatch(
										contract -> ContractDef.ContractType.SALES
												.match(contract
														.getContractType()))) {
							projectIdList2.add(contracts.get(0).getProjectId());
						} else if (contracts.stream()
								.filter(contract -> ContractDef.State.COMPLETED
										.match(contract.getState()))
								.noneMatch(
										contract -> ContractDef.ContractType.SALES
												.match(contract
														.getContractType()))) {
							projectIdList1.add(contracts.get(0).getProjectId());
						}
					}
				}
				queryWrapper.in(Contract::getProjectId, projectIdList);
			} else {
				// 有查看所有权限时，只能查看状态为履约中且是否存在仓储选择了是的项目
				List<String> projectIdList = projectService
						.findByUserIdAndIsExistStorage(null,
								ProjectDef.State.PROCESSING.getCode(),
								CommonDef.Symbol.YES.getCode());
				if (CollectionUtils.isEmpty(projectIdList)) {
					return Page.of(page, size, 0);
				}
				List<Contract> contractList = this
						.findByProjectId(projectIdList);
				if (CollectionUtils.isNotEmpty(contractList)) {
					Map<String, List<Contract>> map = contractList.stream()
							.collect(Collectors
									.groupingBy(Contract::getProjectId));
					for (List<Contract> contracts : map.values()) {
						if (contracts.stream()
								.filter(contract -> ContractDef.State.COMPLETED
										.match(contract.getState()))
								.anyMatch(
										contract -> ContractDef.ContractType.SALES
												.match(contract
														.getContractType()))) {
							projectIdList2.add(contracts.get(0).getProjectId());
						} else if (contracts.stream()
								.filter(contract -> ContractDef.State.COMPLETED
										.match(contract.getState()))
								.noneMatch(
										contract -> ContractDef.ContractType.SALES
												.match(contract
														.getContractType()))) {
							projectIdList1.add(contracts.get(0).getProjectId());
						}
					}
				}
				queryWrapper.in(Contract::getProjectId, projectIdList);
			}
		}
		// 货物名称
		if (Objects.nonNull(goodsName)) {
			queryWrapper.like(Contract::getGoodsName, goodsName);
		}
		// 项目名称或合同名称
		if (StringUtils.isNotBlank(param)) {
			// 是否存在仓储为是的项目
			List<String> projectIds = projectService.findByNameLike(param)
					.stream()
					.filter(project -> CommonDef.Symbol.YES
							.match(project.getIsExistStorage()))
					.map(Project::getId).distinct().toList();
			// 根据合同名称查询时要过滤出销售合同
			List<String> contractIds = this.findByNameLike(param).stream()
					.filter(contract -> ContractDef.ContractType.SALES
							.match(contract.getContractType()))
					.map(Contract::getId).distinct().toList();
			queryWrapper.and(i -> i
					.in(CollectionUtils.isNotEmpty(projectIds),
							Contract::getProjectId, projectIds)
					.or()
					.in(com.baomidou.mybatisplus.core.toolkit.CollectionUtils
							.isNotEmpty(contractIds), Contract::getId,
							contractIds));
		}
		// 采购方名称
		if (StringUtils.isNotBlank(buyerName)) {
			// 根据采购方名称查询时需要加上合同类型为销售合同
			queryWrapper.eq(Contract::getContractType,
					ContractDef.ContractType.SALES.getCode());
			queryWrapper.and(i -> i.apply(StringUtils.isNoneBlank(buyerName),
					"(JSON_EXTRACT(downstream_purchasers_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
					buyerName));
		}
		// 采购合同或者 销售合同
		queryWrapper.in(Contract::getContractType,
				List.of(ContractDef.ContractType.SALES.getCode(),
						ContractDef.ContractType.PURCHASE.getCode()));
		// 已完成状态
		queryWrapper.eq(Contract::getState,
				ContractDef.State.COMPLETED.getCode());

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 按项目创建时间时间倒序，项目相同按合同创建时间倒序；
			queryWrapper.last("order by project_id desc, created_time desc");
		}

		List<Contract> contractList = repository.selectList(queryWrapper);
		// 要过滤的合同
		List<String> contractIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(contractList)) {
			if (CollectionUtils.isNotEmpty(projectIdList1)) {
				for (String projectId : projectIdList1) {
					contractIds.addAll(contractList.stream()
							.filter(contract -> projectId
									.equals(contract.getProjectId()))
							.filter(contract -> ContractDef.ContractType.PURCHASE
									.match(contract.getContractType()))
							.skip(1).map(Contract::getId).toList());

				}
			}

			if (CollectionUtils.isNotEmpty(projectIdList2)) {
				for (String projectId : projectIdList2) {
					contractIds.addAll(contractList.stream()
							.filter(contract -> projectId
									.equals(contract.getProjectId()))
							.filter(contract -> ContractDef.ContractType.PURCHASE
									.match(contract.getContractType()))
							.map(Contract::getId).toList());
				}
			}
		}

		if (CollectionUtils.isNotEmpty(contractIds)) {
			queryWrapper.notIn(Contract::getId, contractIds);
		}

		Page<Contract> infoPage = repository.selectPage(new Page<>(page, size),
				queryWrapper);

		return PageUtil.getRecordsInfoPage(infoPage,
				this.packPageGoodsInfoVos(infoPage.getRecords()));

	}

	@Override
	public Page<ContractGoodsInfoVo> findCustomByGoods(Integer page,
			Integer size, String goodsName, String param, String sellerName,
			String sortKey, String sortOrder, Long customerId) {
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		// 只能查看状态为履约中且是否存在仓储选择了是的项目
		List<String> projectIdList = projectService
				.findByUserIdAndIsExistStorage(null,
						ProjectDef.State.PROCESSING.getCode(),
						CommonDef.Symbol.YES.getCode());
		if (org.apache.commons.collections4.CollectionUtils
				.isNotEmpty(projectIdList)) {
			queryWrapper.in(Contract::getProjectId, projectIdList);
		} else {
			return Page.of(page, size, 0);
		}
		// 采购方id为自己的
		queryWrapper.eq(Objects.nonNull(customerId),
				Contract::getDownstreamPurchasersId, customerId);
		// 货物名称
		queryWrapper.like(Objects.nonNull(goodsName), Contract::getGoodsName,
				goodsName);
		// 合同名称
		queryWrapper.like(StringUtils.isNotBlank(param), Contract::getName,
				param);
		// 销售方名称
		if (StringUtils.isNotBlank(sellerName)) {
			queryWrapper.and(i -> i.apply(StringUtils.isNoneBlank(sellerName),
					"(JSON_EXTRACT(supplier_chain_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
					sellerName));
		}
		// 客户端只有销售合同
		queryWrapper.in(Contract::getContractType,
				List.of(ContractDef.ContractType.SALES.getCode()));
		// 已完成状态
		queryWrapper.eq(Contract::getState,
				ContractDef.State.COMPLETED.getCode());

		if (com.zhihaoscm.common.util.utils.StringUtils.isNoneBlank(sortKey,
				sortOrder)) {
			queryWrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 按项目创建时间时间倒序，项目相同按合同创建时间倒序；
			queryWrapper.last("order by project_id desc, created_time desc");
		}
		Page<Contract> infoPage = repository.selectPage(new Page<>(page, size),
				queryWrapper);

		return PageUtil.getRecordsInfoPage(infoPage,
				this.packPageGoodsInfoVos(infoPage.getRecords()));

	}

	@Override
	public Page<Contract> selector(Integer page, Integer size, String projectId,
			String name, List<Integer> contractTypes, Long customId,
			Integer origin, String neId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		if (CommonDef.AccountSource.INNER.match(origin)) {
			if (Objects.isNull(projectId)) {
				return new Page<>();
			}
			wrapper.eq(Contract::getProjectId, projectId)
					.ne(StringUtils.isNotBlank(neId), Contract::getId, neId);
			if (CollectionUtils.isNotEmpty(contractTypes)) {
				wrapper.in(Contract::getContractType, contractTypes);
			}
		} else {
			List<DealingsEnterprise> dealingsEnterprises = dealingsEnterpriseService
					.findByCustomerIdAndEnterpriseType(customId,
							DealingsEnterpriseDef.EnterpriseType.REGISTERED_BUSINESS
									.getCode());
			List<Long> dealingsEnterpriseIds = dealingsEnterprises.stream()
					.map(DealingsEnterprise::getId).toList();
			// 客户端下拉
			if (Objects.isNull(customId)) {
				return new Page<>();
			}
			// 客户端查询
			if (contractTypes
					.contains(ContractDef.ContractType.SALES.getCode())) {
				// 关联采购方为自己的采购合同
				wrapper.eq(Contract::getDownstreamPurchasersId, customId).eq(
						Contract::getContractType,
						ContractDef.ContractType.SALES.getCode());
				if (CollectionUtils.isNotEmpty(dealingsEnterprises)) {
					// 下游采购商id+state，履约中的项目
					List<String> projectIds = projectService
							.findByCustomerIdsAndState(dealingsEnterpriseIds,
									ProjectDef.State.PROCESSING.getCode())
							.stream().map(Project::getId).toList();
					wrapper.in(CollectionUtils.isNotEmpty(projectIds),
							Contract::getProjectId, projectIds);
				}
			} else if (contractTypes
					.contains(ContractDef.ContractType.PURCHASE.getCode())) {
				// 关联销售方为自己的销售合同
				wrapper.eq(Contract::getUpstreamSuppliersId, customId).eq(
						Contract::getContractType,
						ContractDef.ContractType.PURCHASE.getCode());
				if (CollectionUtils.isNotEmpty(dealingsEnterprises)) {
					// 上游供应商id+state，履约中的项目
					List<String> projectIds = projectService
							.findBySupplierIdsAndState(dealingsEnterpriseIds,
									ProjectDef.State.PROCESSING.getCode())
							.stream().map(Project::getId).toList();
					wrapper.in(CollectionUtils.isNotEmpty(projectIds),
							Contract::getProjectId, projectIds);
				}
			}
		}
		wrapper.like(StringUtils.isNotBlank(name), Contract::getName, name)
				.eq(Contract::getState, ContractDef.State.COMPLETED.getCode())
				.orderByDesc(Contract::getUpdatedTime);
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Page<Contract> selectorInception(Integer page, Integer size,
			String projectId, String name, Integer contractType,
			List<String> neIds) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		if (CollectionUtils.isNotEmpty(neIds)) {
			wrapper.notIn(Contract::getId, neIds);
		}
		if (Objects.nonNull(contractType)) {
			wrapper.eq(Contract::getContractType, contractType);
		}
		wrapper.eq(Objects.nonNull(projectId), Contract::getProjectId,
				projectId);
		// 状态为签署完成的
		wrapper.like(StringUtils.isNotBlank(name), Contract::getName, name)
				.eq(Contract::getState, ContractDef.State.COMPLETED.getCode())
				.orderByDesc(Contract::getUpdatedTime);
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Page<Contract> selectorRefund(Integer page, Integer size,
			String projectId, String name, Long customId, Integer origin,
			List<String> neIds) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		if (CommonDef.AccountSource.INNER.match(origin)) {
			// 供应链端：如果选择的项目上游是自录数据，可以选择签署完成的采购合同和销售合同；如果选择的项目上游不是自录数据，可以选择签署完成的销售合同
			if (Objects.isNull(projectId)) {
				return new Page<>();
			}
			Project project = projectService.findOne(projectId).orElse(null);
			if (Objects.nonNull(project)) {
				if (CommonDef.Symbol.YES
						.match(project.getSupplierIsRecorded())) {
					// 签署完成的采购和销售合同
					wrapper.in(Contract::getContractType,
							List.of(ContractDef.ContractType.PURCHASE.getCode(),
									ContractDef.ContractType.SALES.getCode()));
				} else {
					// 签署完成的销售合同
					wrapper.eq(Contract::getContractType,
							ContractDef.ContractType.SALES.getCode());
				}
			} else {
				return new Page<>();
			}
			wrapper.eq(Contract::getProjectId, projectId).notIn(
					CollectionUtils.isNotEmpty(neIds), Contract::getId, neIds);
		} else {
			if (Objects.isNull(customId)) {
				return new Page<>();
			}
			// 客户端：选择签署完成的销售合同(供应链的采购合同)
			List<DealingsEnterprise> dealingsEnterprises = dealingsEnterpriseService
					.findByCustomerIdAndEnterpriseType(customId,
							DealingsEnterpriseDef.EnterpriseType.REGISTERED_BUSINESS
									.getCode());
			List<Long> dealingsEnterpriseIds = dealingsEnterprises.stream()
					.map(DealingsEnterprise::getId).toList();
			// 销售方为自己的销售合同(供应链的采购合同)
			wrapper.eq(Contract::getUpstreamSuppliersId, customId).eq(
					Contract::getContractType,
					ContractDef.ContractType.PURCHASE.getCode());
			if (CollectionUtils.isNotEmpty(dealingsEnterprises)) {
				// 上游供应商id+state，履约中的项目
				List<String> projectIds = projectService
						.findBySupplierIdsAndState(dealingsEnterpriseIds,
								ProjectDef.State.PROCESSING.getCode())
						.stream().map(Project::getId).toList();
				wrapper.in(CollectionUtils.isNotEmpty(projectIds),
						Contract::getProjectId, projectIds);
			}
		}
		wrapper.like(StringUtils.isNotBlank(name), Contract::getName, name)
				.eq(Contract::getState, ContractDef.State.COMPLETED.getCode())
				.orderByDesc(Contract::getUpdatedTime);
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Page<Contract> selectorByPurchasersId(Integer page, Integer size,
			Integer contractType, Long downstreamPurchasersId, String projectId,
			String name) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(contractType), Contract::getContractType,
				contractType);
		wrapper.eq(Objects.nonNull(projectId), Contract::getProjectId,
				projectId);
		wrapper.like(StringUtils.isNotBlank(name), Contract::getName, name);
		wrapper.eq(Objects.nonNull(downstreamPurchasersId),
				Contract::getDownstreamPurchasersId, downstreamPurchasersId);
		wrapper.eq(Contract::getState, ContractDef.State.COMPLETED.getCode())
				.orderByDesc(Contract::getUpdatedTime);
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Page<Contract> selectorPledge(Integer page, Integer size,
			String name, String projectId) {
		List<String> contractIds = pledgeService.findContractIds().stream()
				.map(Pledge::getContractId).distinct().toList();
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(projectId), Contract::getProjectId,
				projectId);
		wrapper.eq(Contract::getContractType,
				ContractDef.ContractType.SALES.getCode());
		wrapper.like(StringUtils.isNotBlank(name), Contract::getName, name);
		wrapper.notIn(CollectionUtils.isNotEmpty(contractIds), Contract::getId,
				contractIds);
		wrapper.eq(Contract::getState, ContractDef.State.COMPLETED.getCode())
				.orderByDesc(Contract::getUpdatedTime);
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Optional<ContractVo> findVoById(String id, Long customId) {
		return this.findOne(id)
				.map(contract -> this.packVo(contract, customId));
	}

	@Override
	public List<Contract> findByNameLike(String name) {
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.like(StringUtils.isNotBlank(name), Contract::getName,
				name);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Contract> findByRelatedContractId(String id) {
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		if (Objects.nonNull(id)) {
			queryWrapper.and(wrapper -> wrapper.apply(
					"JSON_CONTAINS(relate_contract_ids, JSON_QUOTE({0})) ",
					id));
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Contract> findByProjectId(List<String> projectId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.in(Contract::getProjectId, projectId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Contract> findFinishedByProjectId(String projectId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Contract::getProjectId, projectId);
		wrapper.eq(Contract::getState, ContractDef.State.COMPLETED.getCode());
		return repository.selectList(wrapper);
	}

	@Override
	public List<Contract> findUnfinished(String projectId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Contract::getProjectId, projectId);
		wrapper.ne(Contract::getState, ContractDef.State.COMPLETED.getCode());
		return repository.selectList(wrapper);
	}

	@Override
	public List<ContractVo> findByProjectIdAndType(String projectId,
			Integer type) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Contract::getProjectId, projectId);
		wrapper.eq(Contract::getContractType, type);
		return this.packContractVos(repository.selectList(wrapper));
	}

	@Override
	public List<Contract> findByType(Integer type) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Contract::getContractType, type);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Contract> findByPurchaserIdAndState(Long customId,
			Integer state) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(customId),
				Contract::getDownstreamPurchasersId, customId)
				.eq(Objects.nonNull(state), Contract::getState, state);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Contract> find(LocalDateTime startTime, LocalDateTime endTime) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.ge(Objects.nonNull(startTime), Contract::getFinishDate,
				startTime)
				.lt(Objects.nonNull(endTime), Contract::getFinishDate, endTime);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Contract> customerFind(String purchaserName, String goodsName,
			String contractName, Integer type, Long purchaserId) {
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.like(Objects.nonNull(contractName), Contract::getName,
				contractName);
		queryWrapper.like(Objects.nonNull(goodsName), Contract::getGoodsName,
				goodsName);
		queryWrapper.eq(Objects.nonNull(type), Contract::getContractType, type);
		queryWrapper.eq(Objects.nonNull(purchaserId),
				Contract::getDownstreamPurchasersId, purchaserId);
		if (Objects.nonNull(purchaserName)) {
			queryWrapper.and(i -> i.apply(
					StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(upstream_suppliers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName));
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Contract> find(String purchaserName, String goodsName,
			String contractName, Integer type, Long purchaserId) {
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.like(Objects.nonNull(contractName), Contract::getName,
				contractName);
		queryWrapper.like(Objects.nonNull(goodsName), Contract::getGoodsName,
				goodsName);
		queryWrapper.eq(Objects.nonNull(type), Contract::getContractType, type);
		queryWrapper.eq(Objects.nonNull(purchaserId),
				Contract::getDownstreamPurchasersId, purchaserId);
		if (StringUtils.isNotBlank(purchaserName)
				&& ContractDef.Type.SELL.match(type)) {
			queryWrapper.and(i -> i.apply(
					StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(downstream_purchasers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName));
		} else if (StringUtils.isNotBlank(purchaserName)
				&& ContractDef.Type.BUY.match(type)) {
			queryWrapper.and(i -> i.apply(
					StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(upstream_suppliers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName));
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Contract> findUp(String purchaserName, String goodsName,
			String contractName, Integer type, Long purchaserId) {
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.like(Objects.nonNull(contractName), Contract::getName,
				contractName);
		queryWrapper.like(Objects.nonNull(goodsName), Contract::getGoodsName,
				goodsName);
		queryWrapper.eq(Objects.nonNull(type), Contract::getContractType, type);
		queryWrapper.eq(Objects.nonNull(purchaserId),
				Contract::getUpstreamSuppliersId, purchaserId);
		if (StringUtils.isNotBlank(purchaserName)
				&& ContractDef.Type.SELL.match(type)) {
			queryWrapper.and(i -> i.apply(
					StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(downstream_purchasers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName));
		} else if (StringUtils.isNotBlank(purchaserName)
				&& ContractDef.Type.BUY.match(type)) {
			queryWrapper.and(i -> i.apply(
					StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(upstream_suppliers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName));
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Contract> find(String purchaserName, String goodsName,
			Integer type, Long purchaserId) {
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.like(Objects.nonNull(goodsName), Contract::getGoodsName,
				goodsName);
		queryWrapper.eq(Objects.nonNull(type), Contract::getContractType, type);
		queryWrapper.eq(Objects.nonNull(purchaserId),
				Contract::getDownstreamPurchasersId, purchaserId);
		if (StringUtils.isNotBlank(purchaserName)
				&& ContractDef.Type.SELL.match(type)) {
			queryWrapper.and(i -> i.apply(
					StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(downstream_purchasers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName).or().eq(Contract::getName, purchaserName));
		} else if (StringUtils.isNotBlank(purchaserName)
				&& ContractDef.Type.BUY.match(type)) {
			queryWrapper.and(i -> i.apply(
					StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(upstream_suppliers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName).or().eq(Contract::getName, purchaserName));
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Contract> find(String sellerName, String goodsName,
			Integer type) {
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.like(Objects.nonNull(goodsName), Contract::getGoodsName,
				goodsName);
		queryWrapper.eq(Objects.nonNull(type), Contract::getContractType, type);
		if (StringUtils.isNotBlank(sellerName)) {
			queryWrapper.and(i -> i.apply(StringUtils.isNoneBlank(sellerName),
					"(JSON_EXTRACT(upstream_suppliers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					sellerName).or());
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public Page<ContractVo> selectorMytask(Integer page, Integer size,
			String keyword) {
		List<ContractVo> contractVoList = new ArrayList<>();
		Long customId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		// 关联采购方为自己的采购合同
		wrapper.eq(Contract::getDownstreamPurchasersId, customId).eq(
				Contract::getContractType,
				ContractDef.ContractType.SALES.getCode());
		// 下游采购商id+state
		List<String> projectIds = projectService
				.findByCustomerIdAndState(customId,
						ProjectDef.State.PROCESSING.getCode())
				.stream().map(Project::getId).toList();
		wrapper.in(CollectionUtils.isNotEmpty(projectIds),
				Contract::getProjectId, projectIds);
		wrapper.like(StringUtils.isNotBlank(keyword), Contract::getName,
				keyword)
				.eq(Contract::getState, ContractDef.State.COMPLETED.getCode());
		wrapper.orderByDesc(Contract::getUpdatedTime);
		Page<Contract> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<Contract> contractList = paging.getRecords();
		if (CollectionUtils.isNotEmpty(contractList)) {
			for (Contract contract : contractList) {
				ContractVo contractVo = new ContractVo();
				contractVo.setContract(contract);
				contractVo.setEstimatedGoodsAmount(
						this.findEstimatedAmount(contract.getId())
								.orElse(BigDecimal.ZERO)
								.setScale(0, RoundingMode.HALF_UP));
				contractVoList.add(contractVo);
			}
		}
		return PageUtil.getRecordsInfoPage(paging, contractVoList);
	}

	@Override
	public Optional<Boolean> validateIsRecorded(String contractId,
			Integer contractType) {
		Contract contract = super.findOne(contractId).orElse(null);
		if (Objects.nonNull(contract)) {
			// 销售合同 判断下游采购方是否录入企业
			if (ContractDef.ContractType.SALES.match(contractType)
					|| ContractDef.ContractType.SALES
							.match(contract.getContractType())) {
				// 下游采购方客户id不为空 则为注册企业
				if (Objects.nonNull(contract.getDownstreamPurchasersId())) {
					return Optional.of(false);
				} else {
					return Optional.of(true);
				}
			}
			// 采购合同 判断上游供应商是否录入企业
			else if (ContractDef.ContractType.PURCHASE.match(contractType)
					|| ContractDef.ContractType.PURCHASE
							.match(contract.getContractType())) {
				// 下游采购方客户id不为空 则为注册企业
				if (Objects.nonNull(contract.getUpstreamSuppliersId())) {
					return Optional.of(false);
				} else {
					return Optional.of(true);
				}
			}
		}
		return Optional.empty();
	}

	/**
	 * 根据合同id获取预估可提货余额
	 *
	 * @param contractId
	 * @return
	 */
	@Override
	public Optional<BigDecimal> findEstimatedAmount(String contractId) {
		Contract contract = super.findOne(contractId).orElse(null);
		if (Objects.nonNull(contract)) {
			// 销售合同
			if (ContractDef.ContractType.SALES
					.match(contract.getContractType())) {
				// 预估可提货余额：关联了该合同的还款（关联路径：还款-借据-融资-合同）中供应链确认释放的可提货额度金额之和+状态为已完成的收款费用类型为货款付款方式不是流动贷的总金额-已提货预估金额-状态为已完成的货款退款+额度变更增加的货款-额度变更减少的货款
				// 关联了该合同的还款（关联路径：还款-借据-融资-合同）中供应链确认释放的可提货额度金额之和
				BigDecimal repaymentAmount = repaymentService
						.findAmount(contractId,
								RepaymentDef.State.RELEASED.getCode())
						.orElse(BigDecimal.ZERO);
				// 实际付货款/收货款总金额 =该项目收款状态为完成的费用类型为货款 付款方式不是流动贷的 金额汇总
				BigDecimal payedAmount = paymentService
						.findAmount(contractId,
								PaymentDef.State.COMPLETED.getCode(),
								contract.getContractType(),
								PaymentDef.CostType.GOODS_PAYMENT.getCode())
						.orElse(BigDecimal.ZERO);

				// 已提货未签收总金额 订单状态为已完成 签收状态为未开始和进行中的数据
				// 1.已申请提货未签收总金额
				BigDecimal orderAmount;
				// 注册企业
				if (Objects.nonNull(contract.getDownstreamPurchasersId())) {
					// 已申请提货（已完成、作废中、签署中、待签署、待确认订单）未签收（无签收单、草稿、待确认、待签署/签署中、作废中）数量/重量*单价汇总
					orderAmount = orderService.findAmount(contractId)
							.orElse(BigDecimal.ZERO);
				}
				// 录入企业计算
				else {
					orderAmount = orderService.findRecordAmount(contractId)
							.orElse(BigDecimal.ZERO);

				}
				// 已提货已签收（签收状态为已完成）未对账（对账状态为未开始和进行中的数据）总金额
				BigDecimal signAmount;
				// 2.已提货已签收未对账总金额=已提货已签收（签收完成）未对账（无对账单、草稿、签署中/待签署、确认中/待确认、已驳回、作废中）的签收数量/重量*订单单价汇总（如果签收订单选择合并签收单价取包含的规格的平均单价）
				// 注册企业计算
				if (Objects.nonNull(contract.getDownstreamPurchasersId())) {
					signAmount = signReceiptService.findAmount(contractId,
							List.of(SignReceiptDef.Status.FINISHED.getCode()),
							List.of(OrderDef.BusinessStatus.NOT_STARTED
									.getCode(),
									OrderDef.BusinessStatus.IN_PROGRESS
											.getCode(),
									OrderDef.BusinessStatus.INVALID.getCode(),
									OrderDef.BusinessStatus.PRE_RECONCILIATION
											.getCode()),
							contract.getContractType()).orElse(BigDecimal.ZERO);
				}
				// 录入企业计算
				else {
					// 已提货已签收未对账（无对账单）总金额
					signAmount = signReceiptService.findAmount(contractId,
							List.of(SignReceiptDef.Status.FINISHED.getCode()),
							List.of(OrderDef.BusinessStatus.NOT_STARTED
									.getCode()),
							contract.getContractType()).orElse(BigDecimal.ZERO);

				}
				// 已提货已对账（对账状态为已完成）对账总金额
				// 3.已提货已对账总金额=已提货已对账（对账完成,预对账完成）实际对账金额合计
				Optional<BigDecimal> reconciliationAmount = reconciliationService
						.findRecAmount(contractId, List.of(
								ReconciliationDef.State.FINISHED.getCode(),
								ReconciliationDef.State.PRE_FINISHED.getCode()),
								contract.getContractType());
				// 已提货预估金额=已提货未签收总金额+已提货已签收未对账总金额+已提货已对账总金额：
				BigDecimal goodsAmount = orderAmount.add(signAmount)
						.add(reconciliationAmount.orElse(BigDecimal.ZERO));
				// 货款金额做处理
				BigDecimal loanAmount = BigDecimal.ZERO;
				if (Objects.nonNull(contract.getLoanAmount())) {
					loanAmount = contract.getLoanAmount();
				}
				// 状态为已完成的货款退款
				BigDecimal refundLoanAmount = BigDecimal.ZERO;
				if (Objects.nonNull(contract.getRefundLoanAmount())) {
					refundLoanAmount = contract.getRefundLoanAmount();
				}
				// 期初可提货余额 =期初货款收入总金额-对账总金额
				BigDecimal projectInceptionAmount = projectInceptionDetailService
						.findAmount(contractId,
								ProjectInceptionDef.Type.SELL.getCode())
						.orElse(BigDecimal.ZERO);
				// 预估可提货余额：确认释放的可提货额度金额之和+实际收款总金额-状态为已完成的货款退款-已提货预估金额+货款金额+期初可提货余额
				BigDecimal estimatedGoodsAmount = repaymentAmount
						.add(payedAmount).subtract(refundLoanAmount)
						.subtract(goodsAmount).add(loanAmount)
						.add(projectInceptionAmount);

				return Optional.of(estimatedGoodsAmount);
			} else if (ContractDef.ContractType.PURCHASE
					.match(contract.getContractType())) {
				// 预估可提货余额：付款费用类型为货款的总金额-已提货预估金额

				// 货款=状态为已完成的付款费用类型为货款的未关联退款完成的退款单的总金额
				BigDecimal payedAmount = paymentService
						.findAmount(contractId,
								PaymentDef.State.COMPLETED.getCode(),
								contract.getContractType(),
								PaymentDef.CostType.GOODS_PAYMENT.getCode())
						.orElse(BigDecimal.ZERO);

				// 已提货未关联签收总金额 关联了订单 未关联签收的项目
				// 1. 已提货未签收总金额 = 已提货未关联签收数量/重量*单价 汇总
				BigDecimal orderAmount;
				// 注册企业
				if (Objects.nonNull(contract.getUpstreamSuppliersId())) {
					// 已申请提货（已完成、作废中、签署中、待签署、待确认订单）未签收（无签收单、草稿、待确认、待签署/签署中、作废中）数量/重量*单价汇总
					orderAmount = orderService.findAmount(contractId)
							.orElse(BigDecimal.ZERO);
				} else {
					orderAmount = orderService.findRecordAmount(contractId)
							.orElse(BigDecimal.ZERO);

				}
				// 已提货已签收未对账总金额
				BigDecimal signAmount;
				// 2.已提货已签收未对账总金额=已提货已签收（签收完成）未对账（无对账单、草稿、签署中/待签署、确认中/待确认、已驳回、作废中）的签收数量/重量*订单单价汇总（如果签收订单选择合并签收单价取包含的规格的平均单价）
				// 注册企业计算
				if (Objects.nonNull(contract.getUpstreamSuppliersId())) {
					signAmount = signReceiptService.findAmount(contractId,
							List.of(SignReceiptDef.Status.FINISHED.getCode()),
							List.of(OrderDef.BusinessStatus.NOT_STARTED
									.getCode(),
									OrderDef.BusinessStatus.IN_PROGRESS
											.getCode(),
									OrderDef.BusinessStatus.INVALID.getCode(),
									OrderDef.BusinessStatus.PRE_RECONCILIATION
											.getCode()),
							contract.getContractType()).orElse(BigDecimal.ZERO);
				}
				// 录入企业计算
				else {
					// 已提货已签收未对账（无对账单）总金额
					signAmount = signReceiptService.findAmount(contractId,
							List.of(SignReceiptDef.Status.FINISHED.getCode()),
							List.of(OrderDef.BusinessStatus.NOT_STARTED
									.getCode()),
							contract.getContractType()).orElse(BigDecimal.ZERO);

				}
				// 已提货已对账总金额
				// 3. 已提货已对账总金额 = 已提货已关联签收已关联对账实际对账金额合计
				Optional<BigDecimal> reconciliationAmount = reconciliationService
						.findRecAmount(contractId, List.of(
								ReconciliationDef.State.FINISHED.getCode(),
								ReconciliationDef.State.PRE_FINISHED.getCode()),
								contract.getContractType());
				// 已提货预估金额 = 已提货未签收总金额+已提货已签收未对账总金额+已提货已对账总金额：
				// 已提货预估金额 = 已提货未关联签收总金额+已提货已关联签收未关联对账总金额+已提货已关联签收已关联对账总金额：
				BigDecimal goodsAmount = orderAmount.add(signAmount)
						.add(reconciliationAmount.orElse(BigDecimal.ZERO));
				// 货款金额做处理
				BigDecimal loanAmount = BigDecimal.ZERO;
				if (Objects.nonNull(contract.getLoanAmount())) {
					loanAmount = contract.getLoanAmount();
				}
				// 期初可提货余额 =期初货款收入总金额-对账总金额
				BigDecimal projectInceptionAmount = projectInceptionDetailService
						.findAmount(contractId,
								ProjectInceptionDef.Type.BUY.getCode())
						.orElse(BigDecimal.ZERO);
				// 预估可提货余额：实际付货款总金额-已提货预估金额+(额度变更的)货款金额+期初可提货余额
				BigDecimal estimatedGoodsAmount = payedAmount
						.subtract(goodsAmount).add(loanAmount)
						.add(projectInceptionAmount);
				return Optional.of(estimatedGoodsAmount);
			}
		}
		return Optional.of(BigDecimal.ZERO);
	}

	@Override
	public Optional<BigDecimal> findEstimatedAmounts(String contractId) {
		if (StringUtils.isBlank(contractId)) {
			List<Contract> contracts = this
					.findByPurchaserIdAndState(
							CustomerContextHolder.getCustomerLoginVo()
									.getProxyAccount().getId(),
							ContractDef.State.COMPLETED.getCode());
			if (CollectionUtils.isNotEmpty(contracts)) {
				BigDecimal total = BigDecimal.ZERO;
				for (Contract contract : contracts) {
					total = total.add(this.findEstimatedAmount(contract.getId())
							.orElse(null));
				}
				return Optional.of(total);
			}
		} else {
			return Optional.of(Objects.requireNonNull(
					this.findEstimatedAmount(contractId).orElse(null)));
		}
		return Optional.of(BigDecimal.ZERO);
	}

	@Override
	@FileId
	@Transactional(rollbackFor = Exception.class)
	public Optional<Contract> create(Contract resource, Integer hasSubmit) {
		Project project = projectService.findOne(resource.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		// 按规则设置合同id:所属项目编号（6位）+0（固定）+1（固定）+自增数（4位）
		resource.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(),
				RedisKeys.Cache.PURCHASE_CONTRACT_CODE_GENERATOR,
				0 + AutoCodeDef.BusinessRuleCode.CONTRACT_SUFFIX.getCode(), 4,
				AutoCodeDef.DATE_TYPE.yy));
		// 设置状态 签署方式不为空的情况下
		if (Objects.nonNull(resource.getSignMode())) {
			// 如果是线下并且是保存并提交时设置确认中
			if (ContractDef.SignMode.OFFLINE.match(resource.getSignMode())
					&& ContractDef.HasSubmit.SAVE_AND_SUBMIT.match(hasSubmit)) {
				resource.setState(
						ContractDef.State.PENDING_CONFIRMATION.getCode());
				if (ContractDef.ContractType.SALES
						.match(resource.getContractType())) {
					this.sendNotice(resource,
							wxSubscriptionProperties.getUnConfirmContractCode(),
							MessageFormat.format(
									UserMessageConstants.CONTRACT_UNCONFIRMED_TEMPLATE,
									resource.getId()),
							ContractDef.ContractType.SALES.getCode());
				} else if (ContractDef.ContractType.PURCHASE
						.match(resource.getContractType())) {
					this.sendNotice(resource,
							wxSubscriptionProperties.getUnConfirmContractCode(),
							MessageFormat.format(
									UserMessageConstants.CONTRACT_UNCONFIRMED_TEMPLATE,
									resource.getId()),
							ContractDef.ContractType.PURCHASE.getCode());
				}
			}
			// 如果是线上直接设置完成
			else {
				resource.setState(ContractDef.State.DRAFT.getCode());
			}
		}
		// 如果签署方式为空直接设置为已完成
		else {
			// 除销售合同以外的其他类型合同无需签署、确认，提交后即为“签署完成”状态
			resource.setState(ContractDef.State.COMPLETED.getCode());
			// 只有签署完成的 销售和采购合同才会把项目状态改变
			if (ContractDef.ContractType.PURCHASE
					.match(resource.getContractType())
					|| ContractDef.ContractType.SALES
							.match(resource.getContractType())) {
				if (ProjectDef.State.PENDING.match(project.getState())) {
					// 修改关联项目状态为“履约中”
					projectService.updateState(resource.getProjectId(),
							ProjectDef.State.PROCESSING.getCode());
				}
			}
		}
		return Optional.of(super.create(resource));
	}

	@Override
	@FileId(type = 2)
	@Transactional(rollbackFor = Exception.class)
	public Optional<Contract> update(Contract resource, Integer hasSubmit) {
		if (Objects.nonNull(resource.getSignMode())) {
			if (ContractDef.SignMode.OFFLINE.match(resource.getSignMode())
					&& ContractDef.HasSubmit.SAVE_AND_SUBMIT.match(hasSubmit)) {
				resource.setState(
						ContractDef.State.PENDING_CONFIRMATION.getCode());
				if (ContractDef.ContractType.SALES
						.match(resource.getContractType())) {
					this.sendNotice(resource,
							wxSubscriptionProperties.getUnConfirmContractCode(),
							MessageFormat.format(
									UserMessageConstants.CONTRACT_UNCONFIRMED_TEMPLATE,
									resource.getId()),
							ContractDef.ContractType.SALES.getCode());
				} else if (ContractDef.ContractType.PURCHASE
						.match(resource.getContractType())) {
					this.sendNotice(resource,
							wxSubscriptionProperties.getUnConfirmContractCode(),
							MessageFormat.format(
									UserMessageConstants.CONTRACT_UNCONFIRMED_TEMPLATE,
									resource.getId()),
							ContractDef.ContractType.PURCHASE.getCode());
				}
			} else {
				resource.setState(ContractDef.State.DRAFT.getCode());
			}
		} else {
			// 除销售合同以外的其他类型合同无需签署、确认，提交后即为“签署完成”状态
			resource.setState(ContractDef.State.COMPLETED.getCode());
			// 只有签署完成的 销售和采购合同才会把项目状态改变
			if (ContractDef.ContractType.PURCHASE
					.match(resource.getContractType())
					|| ContractDef.ContractType.SALES
							.match(resource.getContractType())) {
				Project project = projectService
						.findOne(resource.getProjectId())
						.orElseThrow(() -> new BadRequestException(
								ErrorCode.CODE_30152013));
				if (ProjectDef.State.PENDING.match(project.getState())) {
					// 修改关联项目状态为“履约中”
					projectService.updateState(resource.getProjectId(),
							ProjectDef.State.PROCESSING.getCode());
				}
			}
		}
		return Optional.of(super.updateAllProperties(resource));
	}

	@Override
	@FileId(type = 2)
	public Contract updateAllProperties(Contract resource) {
		return super.updateAllProperties(resource);
	}

	@Override
	@FileId(type = 3)
	public void delete(String id) {
		super.delete(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Contract> reject(Contract contract, Boolean isRevoke) {
		fileService.batchUnActive(List.of(contract.getFileId()));
		if (ContractDef.SignMode.ONLINE.match(contract.getSignMode())) {
			contract.setFileId(null);
		}
		contract.setState(ContractDef.State.REJECTED.getCode());

		if (ContractDef.SignMode.ONLINE.match(contract.getSignMode())) {
			contract.setSignStatus(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode());
			if (isRevoke) {
				// 撤销合同
				contractRecordService.revoke(contract.getId(),
						PurchaseContractDef.CorrelationTable.CONTRACT);
			}
		} else {
			SpringUtil.getBean(ContractService.class).notice(contract, 3);
		}
		return Optional.of(super.updateAllProperties(contract));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Contract> confirm(Contract contract) {
		contract.setState(ContractDef.State.COMPLETED.getCode());
		if (ContractDef.SignMode.OFFLINE.match(contract.getSignMode())) {
			SpringUtil.getBean(ContractService.class).notice(contract, 2);
		}
		// 只有签署完成的 销售和采购合同才会把项目状态改变
		Project project = projectService.findOne(contract.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		if (ProjectDef.State.PENDING.match(project.getState())) {
			// 修改关联项目状态为“履约中”
			projectService.updateState(contract.getProjectId(),
					ProjectDef.State.PROCESSING.getCode());
		}
		return Optional.of(super.updateAllProperties(contract));
	}

	@Override
	public Optional<Contract> submit(Contract contract) {
		if (ContractDef.SignMode.OFFLINE.match(contract.getSignMode())) {
			contract.setState(ContractDef.State.PENDING_CONFIRMATION.getCode());
		} else {
			contract.setState(ContractDef.State.TO_BE_INITIATE.getCode());
		}
		return Optional.of(super.updateAllProperties(contract));
	}

	@Override
	public Optional<Contract> retract(Contract contract) {
		contract.setState(ContractDef.State.DRAFT.getCode());
		return Optional.of(super.updateAllProperties(contract));
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Optional<ContractPageResponse> initiateSign(Contract resource,
			Integer origin, Integer initiateType) {

		// 设置供应链签署人
		switch (CommonDef.AccountSource.from(initiateType)) {
			case CUSTOM -> {
				User user = adminSealService
						.findByType(SignerSettings.billType.contract)
						.orElse(null);
				if (Objects.nonNull(user)) {
					resource.setSupplierSigner(user.getName());
					resource.setSupplierSignerId(user.getId());
				}
			}
			case INNER -> {
				User user = UserContextHolder.getUser();
				if (Objects.nonNull(user)) {
					resource.setSupplierSigner(user.getName());
					resource.setSupplierSignerId(user.getId());
				}
			}
		}

		Map<Long, String> customerMap;
		// 销售合同
		if (ContractDef.ContractType.SALES.match(resource.getContractType())) {
			// 发起合同
			customerMap = contractRecordService.draft(resource.getName(),
					List.of(resource.getDownstreamPurchasersId(),
							resource.getSupplierChainId()),
					List.of(resource.getFileId()), resource.getId(),
					PurchaseContractDef.CorrelationTable.CONTRACT, null, null);
			resource.getDownstreamPurchasersEnterprise().setSignMobile(
					customerMap.get(resource.getDownstreamPurchasersId()));
			resource.getSupplierChainEnterprise().setSignMobile(
					customerMap.get(resource.getSupplierChainId()));
			// 校验子账号是否有签章权限
			if (!contractRecordService.validateSign(
					resource.getDownstreamPurchasersEnterprise()
							.getSignMobile(),
					resource.getSupplierChainEnterprise().getSignMobile())) {
				return Optional.empty();
			}
		}
		// 采购合同
		else {
			// 发起合同
			customerMap = contractRecordService.draft(resource.getName(),
					List.of(resource.getUpstreamSuppliersId(),
							resource.getSupplierChainId()),
					List.of(resource.getFileId()), resource.getId(),
					PurchaseContractDef.CorrelationTable.CONTRACT, null, null);
			resource.getUpstreamSuppliersEnterprise().setSignMobile(
					customerMap.get(resource.getUpstreamSuppliersId()));
			resource.getSupplierChainEnterprise().setSignMobile(
					customerMap.get(resource.getSupplierChainId()));
			// 校验子账号是否有签章权限
			if (!contractRecordService.validateSign(
					resource.getUpstreamSuppliersEnterprise().getSignMobile(),
					resource.getSupplierChainEnterprise().getSignMobile())) {
				return Optional.empty();
			}
		}

		// 设置文件id
		resource.setFileId(contractRecordService.download(resource.getId(),
				PurchaseContractDef.CorrelationTable.CONTRACT));
		resource.setState(ContractDef.State.SIGNING.getCode());
		resource.setSignStatus(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode());
		this.updateAllProperties(resource);

		// 获取签署链接
		return contractRecordService.sign(resource.getId(),
				PurchaseContractDef.CorrelationTable.CONTRACT, origin);
	}

	@Override
	public void updateDeposit(String id, BigDecimal deposit) {
		LambdaUpdateWrapper<Contract> wrapper = Wrappers
				.lambdaUpdate(Contract.class);
		wrapper.eq(Contract::getId, id)
				.eq(Contract::getDel, CommonDef.Symbol.NO.getCode())
				.set(Contract::getDeposit, deposit);
		repository.update(wrapper);
	}

	@Override
	public void updateLoanAmount(String id, BigDecimal loanAmount) {
		LambdaUpdateWrapper<Contract> wrapper = Wrappers
				.lambdaUpdate(Contract.class)
				.set(Contract::getLoanAmount, loanAmount)
				.eq(Contract::getId, id)
				.eq(Contract::getDel, CommonDef.Symbol.NO.getCode());
		repository.update(wrapper);
	}

	@Override
	public void updateRefundLoanAmount(String id, BigDecimal refundLoanAmount) {
		LambdaUpdateWrapper<Contract> wrapper = Wrappers
				.lambdaUpdate(Contract.class)
				.set(Contract::getRefundLoanAmount, refundLoanAmount)
				.eq(Contract::getId, id)
				.eq(Contract::getDel, CommonDef.Symbol.NO.getCode());
		repository.update(wrapper);
	}

	@Override
	public Optional<ContractPageResponse> sign(Contract contract,
			Integer origin) {
		if (ContractDef.ContractType.SALES.match(contract.getContractType())) {
			// 校验子账号是否有签章权限
			if (!contractRecordService.validateSign(
					contract.getDownstreamPurchasersEnterprise()
							.getSignMobile(),
					contract.getSupplierChainEnterprise().getSignMobile())) {
				return Optional.empty();
			}
		} else {
			// 校验子账号是否有签章权限
			if (!contractRecordService.validateSign(
					contract.getUpstreamSuppliersEnterprise().getSignMobile(),
					contract.getSupplierChainEnterprise().getSignMobile())) {
				return Optional.empty();
			}
		}

		// 获取签署链接
		return contractRecordService.sign(contract.getId(),
				PurchaseContractDef.CorrelationTable.CONTRACT, origin);
	}

	@Override
	public Optional<ContractPageResponse> invalid(Contract contract,
			Integer origin) {
		// 调用契约锁撤销合同接口
		contractRecordService.revoke(contract.getId(),
				PurchaseContractDef.CorrelationTable.CONTRACT);
		// 作废后获取作废合同id
		Long fileId = contractRecordService.detail(contract.getId(),
				PurchaseContractDef.CorrelationTable.CONTRACT);
		contract.setInvalidFileId(fileId);
		contract.setState(ContractDef.State.INVALIDING.getCode());
		contract.setInvalidSignState(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode());
		contract.setInvalidRevokeReason(null);
		contract.setInvalidRevokeTime(null);
		contract.setPurchaseInvalidTime(null);
		contract.setSellerInvalidTime(null);
		super.update(contract);

		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				contract.getDownstreamPurchasersEnterprise().getSignMobile(),
				contract.getSupplierChainEnterprise().getSignMobile())) {
			return Optional.empty();
		}

		return contractRecordService.sign(contract.getId(),
				PurchaseContractDef.CorrelationTable.CONTRACT,
				CertificationDef.Origin.PC.getCode());
	}

	@Override
	public Optional<Contract> invalidOffLine(Contract contract,
			Integer initiator) {
		// 作废合同
		contract.setState(ContractDef.State.INVALIDING.getCode());
		contract.setInvalidInitiator(initiator);
		contract.setInvalidRevokeReason(null);
		contract.setInvalidRevokeTime(null);
		contract.setPurchaseInvalidTime(null);
		contract.setSellerInvalidTime(null);
		if (CommonDef.UserType.INNER.match(initiator)) {
			contract.setInvalidSignState(
					BusinessContractDef.CommonSignState.SUPPLY_SIGNED
							.getCode());
			contract.setSellerInvalidTime(LocalDateTime.now());
		} else {
			contract.setInvalidSignState(
					BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode());
			contract.setPurchaseInvalidTime(LocalDateTime.now());
		}
		return Optional.of(super.updateAllProperties(contract));
	}

	@Override
	public Optional<Contract> confirmInvalid(Contract contract) {
		// 确认作废合同
		contract.setState(ContractDef.State.INVALID.getCode());
		contract.setInvalidSignState(
				BusinessContractDef.CommonSignState.COMPLETED.getCode());
		if (CommonDef.UserType.INNER.match(contract.getInvalidInitiator())) {
			contract.setPurchaseInvalidTime(LocalDateTime.now());
		} else {
			contract.setSellerInvalidTime(LocalDateTime.now());
		}
		return Optional.of(super.updateAllProperties(contract));
	}

	@Override
	public Optional<Contract> revertInvalid(Contract contract) {
		// 撤销作废合同
		contract.setState(ContractDef.State.COMPLETED.getCode());
		contract.setInvalidSignState(null);
		contract.setInvalidRevokeTime(LocalDateTime.now());
		if (CommonDef.UserType.INNER.match(contract.getInvalidInitiator())) {
			contract.setPurchaseInvalidTime(null);
		} else {
			contract.setSellerInvalidTime(null);
		}
		return Optional.of(super.updateAllProperties(contract));
	}

	@Override
	public Optional<ContractCountVo> staticsAdminContract(Boolean isManage,
			boolean isSeal) {
		ContractCountVo contractCountVo = new ContractCountVo();
		contractCountVo.setReject(0L);
		contractCountVo.setWaitConfirm(0L);
		contractCountVo.setToBeSigned(0L);
		List<String> projectIds = projectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计已驳回
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(Contract::getState,
						ContractDef.State.REJECTED.getCode());
				queryWrapper.in(Contract::getProjectId, projectIds);
				contractCountVo.setReject(repository.selectCount(queryWrapper));
			}
		}

		if (isSeal) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计待签署
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(Contract::getState,
						ContractDef.State.SIGNING.getCode());
				queryWrapper.and(x -> x
						.eq(Contract::getSignStatus,
								BusinessContractDef.CommonSignState.UNSIGNED
										.getCode())
						.or().eq(Contract::getSignStatus,
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode()));
				queryWrapper.in(Contract::getProjectId, projectIds);
				contractCountVo
						.setToBeSigned(repository.selectCount(queryWrapper));
			}
		}
		return Optional.of(contractCountVo);
	}

	@Override
	public Optional<ContractCountVo> staticsCustomerContract(boolean isSeal,
			boolean isPermission) {
		ContractCountVo contractCountVo = new ContractCountVo();
		contractCountVo.setReject(0L);
		contractCountVo.setWaitConfirm(0L);
		contractCountVo.setToBeSigned(0L);
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		Long customerId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();
		if (isPermission) {
			// 统计待确认
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(Contract::getDownstreamPurchasersId, customerId);
			queryWrapper.eq(Contract::getState,
					ContractDef.State.PENDING_CONFIRMATION.getCode());
			contractCountVo
					.setWaitConfirm(repository.selectCount(queryWrapper));
		}

		if (isSeal) {
			// 统计待签署
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(Contract::getState,
					ContractDef.State.SIGNING.getCode());
			queryWrapper.and(x -> x
					.eq(Contract::getSignStatus,
							BusinessContractDef.CommonSignState.UNSIGNED
									.getCode())
					.or().eq(Contract::getSignStatus,
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode()));
			queryWrapper.eq(Contract::getDownstreamPurchasersId, customerId);
			contractCountVo.setToBeSigned(repository.selectCount(queryWrapper));
		}

		return Optional.of(contractCountVo);
	}

	@Override
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = "{{#success}}", type = LogDef.CONTRACT_INFO, bizNo = "{{#resource.getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#resource.getId()}}"),
			@LogRecord.KeyValuePair(key = "#projectId#", value = "{{#code}}") }, messageType = LogDef.MESSAGE_TYPE_CONTRACT, permission = LogDef.PROJECT_DEAL)
	public void notice(Contract resource, Integer type) {
		Project project = projectService.findOne(resource.getProjectId())
				.orElse(new Project());
		LogRecordContext.putVariable("code", project.getName());
		switch (type) {
			case 1 -> LogRecordContext.putVariable("success",
					LogDef.CONTRACT_TO_BE_CONFIRMED);
			case 2 -> LogRecordContext.putVariable("success",
					LogDef.CONTRACT_CONFIRMED);
			case 3 -> LogRecordContext.putVariable("success",
					LogDef.CONTRACT_REJECTED);
			case 4 -> LogRecordContext.putVariable("success",
					LogDef.CONTRACT_COMPLETED);
			case 5 -> LogRecordContext.putVariable("success",
					LogDef.CONTRACT_IS_ABOUT_TO_EXPIRE);
			case 6 -> LogRecordContext.putVariable("success",
					LogDef.CONTRACT_IS_EXPIRED);
			default -> {
			}
		}
		log.info("合同发送通知:{}", resource.getId());
	}

	/**
	 * 封装vo
	 *
	 * @param contract
	 * @return
	 */
	private ContractVo packVo(Contract contract, Long customId) {
		ContractVo vo = new ContractVo();
		vo.setContract(contract);
		// 设置项目信息
		if (ObjectUtils.isNotEmpty(contract.getProjectId())) {
			projectService.findOne(contract.getProjectId())
					.ifPresent(vo::setProject);
		}
		// 设置关联合同
		List<Contract> relateContractList = super.findByIdsNoDeleted(
				contract.getRelateContractIds());
		// 客户端查看的时候，要过滤不是自己的
		if (Objects.nonNull(customId)) {
			relateContractList = relateContractList.stream()
					.filter(reContract -> customId
							.equals(reContract.getDownstreamPurchasersId()))
					.collect(Collectors.toList());
			vo.setRelateContracts(relateContractList);
		} else {
			vo.setRelateContracts(relateContractList);
		}
		// 如果是借款合同，设置合同是否被融资关联
		if (ContractDef.ContractType.LOAN_AGREEMENT
				.match(contract.getContractType())) {
			// 在“借款合同”被关联
			List<Financing> financingList1 = financingService
					.findByLoanContractIds(List.of(contract.getId()));
			if (CollectionUtils.isNotEmpty(financingList1)) {
				vo.setIsLinkedFinancing(CommonDef.Symbol.YES.getCode());
			} else {
				// 在“关联合同”被关联
				List<Financing> financingList2 = financingService
						.findByRelatedContractId(contract.getId());
				vo.setIsLinkedFinancing(CollectionUtils.isEmpty(financingList2)
						? CommonDef.Symbol.NO.getCode()
						: CommonDef.Symbol.YES.getCode());
			}
		}
		return vo;
	}

	/**
	 * 封装分页查询的vo信息
	 *
	 * @param
	 * @return
	 */
	private List<ContractVo> packPageVos(List<Contract> contractList) {
		if (CollectionUtils.isEmpty(contractList)) {
			return List.of();
		}
		List<ContractVo> vos = new ArrayList<>();
		// 获取合同id列表
		List<String> contractIds = contractList.stream().map(Contract::getId)
				.distinct().toList();
		// 借款合同是这些合同的融资列表
		List<Financing> financingList = financingService
				.findByLoanContractIds(contractIds);
		// 将融资根据借款合同id进行分组
		Map<String, List<Financing>> financingMap = financingList.stream()
				.collect(Collectors.groupingBy(Financing::getLoanContractId));
		// 项目信息
		List<String> projectIds = contractList.stream()
				.map(Contract::getProjectId).distinct().toList();
		Map<String, Project> projectMap = projectService.findByIds(projectIds)
				.stream().collect(Collectors.toMap(Project::getId, e -> e));
		for (Contract contract : contractList) {
			ContractVo vo = new ContractVo();
			vo.setContract(contract);
			vo.setProject(projectMap.get(contract.getProjectId()));
			// 如果是借款合同，设置合同是否被融资关联
			if (ContractDef.ContractType.LOAN_AGREEMENT
					.match(contract.getContractType())) {
				// 在“借款合同”被关联
				List<Financing> financingListByContractId = financingMap
						.get(contract.getId());
				if (CollectionUtils.isNotEmpty(financingListByContractId)) {
					vo.setIsLinkedFinancing(CommonDef.Symbol.YES.getCode());
				} else {
					// 在“关联合同”被关联
					List<Financing> financingList2 = financingService
							.findByRelatedContractId(contract.getId());
					vo.setIsLinkedFinancing(
							CollectionUtils.isEmpty(financingList2)
									? CommonDef.Symbol.NO.getCode()
									: CommonDef.Symbol.YES.getCode());
				}
			}
			vos.add(vo);
		}
		return vos;
	}

	/**
	 * 封装分页查询的vo信息
	 *
	 * @param
	 * @return
	 */
	private List<ContractGoodsInfoVo> packPageGoodsInfoVos(
			List<Contract> contractList) {
		if (CollectionUtils.isEmpty(contractList)) {
			return List.of();
		}
		List<String> contractIds = contractList.stream().map(Contract::getId)
				.distinct().toList();
		List<String> projectIds = contractList.stream()
				.map(Contract::getProjectId).distinct().toList();
		List<Project> projectList = projectService.findByIds(projectIds);
		List<Long> goodIds = projectList.stream().map(Project::getGoodsId)
				.toList();
		List<Goods> goodsList = goodsService.findByIds(goodIds);
		Map<Long, Goods> goodsMap = goodsList.stream()
				.collect(Collectors.toMap(Goods::getId, e -> e));

		Map<String, Project> projectMap = projectList.stream()
				.collect(Collectors.toMap(Project::getId, e -> e));
		// 仓储期初入库信息
		List<StorageInceptionInboundDetail> storageInboundList = storageInceptionInboundDetailService
				.findByProjectIds(projectIds);
		// 仓储期初入库根据项目分组
		Map<String, List<StorageInceptionInboundDetail>> inboundMap = storageInboundList
				.stream().collect(Collectors.groupingBy(
						StorageInceptionInboundDetail::getProjectId));
		// 仓储期初出库信息
		List<StorageInceptionOutboundDetail> storagOutnboundList = storageInceptionOutboundDetailService
				.findByProjectIds(projectIds);
		// 仓储期初出库根据项目分组
		Map<String, List<StorageInceptionOutboundDetail>> outboundMap = storagOutnboundList
				.stream().collect(Collectors.groupingBy(
						StorageInceptionOutboundDetail::getProjectId));

		// 根据项目id查询已入库的数据
		List<Inbound> inboundList = inboundService.findByProjectIds(projectIds,
				List.of(InboundDef.Status.INBOUNDED.getCode()),
				InboundDef.Type.JXC.getCode());
		// 已入库的数据根据项目id进行分组
		Map<String, List<Inbound>> inboundMap1 = inboundList.stream()
				.collect(Collectors.groupingBy(Inbound::getProjectId));
		// 根据项目id查询已出库的数据
		List<Outbound> outboundList = outboundService.findByProjectIds(
				projectIds, List.of(OutboundDef.Status.OUTBOUND.getCode()),
				InboundDef.Type.JXC.getCode());
		// 已入库的数据根据项目id进行分组
		Map<String, List<Outbound>> outboundMap1 = outboundList.stream()
				.collect(Collectors.groupingBy(Outbound::getProjectId));

		// 根据项目id查询待出库的数据
		List<Outbound> toOutboundList = outboundService.findByProjectIds(
				projectIds,
				List.of(OutboundDef.Status.CONFIRMING.getCode(),
						OutboundDef.Status.TO_BE_INITIATED.getCode(),
						OutboundDef.Status.DRAFT.getCode(),
						OutboundDef.Status.REJECTED.getCode(),
						OutboundDef.Status.INVALIDING.getCode(),
						OutboundDef.Status.SIGNING.getCode()),
				InboundDef.Type.JXC.getCode());
		// 待出库的数据根据项目id进行分组
		Map<String, List<Outbound>> toOutboundMap = toOutboundList.stream()
				.collect(Collectors.groupingBy(Outbound::getProjectId));
		// 根据合同id查询质押信息
		List<Pledge> pledges = pledgeService.findByContractIds(contractIds);
		// 质押信息根据合同id进行分组
		Map<String, List<Pledge>> pledgeMap = pledges.stream()
				.collect(Collectors.groupingBy(Pledge::getContractId));

		return contractList.stream().map(e -> {
			ContractGoodsInfoVo vo = new ContractGoodsInfoVo();
			vo.setContract(e);
			Project project = projectMap.get(e.getProjectId());
			vo.setProject(project);
			// 期初入库数量/重量
			BigDecimal inboundWeight1 = BigDecimal.ZERO;
			// 期初出库数量/重量
			BigDecimal outboundWeight1 = BigDecimal.ZERO;
			// 已完成入库数量/重量
			BigDecimal inboundWeight2 = BigDecimal.ZERO;
			// 已完成出库数量/重量
			BigDecimal outboundWeight2 = BigDecimal.ZERO;
			// 待出库数量/重量
			BigDecimal outboundWeight3 = BigDecimal.ZERO;
			// 库存数量/重量
			BigDecimal stockWeight = BigDecimal.ZERO;
			List<StorageInceptionInboundDetail> inbounds1 = inboundMap
					.get(e.getProjectId());
			// 期初入库信息
			if (CollectionUtils.isNotEmpty(inbounds1)) {
				inboundWeight1 = inbounds1.stream()
						.map(StorageInceptionInboundDetail::getQuantity)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			List<StorageInceptionOutboundDetail> outbounds1 = outboundMap
					.get(e.getProjectId());
			// 期初出库信息
			if (CollectionUtils.isNotEmpty(outbounds1)) {
				outboundWeight1 = outbounds1.stream()
						.map(StorageInceptionOutboundDetail::getQuantity)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			// 已完成入库信息
			List<Inbound> inbounds2 = inboundMap1.get(e.getProjectId());
			// 已完成入库信息
			if (CollectionUtils.isNotEmpty(inbounds2)) {
				inboundWeight2 = inbounds2.stream()
						.map(Inbound::getInboundWeight).filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);

			}
			// 已完成出库信息
			List<Outbound> outbounds2 = outboundMap1.get(e.getProjectId());
			// 已完成出库数量/重量
			if (CollectionUtils.isNotEmpty(outbounds2)) {
				outboundWeight2 = outbounds2.stream()
						.map(Outbound::getOutboundWeight)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			// 待出库信息
			List<Outbound> outbounds3 = toOutboundMap.get(e.getProjectId());
			// 待出库数量/重量
			if (CollectionUtils.isNotEmpty(outbounds3)) {
				outboundWeight3 = outbounds3.stream()
						.map(Outbound::getOutboundWeight)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			// 库存数量/重量= 期初库存（期初入库-期初出库) + 有效入库数量 - 有效出库数量
			stockWeight = stockWeight.add(inboundWeight1).add(inboundWeight2)
					.subtract(outboundWeight1).subtract(outboundWeight2);
			vo.setStockWeight(stockWeight);
			// 质押重量
			BigDecimal pledgeWeight = BigDecimal.ZERO;
			// 质押信息
			List<Pledge> pledgeList = pledgeMap.get(e.getId());
			if (CollectionUtils.isNotEmpty(pledgeList)) {
				Pledge pledge = pledgeList.stream()
						.max(Comparator.comparing(Pledge::getCreatedTime))
						.orElse(null);
				if (Objects.nonNull(pledge)) {
					String pledgeInfo = pledge.getPledgeInfo();
					List<PledgeInfo> pledgeInfos = this
							.convertPledgeInfo(pledgeInfo);
					PledgeInfo pledgeInfo1 = pledgeInfos
							.get(pledgeInfos.size() - 1);
					pledgeWeight = pledgeInfo1.getPledgeNum();
				}
			}
			if (BigDecimal.ZERO.compareTo(pledgeWeight) != 0) {
				vo.setPledgeWeight(pledgeWeight);
			}
			// 质押数量/重量*库存控货比
			BigDecimal pledgeWeight1;
			// 质押数量/重量*库存控货比
			pledgeWeight1 = Objects.nonNull(project.getInventoryControlRatio())
					? pledgeWeight.multiply(project.getInventoryControlRatio())
							.divide(new BigDecimal(100), 2,
									RoundingMode.HALF_UP)
					: pledgeWeight;
			// 可销售出库数量/重量=库存数量/重量-质押数量/重量*库存控货比-待出库数量/重量
			BigDecimal saleOutboundWeight;
			saleOutboundWeight = stockWeight.subtract(pledgeWeight1)
					.subtract(outboundWeight3);
			vo.setSaleOutboundWeight(saleOutboundWeight);
			vo.setInventoryControlRatio(project.getInventoryControlRatio());
			vo.setGoodsName(project.getGoodsName());
			vo.setUnit(goodsMap.get(project.getGoodsId()).getUnit());
			return vo;
		}).toList();

	}

	/**
	 * 封装合同vo信息
	 *
	 * @param
	 * @return
	 */
	private List<ContractVo> packContractVos(List<Contract> contractList) {
		List<ContractVo> contractVos = new ArrayList<>();
		for (Contract contract : contractList) {
			ContractVo vo = new ContractVo();
			vo.setContract(contract);
			this.findEstimatedAmount(contract.getId())
					.ifPresent(vo::setEstimatedGoodsAmount);
			contractVos.add(vo);
		}
		return contractVos;
	}

	private List<PledgeInfo> convertPledgeInfo(String data) {
		Gson gson = new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();
		Type listType = new TypeToken<List<PledgeInfo>>() {
		}.getType();
		return gson.fromJson(data, listType);
	}

	/**
	 * 发送短信
	 *
	 * @param
	 * @param templateCode
	 * @param title
	 */
	private void sendNotice(Contract contract, String templateCode,
			String title, Integer type) {
		Customer customer = null;
		if (ContractDef.ContractType.SALES.match(type)) {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(contract.getDownstreamId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		} else {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(contract.getUpstreamId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		}

		if (Objects.nonNull(customer)) {
			if (StringUtils.isNotBlank(templateCode)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(templateCode)
						.params(Map.of("order_id", contract.getId()))
						.mobile(customer.getMobile()).build());
			}

			if (StringUtils.isNotBlank(title)) {
				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.CONTRACT.getCode())
						.title(title)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.CONTRACT_DETAIL_PAGE)
						.detailId(String.valueOf(contract.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
		}
	}

}
