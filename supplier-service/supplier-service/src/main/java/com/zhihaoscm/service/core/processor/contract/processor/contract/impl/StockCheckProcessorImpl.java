package com.zhihaoscm.service.core.processor.contract.processor.contract.impl;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.DealingsEnterprise;
import com.zhihaoscm.domain.bean.entity.StockCheck;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.core.processor.contract.processor.contract.ContractProcessor;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

@Service
public class StockCheckProcessorImpl implements ContractProcessor {

	@Autowired
	private StockCheckService stockCheckService;
	@Autowired
	private FileService fileService;
	@Autowired
	private ContractRecordService contractRecordService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	@Override
	public Boolean support(Integer type) {
		return PurchaseContractDef.CorrelationTable.STOCK_CHECK.match(type);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void signComplete(String tableId) {
		stockCheckService.findOne(tableId).ifPresent(stockCheck -> {
			if (StockCheckDef.State.FINISHED.match(stockCheck.getState())) {
				return;
			}
			// 修改状态 签署状态双方已完成
			stockCheck.setSignStatus(
					BusinessContractDef.CommonSignState.COMPLETED.getCode());
			// 状态改为已完成
			stockCheck.setState(StockCheckDef.State.FINISHED.getCode());
			// 重新获取新的签署文件
			Long newFileId = contractRecordService.download(tableId,
					PurchaseContractDef.CorrelationTable.STOCK_CHECK);
			stockCheck.setCheckFileId(newFileId);

			if (StockCheckDef.Module.JXC.match(stockCheck.getModule())) {
				stockCheckService.notice(stockCheck, 3);
			} else {
				stockCheckService.notice(stockCheck, 8);
			}

			Customer customer = null;
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(stockCheck.getPurchaserBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}

			if (Objects.nonNull(customer)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(wxSubscriptionProperties
								.getFinishStockCheckCode())
						.params(Map.of("order_id", stockCheck.getId()))
						.mobile(customer.getMobile()).build());

				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.INVENTORY.getCode())
						.title(MessageFormat.format(
								UserMessageConstants.STOCK_CHECK_CONFIRMED_TEMPLATE,
								stockCheck.getId()))
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(StockCheckDef.Module.JXC
								.match(stockCheck.getModule())
										? UserMessageConstants.STOCK_CHECK_DETAIL_PAGE
										: UserMessageConstants.SUPERVISION_STOCK_CHECK_DETAIL_PAGE)
						.detailId(String.valueOf(stockCheck.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}

			stockCheckService.update(stockCheck);
		});
	}

	@Override
	public void signReject(String tableId, String contact) {
		stockCheckService.findOne(tableId).ifPresent(stockCheck -> {
			if (Objects.nonNull(stockCheck.getCheckFileId())
					&& StockCheckDef.SignType.ONLINE
							.match(stockCheck.getSignType())) {
				fileService.batchUnActive(List.of(stockCheck.getCheckFileId()));
				stockCheck.setCheckFileId(null);
			}
			stockCheck.setState(StockCheckDef.State.REJECTED.getCode());
			stockCheck.setSignStatus(null);
			stockCheckService.update(stockCheck);
		});
	}

	@Override
	public void signing(String correlationId, String name,
			String callbackType) {
		stockCheckService.findOne(correlationId).ifPresent(stockCheck -> {
			// 防止契约锁回调顺序有问题的处理
			if (StockCheckDef.State.FINISHED.match(stockCheck.getState())) {
				return;
			}
			Long newFileId = contractRecordService.download(correlationId,
					PurchaseContractDef.CorrelationTable.STOCK_CHECK);
			fileService.batchUnActive(List.of(stockCheck.getCheckFileId()));
			stockCheck.setCheckFileId(newFileId);
			boolean isBuyer = false;
			if (PurchaseContractDef.CallbackType.SEAL.name()
					.equals(callbackType)) {
				isBuyer = name
						.equals(stockCheck.getPurchaserEnterprise().getName());
			} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
					.equals(callbackType)) {
				isBuyer = name.equals(
						stockCheck.getPurchaserEnterprise().getRealName());
			}
			boolean isSupplier = false;
			if (PurchaseContractDef.CallbackType.SEAL.name()
					.equals(callbackType)) {
				isSupplier = name
						.equals(stockCheck.getSellerEnterprise().getName());
			} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
					.equals(callbackType)) {
				isSupplier = name
						.equals(stockCheck.getSellerEnterprise().getRealName());
			}
			if (isBuyer) {
				if (BusinessContractDef.CommonSignState.SUPPLY_SIGNED
						.match(stockCheck.getSignStatus())) {
					stockCheck.setSignStatus(
							BusinessContractDef.CommonSignState.COMPLETED
									.getCode());
				} else {
					stockCheck.setSignStatus(
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode());
				}
			} else if (isSupplier) {
				if (BusinessContractDef.CommonSignState.BUYER_SIGNED
						.match(stockCheck.getSignStatus())) {
					stockCheck.setSignStatus(
							BusinessContractDef.CommonSignState.COMPLETED
									.getCode());
				} else {
					stockCheck.setSignStatus(
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode());
				}
			}
			stockCheckService.update(stockCheck);
		});

	}

	@Override
	public void sendInvalid(String tableId, String name) {
		stockCheckService.findOne(tableId).ifPresent(stockCheck -> {
			if (StockCheckDef.State.INVALIDING.match(stockCheck.getState())
					&& Objects.nonNull(stockCheck.getInvalidFileId())) {
				return;
			}
			// 作废后获取作废合同id
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.STOCK_CHECK);
			StockCheck stockCheck1 = new StockCheck();
			stockCheck1.setInvalidFileId(fileId);
			stockCheck1.setState(StockCheckDef.State.INVALIDING.getCode());
			stockCheck1.setInvalidSignState(
					PurchaseContractDef.CommonSignState.UNSIGNED.getCode());
			stockCheckService.updateNotNull(stockCheck1);
		});
	}

	@Override
	public void invaliding(String tableId, String name) {
		stockCheckService.findOne(tableId).ifPresent(stockCheck -> {
			// 防止契约锁回调顺序有问题的处理
			if (Objects.equals(StockCheckDef.State.INVALID.getCode(),
					stockCheck.getState())) {
				return;
			}
			boolean isBuyer;
			boolean isSupplier;
			isBuyer = name.equals(stockCheck.getPurchaserEnterprise().getName())
					|| name.equals(
							stockCheck.getPurchaserEnterprise().getRealName());

			isSupplier = name.equals(stockCheck.getSellerEnterprise().getName())
					|| name.equals(
							stockCheck.getSellerEnterprise().getRealName());

			if (isBuyer) {
				stockCheck.setInvalidSignState(
						PurchaseContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				stockCheck.setPurchaseInvalidTime(LocalDateTime.now());
			} else if (isSupplier) {
				stockCheck.setInvalidSignState(
						PurchaseContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
				stockCheck.setSellerInvalidTime(LocalDateTime.now());
			}

			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.STOCK_CHECK);
			stockCheck.setInvalidFileId(fileId);
			stockCheckService.update(stockCheck);
		});
	}

	@Override
	public void invalided(String tableId, String name) {
		stockCheckService.findOne(tableId).ifPresent(stockCheck -> {
			stockCheck.setState(StockCheckDef.State.INVALID.getCode());
			stockCheck.setInvalidSignState(
					PurchaseContractDef.CommonSignState.COMPLETED.getCode());
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.STOCK_CHECK);
			stockCheck.setInvalidFileId(fileId);
			stockCheckService.update(stockCheck);
		});
	}

	@Override
	public void rejectInvalid(String tableId, String name) {
		stockCheckService.findOne(tableId).ifPresent(stockCheck -> {
			stockCheck.setState(StockCheckDef.State.FINISHED.getCode());
			stockCheck.setInvalidSignState(null);
			stockCheck.setInvalidRevokeTime(LocalDateTime.now());
			stockCheckService.updateAllProperties(stockCheck);
		});
	}
}
