package com.zhihaoscm.service.resource.validator.order;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import com.zhihaoscm.service.resource.form.order.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.GoodsInfo;
import com.zhihaoscm.domain.meta.biz.BusinessContractDef;
import com.zhihaoscm.domain.meta.biz.ContractDef;
import com.zhihaoscm.domain.meta.biz.DeliverGoodsDef;
import com.zhihaoscm.domain.meta.biz.OrderDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.LocalDateTimeAdapter;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.resource.validator.contract.ContractValidator;
import com.zhihaoscm.service.resource.validator.customer.CustomerValidator;
import com.zhihaoscm.service.resource.validator.project.ProjectValidator;

/**
 * @description: 订单校验器
 * @author: 彭湃
 * @date: 2025/1/17 16:03
 **/
@Component
public class OrderValidator {

	@Autowired
	private OrderService orderService;

	@Autowired
	private ProjectService projectService;

	@Autowired
	private ContractService contractService;

	@Autowired
	private SignReceiptService signReceiptService;

	@Autowired
	private CustomerValidator customerValidator;
	@Autowired
	private AccountsService accountsService;

	@Autowired
	private ProjectValidator projectValidator;

	@Autowired
	private ContractValidator contractValidator;

	@Autowired
	private DeliverGoodsService deliverGoodsService;

	@Autowired
	private PaymentService paymentService;

	public void validateCreate(OrderCreateForm form) {
		Project project = projectService.findOne(form.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30001001));

		Contract contract = contractService.findOne(form.getContractId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30001001));

		if (OrderDef.SignType.ONLINE.match(form.getSignType())) {
			customerValidator
					.validateSealAdmin(contract.getDownstreamPurchasersId());
		}

        if(!Objects.equals(contract.getContractType(), ContractDef.Type.SELL.getCode())) {
            throw new BadRequestException(ErrorCode.CODE_30009050);
        }

		// 先货后款合同的账期不能为空
		if (ContractDef.SettleWay.GOODS_FIRST.match(contract.getSettleWay())) {
			if (Objects.isNull(form.getSettlePeriod())) {
				throw new BadRequestException(ErrorCode.CODE_30009033);
			}
			if (form.getSettlePeriod() < 0 || form.getSettlePeriod() > 1000) {
				throw new BadRequestException(ErrorCode.CODE_30009034);
			}
		}

		// 如果关联的合同结算方式为先款后货，校验合计金额须小于等于项目的预估可提货余额，不满足弹窗提示去还款
		if (ContractDef.SettleWay.PAYMENT_FIRST
				.match(contract.getSettleWay())) {
			// 关联项目的预估可提货余额
			BigDecimal estimatedAmount = orderService
					.findCustomerEstimatedGoodsAmount(form.getProjectId())
					.orElse(BigDecimal.ZERO);
			// 订单合计金额大于预估可提货余额时，要提示
			if (form.getGoodsTotalAmount().compareTo(estimatedAmount) > 0) {
				throw new BadRequestException(ErrorCode.CODE_30009040);
			}

		}
		List<GoodsInfo> list = this.convertGoodsInfo(form.getGoodsInfo());
		this.validateModel(list);
		// 根据项目id列表查询项目已逾期的应收款数据列表 去除逾期的校验
		// List<Accounts> accountsList =
		// accountsService.findByProjectIdAndStates(
		// project.getId(), List.of(AccountsDef.Status.DELAY.getCode()),
		// null, null);
		// 项目已延期
		// if (CollectionUtils.isNotEmpty(accountsList)) {
		// throw new BadRequestException(ErrorCode.CODE_30009035);
		// }
	}

    public void validateAdminCreateSell(AdminOrderSellForm form) {
        projectService.findOne(form.getProjectId()).orElseThrow(
                () -> new BadRequestException(ErrorCode.CODE_30001001));
        Contract contract = contractService.findOne(form.getContractId())
                .orElseThrow(
                        () -> new BadRequestException(ErrorCode.CODE_30001001));
        projectValidator.validateProjectPeople(contract.getProjectId());

        if(!Objects.equals(contract.getContractType(), ContractDef.Type.SELL.getCode())) {
            throw new BadRequestException(ErrorCode.CODE_30009049);
        }

        List<GoodsInfo> list = this.convertGoodsInfo(form.getGoodsInfo());
        this.validateModel(list);
        List<String> ids = new ArrayList<>();
        for (GoodsInfo goodsInfo : list) {
            if (Objects.nonNull(goodsInfo.getRelatedOrderId())) {
                ids.add(goodsInfo.getRelatedOrderId());
            }
        }
        List<Order> orderList = orderService.findByIds(ids);
        if (CollectionUtils.isNotEmpty(orderList)) {
            for (Order order : orderList) {
                if (Objects.nonNull(order.getRelatedBuyOrderId())) {
                    throw new BadRequestException(ErrorCode.CODE_30009036);
                }
            }
        }
    }


    public Order validateAdminUpdateSell(String orderId,AdminOrderSellForm form) {
        Order order = this.validateExist(orderId);
        Contract contract = contractService.findOne(order.getContractId())
                .orElseThrow(
                        () -> new BadRequestException(ErrorCode.CODE_30151001));
        projectValidator.validateProjectPeople(contract.getProjectId());
        List<GoodsInfo> list = this.convertGoodsInfo(form.getGoodsInfo());
        this.validateModel(list);
        List<String> ids = new ArrayList<>();
        for (GoodsInfo goodsInfo : list) {
            if (Objects.nonNull(goodsInfo.getRelatedOrderId())) {
                ids.add(goodsInfo.getRelatedOrderId());
            }
        }
        List<Order> orderList = orderService.findByIds(ids);
        if (CollectionUtils.isNotEmpty(orderList)) {
            for (Order item : orderList) {
                if (Objects.nonNull(item.getRelatedBuyOrderId())
                        && !Objects.equals(item.getRelatedBuyOrderId(), orderId)) {
                    throw new BadRequestException(ErrorCode.CODE_30009036);
                }
            }
        }
        return order;
    }


    public void validateAdminCreateBuy(AdminOrderBuyForm form){
        projectService.findOne(form.getProjectId()).orElseThrow(
                () -> new BadRequestException(ErrorCode.CODE_30001001));
        Contract contract = contractService.findOne(form.getContractId())
                .orElseThrow(
                        () -> new BadRequestException(ErrorCode.CODE_30001001));
        projectValidator.validateProjectPeople(contract.getProjectId());

        if(!Objects.equals(contract.getContractType(), ContractDef.Type.BUY.getCode())) {
            throw new BadRequestException(ErrorCode.CODE_30009050);
        }

        List<GoodsInfo> list = this.convertGoodsInfo(form.getGoodsInfo());
        this.validateModel(list);
        List<String> ids = new ArrayList<>();
        for (GoodsInfo goodsInfo : list) {
            if (Objects.nonNull(goodsInfo.getRelatedOrderId())) {
                ids.add(goodsInfo.getRelatedOrderId());
            }
        }
        List<Order> orderList = orderService.findByIds(ids);
        if (CollectionUtils.isNotEmpty(orderList)) {
            for (Order order : orderList) {
                if (Objects.nonNull(order.getRelatedBuyOrderId())) {
                    throw new BadRequestException(ErrorCode.CODE_30009036);
                }
            }
        }
    }


    public Order validateAdminUpdateBuy(String orderId,AdminOrderBuyForm form) {
        Order order = this.validateExist(orderId);
        Contract contract = contractService.findOne(order.getContractId())
                .orElseThrow(
                        () -> new BadRequestException(ErrorCode.CODE_30151001));
        projectValidator.validateProjectPeople(contract.getProjectId());
        List<GoodsInfo> list = this.convertGoodsInfo(form.getGoodsInfo());
        this.validateModel(list);
        List<String> ids = new ArrayList<>();
        for (GoodsInfo goodsInfo : list) {
            if (Objects.nonNull(goodsInfo.getRelatedOrderId())) {
                ids.add(goodsInfo.getRelatedOrderId());
            }
        }
        List<Order> orderList = orderService.findByIds(ids);
        if (CollectionUtils.isNotEmpty(orderList)) {
            for (Order item : orderList) {
                if (Objects.nonNull(item.getRelatedBuyOrderId())
                        && !Objects.equals(item.getRelatedBuyOrderId(), orderId)) {
                    throw new BadRequestException(ErrorCode.CODE_30009036);
                }
            }
        }
        return order;
    }




	/**
	 * 相同规格请填写一个单价
	 *
	 * @param list
	 */
	private void validateModel(List<GoodsInfo> list) {
		Set<String> modelSet = new HashSet<>();
		Set<BigDecimal> unitPriceSet = new HashSet<>();
		for (GoodsInfo goodsInfo : list) {
			modelSet.add(goodsInfo.getModel());
			unitPriceSet.add(goodsInfo.getUnitPrice());
		}
		if (modelSet.size() < unitPriceSet.size()) {
			throw new BadRequestException(ErrorCode.CODE_30009042);
		}
	}

	public Order validateExist(String id) {
		return orderService.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30009003));
	}

	public Order validateCommit(String id) {
		Order order = this.validateExist(id);
		Contract contract = contractValidator
				.validateExist(order.getContractId());
		projectValidator.validateProjectPeople(contract.getProjectId());
		return order;
	}

	public Order validateAdminWithdraw(String id) {
		Order order = this.validateExist(id);
		Contract contract = contractValidator
				.validateExist(order.getContractId());
		projectValidator.validateProjectPeople(contract.getProjectId());
		if (!(
                BusinessContractDef.SET_STATE.apply(OrderDef.Status.TO_BE_INITIATED.getCode(), BusinessContractDef.CommonSignState.UNSIGNED.getCode()).equals(order.getStatus()) ||
                        BusinessContractDef.SET_STATE.apply(OrderDef.Status.TO_BE_INITIATED.getCode(), BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode()).equals(order.getStatus()))) {
			throw new BadRequestException(ErrorCode.CODE_30151037);
		}
		return order;
	}

	public Order validateRevert(String id) {
		Order order = this.validateExist(id);
		Contract contract = contractValidator
				.validateExist(order.getContractId());
		projectValidator.validateProjectPeople(contract.getProjectId());

		if (OrderDef.SignType.ONLINE.match(order.getSignType())) {
			if (!(BusinessContractDef.SET_STATE.apply(
					OrderDef.Status.SIGNING.getCode(),
					BusinessContractDef.CommonSignState.UNSIGNED.getCode())
					.equals(order.getStatus()) || BusinessContractDef.SET_STATE.apply(
							OrderDef.Status.SIGNING.getCode(),
							BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode())
					.equals(order.getStatus()))) {
				throw new BadRequestException(ErrorCode.CODE_30151019);
			}
		} else {
			if (!BusinessContractDef.GET_HIGH_STATE.apply(order.getStatus())
					.equals(OrderDef.Status.CONFIRMING.getCode())) {
				throw new BadRequestException(ErrorCode.CODE_30151019);
			}
		}

		return order;
	}

	public Order validateCompleteDelivery(String id) {
		Order order = this.validateExist(id);
		Contract contract = contractValidator
				.validateExist(order.getContractId());
		projectValidator.validateProjectPeople(contract.getProjectId());
		deliverGoodsService.findByOrderIds(List.of(id))
				.forEach(deliverGoods -> {
					if (DeliverGoodsDef.Status.WAIT_DELIVER
							.match(deliverGoods.getStatus())
							|| DeliverGoodsDef.Status.DELIVERING
									.match(deliverGoods.getStatus())) {
						throw new BadRequestException(ErrorCode.CODE_30009039);
					}
				});
		return order;
	}

	public Order validateUpdate(String id, OrderUpdateBackendForm form) {
		Order order = this.validateExist(id);
		Contract contract = contractService.findOne(order.getContractId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30151001));
		projectValidator.validateProjectPeople(contract.getProjectId());
		List<GoodsInfo> list = this.convertGoodsInfo(form.getGoodsInfo());
		this.validateModel(list);
		List<String> ids = new ArrayList<>();
		for (GoodsInfo goodsInfo : list) {
			if (Objects.nonNull(goodsInfo.getRelatedOrderId())) {
				ids.add(goodsInfo.getRelatedOrderId());
			}
		}
		List<Order> orderList = orderService.findByIds(ids);
		if (CollectionUtils.isNotEmpty(orderList)) {
			for (Order item : orderList) {
				if (Objects.nonNull(item.getRelatedBuyOrderId())
						&& !Objects.equals(item.getRelatedBuyOrderId(), id)) {
					throw new BadRequestException(ErrorCode.CODE_30009036);
				}
			}
		}
		return order;
	}

	public Order validateInitiateSign(String id, Integer initiator) {
		Order order = this.validateExist(id);
        if(Objects.equals(initiator, CommonDef.AccountSource.INNER.getCode())) {
            if(!BusinessContractDef.GET_HIGH_STATE.apply(order.getStatus())
                    .equals(OrderDef.Status.TO_BE_INITIATED.getCode())) {
                throw new BadRequestException(ErrorCode.CODE_30151017);
            }
        }

        if(Objects.equals(initiator, CommonDef.AccountSource.CUSTOM.getCode())) {
            // 线上才有签署
            if (!OrderDef.SignType.ONLINE.match(order.getSignType())) {
                throw new BadRequestException(ErrorCode.CODE_30151020);
            }
            // 用户：草稿
            if (!BusinessContractDef.GET_HIGH_STATE.apply(order.getStatus())
                    .equals(OrderDef.Status.DRAFT.getCode())) {
                throw new BadRequestException(ErrorCode.CODE_30151017);
            }
        }

		return order;
	}

	public Order validateUpdate(String id, OrderUpdateForm form) {
		Order order = this.validateExist(id);
		projectService.findOne(order.getProjectId()).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30001001));

		Contract contract = contractService.findOne(order.getContractId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30001001));
		if (OrderDef.SignType.ONLINE.match(order.getSignType())) {
			customerValidator
					.validateSealAdmin(contract.getDownstreamPurchasersId());
		}
		List<GoodsInfo> list = this.convertGoodsInfo(form.getGoodsInfo());
		this.validateModel(list);

		// 先货后款合同的账期不能为空
		if (ContractDef.SettleWay.GOODS_FIRST.match(contract.getSettleWay())) {
			if (Objects.isNull(form.getSettlePeriod())) {
				throw new BadRequestException(ErrorCode.CODE_30009033);
			}
			if (form.getSettlePeriod() < 0 || form.getSettlePeriod() > 1000) {
				throw new BadRequestException(ErrorCode.CODE_30009034);
			}
		}
		return order;
	}

	public void validateDelete(String id) {
		Order order = this.validateExist(id);
		projectService.findOne(order.getProjectId()).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService.findOne(order.getContractId())
				.orElse(new Contract());
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findByOrderIds(List.of(id));
		projectValidator.validateProjectPeople(contract.getProjectId());
		// 采购项目
		if (ContractDef.Type.BUY.match(contract.getContractType())) {
			if (CollectionUtils.isNotEmpty(deliverGoodsList)) {
				throw new BadRequestException(ErrorCode.CODE_30009048);
			}
			List<SignReceipt> byOrderIds = signReceiptService
					.findByOrderIds(Collections.singletonList(id));
			if (CollectionUtils.isNotEmpty(byOrderIds)) {
				throw new BadRequestException(ErrorCode.CODE_30009031);
			}
			// 删除采购订单组要校验有没有被付款关联，如果被关联不能删除，提示”该订单已付款，不允许删除“
			List<Payment> payments = paymentService.findByOrderIds(List.of(id),
					null, null);
			if (CollectionUtils.isNotEmpty(payments)) {
				throw new BadRequestException(ErrorCode.CODE_30009041);
			}
		} else {
			if (CollectionUtils.isNotEmpty(deliverGoodsList)) {
				throw new BadRequestException(ErrorCode.CODE_30009047);
			}
			if (!(BusinessContractDef.GET_HIGH_STATE.apply(order.getStatus())
					.equals(OrderDef.Status.DRAFT.getCode())
					|| BusinessContractDef.GET_HIGH_STATE
							.apply(order.getStatus())
							.equals(OrderDef.Status.REVERTED.getCode()))) {
				throw new BadRequestException(ErrorCode.CODE_30009032);
			}
		}
	}

	private List<GoodsInfo> convertGoodsInfo(String data) {
		Gson gson = new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();
		Type listType = new TypeToken<List<GoodsInfo>>() {
		}.getType();
		return gson.fromJson(data, listType);
	}

	public Order validateInvalid(String id, Integer initiator, Boolean flag) {
		Order order = this.validateExist(id);
		if (CommonDef.UserType.INNER.match(initiator) && flag) {
			projectValidator.validateProjectPeople(order.getProjectId());
		}
		if (!OrderDef.Status.FINISHED.match(
				BusinessContractDef.GET_HIGH_STATE.apply(order.getStatus()))) {
			throw new BadRequestException(ErrorCode.CODE_30009046);
		}
		return order;
	}

	/**
	 * 校验作废
	 * 
	 * @param id
	 * @param initiator
	 * @return
	 */
	public Order validateInvalidConfirm(String id, Integer initiator) {
		Order order = this.validateExist(id);
		if (CommonDef.UserType.INNER.match(initiator)) {
			projectValidator.validateProjectPeople(order.getProjectId());
		}
		return order;
	}

}
