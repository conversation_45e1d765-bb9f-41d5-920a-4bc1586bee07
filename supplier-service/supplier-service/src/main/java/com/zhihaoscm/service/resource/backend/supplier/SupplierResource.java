package com.zhihaoscm.service.resource.backend.supplier;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.json.ArrayInteger;
import com.zhihaoscm.domain.bean.entity.Supplier;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.service.core.service.SupplierService;
import com.zhihaoscm.service.resource.validator.supplier.SupplierValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "供应商管理", description = "供应商管理API")
@RestController
@RequestMapping("/backend-supplier")
public class SupplierResource {

	@Autowired
	private SupplierService supplierService;

	@Autowired
	private SupplierValidator supplierValidator;

	@Operation(summary = "查询是否有项目-组织机构变更身份使用")
	@GetMapping("/has-project/{supplierId}")
	@Secured({ AdminPermissionDef.SUPPLY_W })
	public ApiResponse<Boolean> findProject(@PathVariable Long supplierId) {
		return new ApiResponse<>(
				supplierValidator.validatorHasProject(supplierId));
	}

	@Operation(summary = "修改供应链企业身份")
	@PutMapping("/change-role/{supplierId}")
	@Secured({ AdminPermissionDef.SUPPLY_W })
	public ApiResponse<Supplier> delete(@PathVariable Long supplierId,
			@RequestParam List<Integer> role) {
		Supplier supplier = supplierValidator.validateBusiness(supplierId);
		supplier.setRoles(new ArrayInteger(role));
		return new ApiResponse<>(supplierService.updateAllProperties(supplier));
	}

}
