package com.zhihaoscm.service.resource.admin.reconciliation;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.Reconciliation;
import com.zhihaoscm.domain.bean.vo.ReconciliationVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.ReconciliationDef;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.core.service.ReconciliationService;
import com.zhihaoscm.service.resource.form.reconciliation.ReconciliationAssociaForm;
import com.zhihaoscm.service.resource.form.reconciliation.ReconciliationBuyForm;
import com.zhihaoscm.service.resource.form.reconciliation.ReconciliationBuyPredForm;
import com.zhihaoscm.service.resource.form.reconciliation.ReconciliationUpdateBuyForm;
import com.zhihaoscm.service.resource.validator.contract.ContractValidator;
import com.zhihaoscm.service.resource.validator.reconciliation.ReconciliationValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <AUTHOR>
 * @create 2025/1/16
 */
@Tag(name = "采购对账管理", description = "采购对账管理API")
@RestController
@RequestMapping("/reconciliation/buy")
public class ReconciliationBuyResource {

	@Autowired
	private ReconciliationService reconciliationService;

	@Autowired
	private ReconciliationValidator validator;

	@Autowired
	private ContractValidator contractValidator;

	@Operation(summary = "分页查询采购对账")
	@GetMapping(value = "/paging")
	@Secured({ AdminPermissionDef.PROJECT_R, AdminPermissionDef.PROJECT_ALL_R,
			AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<Page<ReconciliationVo>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "排序关键字") @RequestParam(value = "sortKey", required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(value = "sortOrder", required = false) String sortOrder,
			@Parameter(description = "合同名称或对账编号") @RequestParam(value = "param", required = false) String param,
			@Parameter(description = "货物名称") @RequestParam(value = "goodsName", required = false) String goodsName,
			@Parameter(description = "销售方名称") @RequestParam(value = "seller", required = false) String seller,
			@Parameter(description = "对账开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@Parameter(description = "对账结束时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
			@Parameter(description = "对账状态") @RequestParam(value = "states", required = false) List<Integer> states) {
		// 是否有查看所有权限
		boolean hasAll = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.PROJECT_ALL_R
						.getPermission());
		return new ApiResponse<>(
				PageUtil.convert(reconciliationService.buyPaging(page, size,
						sortKey, sortOrder, param, goodsName, seller, beginTime,
						endTime, states, hasAll,
						Objects.requireNonNull(UserContextHolder.getUser())
								.getId())));
	}

	@Operation(summary = "分页-根据项目id,类型查询对账列表")
	@GetMapping("/paging/project-id/type")
	public ApiResponse<Page<ReconciliationVo>> pagingFindByProjectIdAndType(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "排序关键字") @RequestParam(value = "sortKey", required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(value = "sortOrder", required = false) String sortOrder,
			@Parameter(description = "项目id") @RequestParam(value = "projectId") String projectId,
			@Parameter(description = "1 采购 2 销售") @RequestParam(value = "type", required = false) Integer type,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
			@Parameter(description = "合同名称或对账编号") @RequestParam(value = "param", required = false) String param,
			@Parameter(description = "采购方名称") @RequestParam(value = "buyer", required = false) String buyer,
			@Parameter(description = "销售方名称") @RequestParam(value = "seller", required = false) String seller,
			@Parameter(description = "对账状态") @RequestParam(value = "states", required = false) List<Integer> states) {
		return new ApiResponse<>(PageUtil.convert(
				reconciliationService.pagingFindByProjectIdAndType(page, size,
						sortKey, sortOrder, projectId, type, beginTime, endTime,
						null, param, buyer, seller, states)));
	}

	@Operation(summary = "根据项目id和类型查询对账列表合计")
	@GetMapping("/total/project-id/type")
	public ApiResponse<BigDecimal> totalFindByProjectIdAndType(
			@Parameter(description = "项目id") @RequestParam(value = "projectId") String projectId,
			@Parameter(description = "1 采购 2 销售") @RequestParam(value = "type", required = false) Integer type,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
			@Parameter(description = "合同名称或对账编号") @RequestParam(value = "param", required = false) String param,
			@Parameter(description = "采购方名称") @RequestParam(value = "buyer", required = false) String buyer,
			@Parameter(description = "销售方名称") @RequestParam(value = "seller", required = false) String seller,
			@Parameter(description = "对账状态") @RequestParam(value = "states", required = false) List<Integer> states) {
		return new ApiResponse<>(reconciliationService
				.totalFindByProjectIdAndType(projectId, type, beginTime,
						endTime, null, param, buyer, seller, states)
				.orElse(null));
	}

	@Operation(summary = "分页-根据订单id,类型查询对账列表")
	@GetMapping("/paging/order-id/type")
	public ApiResponse<Page<ReconciliationVo>> pagingFindByOrderIdAndType(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "排序关键字") @RequestParam(value = "sortKey", required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(value = "sortOrder", required = false) String sortOrder,
			@Parameter(description = "订单id") @RequestParam(value = "orderId") String orderId,
			@Parameter(description = "1 采购 2 销售") @RequestParam(value = "type") Integer type,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
		return new ApiResponse<>(PageUtil.convert(reconciliationService
				.pagingFindByOrderIdAndType(page, size, sortKey, sortOrder,
						orderId, type, beginTime, endTime, null)));
	}

	@Operation(summary = "根据订单id和类型查询对账列表合计")
	@GetMapping("/total/order-id/type")
	public ApiResponse<BigDecimal> totalFindByOrderIdAndType(
			@Parameter(description = "订单id") @RequestParam(value = "orderId") String orderId,
			@Parameter(description = "1 采购 2 销售") @RequestParam(value = "type") Integer type,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
		return new ApiResponse<>(
				reconciliationService.totalFindByOrderIdAndType(orderId, type,
						beginTime, endTime, null).orElse(null));
	}

	/**
	 * <AUTHOR>
	 */
	@Operation(summary = "付款管理-关联对账单下拉列表")
	@GetMapping("/selector-associated")
	public ApiResponse<Page<Reconciliation>> selectorAssociated(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "项目id") @RequestParam String projectId,
			@Parameter(description = "合同编号") @RequestParam String contractId,
			@Parameter(description = "对账编号") @RequestParam(required = false) String reconciliationId) {
		return new ApiResponse<>(
				PageUtil.convert(reconciliationService.selectorAssociated(page,
						size, projectId, contractId, reconciliationId)));
	}

	@Operation(summary = "根据id查询对账单信息")
	@GetMapping("/vo/{id}")
	@Secured({ AdminPermissionDef.PROJECT_R, AdminPermissionDef.PROJECT_ALL_R,
			AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<ReconciliationVo> findVoById(@PathVariable String id) {
		ReconciliationVo vo = reconciliationService.findBuyVoById(id)
				.orElse(null);
		return new ApiResponse<>(vo);
	}

	@Operation(summary = "新增")
	@Secured(AdminPermissionDef.PROJECT_DEAL)
	@PostMapping
	public ApiResponse<Reconciliation> create(
			@Validated @RequestBody ReconciliationBuyForm form) {
		return new ApiResponse<>(
				reconciliationService
						.createBuy(
								validator.validateCreateBuy(form,
										CommonDef.AccountSource.INNER
												.getCode()),
								form.getDeliverGoodsList(),
								form.getDeliverGoodsVoList())
						.orElse(null));
	}

	@Operation(summary = "修改")
	@PutMapping("/{id}")
	@Secured(AdminPermissionDef.PROJECT_DEAL)
	public ApiResponse<Reconciliation> update(@PathVariable("id") String id,
			@Validated @RequestBody ReconciliationUpdateBuyForm form) {
		Reconciliation reconciliation = validator.validateUpdateBuy(id, form,
				CommonDef.AccountSource.INNER.getCode());
		Reconciliation newReconciliation = reconciliationService
				.updateBuy(reconciliation, form.getDeliverGoodsList(),
						form.getDeliverGoodsVoList(), form.getSaveType(),
						CommonDef.AccountSource.INNER.getCode())
				.orElse(null);
		return new ApiResponse<>(newReconciliation);
	}

	@Operation(summary = "预对账完成之后新增修改对账")
	@PutMapping("/update/reconciliation/{id}")
	@Secured(AdminPermissionDef.PROJECT_DEAL)
	public ApiResponse<Reconciliation> updateBuyReconciliation(
			@PathVariable("id") String id,
			@Validated @RequestBody ReconciliationBuyPredForm form) {
		Reconciliation reconciliation = validator
				.validateUpdateBuyReconciliation(id, form,
						CommonDef.AccountSource.INNER.getCode());
		return new ApiResponse<>(reconciliationService.updateBuyReconciliation(
				reconciliation, form.getDeliverGoodsList(),
				form.getDeliverGoodsVoList()).orElse(null));
	}

	@Operation(summary = "删除")
	@DeleteMapping("/{id}")
	@Secured({ AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<Void> deleteBuy(@PathVariable String id) {
		reconciliationService.deleteBuy(validator.validateDelete(id));
		return new ApiResponse<>();
	}

	/**
	 * <AUTHOR>
	 */
	@Operation(summary = "关联对账单")
	@PostMapping("/contract/{id}")
	public ApiResponse<List<Reconciliation>> findByContractId(
			@PathVariable String id,
			@RequestBody(required = false) ReconciliationAssociaForm form) {
		contractValidator.validateExist(id);
		return new ApiResponse<>(reconciliationService.findByContractId(id,
				form, ReconciliationDef.Type.BUY.getCode()));
	}

}
