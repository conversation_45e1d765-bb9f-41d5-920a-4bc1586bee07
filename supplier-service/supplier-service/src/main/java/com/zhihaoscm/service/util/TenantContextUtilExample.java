package com.zhihaoscm.service.util;

/**
 * <p>TenantContextUtil 使用示例和说明</p>
 * <p>展示如何使用 TenantContextUtil 在不同租户上下文中执行操作</p>
 *
 * <AUTHOR>
 * @since 2024-07-31
 */
public class TenantContextUtilExample {

    /**
     * 使用说明：
     *
     * 1. 最简单的用法 - 直接替换您的原代码：
     *
     * 原代码：
     * UserVo userVo = userService.findByMobile(form.getMobile()).orElse(null);
     *
     * 新代码（Lambda方式）：
     * UserVo userVo = TenantContextUtil.executeWithoutTenant(() ->
     *     userService.findByMobile(form.getMobile()).orElse(null)
     * );
     *
     * 新代码（方法引用方式，推荐）：
     * UserVo userVo = TenantContextUtil.executeWithoutTenant(
     *     userService::findByMobile, form.getMobile()
     * ).orElse(null);
     *
     * 2. 保持Optional类型：
     * Optional<UserVo> userVoOptional = TenantContextUtil.executeWithoutTenant(
     *     userService::findByMobile, form.getMobile()
     * );
     *
     * 3. 在指定租户上下文中执行：
     * UserVo userVo = TenantContextUtil.executeWithTenant(tenantId, () ->
     *     userService.findByMobile(mobile).orElse(null)
     * );
     *
     * 4. 无返回值的操作：
     * TenantContextUtil.executeWithoutTenant(() -> {
     *     userService.updateUser(user);
     *     // 其他操作...
     * });
     *
     * 5. 复杂业务逻辑：
     * UserVo result = TenantContextUtil.executeWithoutTenant(() -> {
     *     Optional<UserVo> userOptional = userService.findByMobile(mobile);
     *     if (userOptional.isPresent()) {
     *         UserVo userVo = userOptional.get();
     *         // 执行其他业务逻辑
     *         doSomethingElse(userVo);
     *         return userVo;
     *     }
     *     return null;
     * });
     *
     * 6. 多参数方法调用：
     * UserVo userVo = TenantContextUtil.executeWithoutTenant(() ->
     *     userService.findByMobileAndOrigin(mobile, origin).orElse(null)
     * );
     *
     * 7. 链式调用：
     * String userName = TenantContextUtil.executeWithoutTenant(() ->
     *     userService.findByMobile(mobile)
     *         .map(userVo -> userVo.getUser().getName())
     *         .orElse("未找到用户")
     * );
     */

    /**
     * 实际使用示例方法
     */
    public static void usageExamples() {
        // 这里只是展示用法，实际使用时请替换为您的具体代码

        /*
        // 示例1：最常用的场景
        UserVo userVo = TenantContextUtil.executeWithoutTenant(() ->
            userService.findByMobile(form.getMobile()).orElse(null)
        );

        // 示例2：方法引用方式（推荐）
        UserVo userVo2 = TenantContextUtil.executeWithoutTenant(
            userService::findByMobile, form.getMobile()
        ).orElse(null);

        // 示例3：在特定租户上下文中执行
        UserVo userVo3 = TenantContextUtil.executeWithTenant(1L, () ->
            userService.findByMobile(mobile).orElse(null)
        );

        // 示例4：无返回值操作
        TenantContextUtil.executeWithoutTenant(() -> {
            userService.updateUserStatus(userId, status);
        });
        */
    }
}
