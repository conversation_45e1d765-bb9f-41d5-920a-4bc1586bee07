package com.zhihaoscm.service.core.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.api.util.multipart.file.CustomMultipartFile;
import com.zhihaoscm.common.api.util.multipart.file.MultipartFileUtils;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.ContractPageResponse;
import com.zhihaoscm.domain.bean.dto.DeliverDto;
import com.zhihaoscm.domain.bean.dto.GeneratPdfDto;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.GoodsInfo;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.OrderVo;
import com.zhihaoscm.domain.bean.vo.SignReceiptCountVo;
import com.zhihaoscm.domain.bean.vo.SignReceiptVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.PdfUtils;
import com.zhihaoscm.domain.utils.ThreadPoolUtil;
import com.zhihaoscm.service.config.LocalDateTimeAdapter;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.SignReceiptMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.AdminSealService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: 签收服务实现类
 * @author: 彭湃
 * @date: 2025/1/13 15:47
 **/
@Slf4j
@Service
public class SignReceiptServiceImpl
		extends MpStringIdBaseServiceImpl<SignReceipt, SignReceiptMapper>
		implements SignReceiptService {

	@Autowired
	private DeliverGoodsService deliverGoodsService;
	@Autowired
	private ProjectService projectService;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private OrderService orderService;
	@Autowired
	private ContractService contractService;
	@Autowired
	private ContractRecordService contractRecordService;
	@Autowired
	private FileService fileService;
	@Autowired
	private ReconciliationService reconciliationService;
	@Autowired
	private AdminSealService adminSealService;
	@Autowired
	private TransportOrderShipService transportOrderShipService;
	@Autowired
	private TransportOrderVehicleService transportOrderVehicleService;
	@Autowired
	private TransportOrderRailwayService transportOrderRailwayService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private OutboundService outboundService;
	@Autowired
	private InboundService inboundService;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	public SignReceiptServiceImpl(SignReceiptMapper repository) {
		super(repository);
	}

	@Override
	public Page<SignReceiptVo> adminBuyPaging(Integer page, Integer size,
			String key, String sellerName, String goodsName,
			List<Integer> status, List<Integer> reconciliationStatus,
			String startTime, String endTime, String sortKey, String sortOrder,
			String orderId, String projectId, boolean hasAll, Long userId) {
		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		wrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (Objects.nonNull(userId)) {
			if (!hasAll) {
				// 处理人是自己在的
				List<String> projectIdList = projectService.findByUserId(userId,
						null);
				if (org.apache.commons.collections4.CollectionUtils
						.isNotEmpty(projectIdList)) {
					wrapper.in(SignReceipt::getProjectId, projectIdList);
				} else {
					return Page.of(page, size, 0);
				}
			}
		}
		if (Objects.nonNull(key)) {
			List<String> projectIds = projectService.findByNameLike(key)
					.stream().map(Project::getId).distinct().toList();
			wrapper.and(x -> x.like(SignReceipt::getId, key).or().in(
					CollectionUtils.isNotEmpty(projectIds),
					SignReceipt::getProjectId, projectIds));
		}
		// 根据销售方名称及货物名称查询
		List<Contract> contracts;
		contracts = contractService.find(null, goodsName,
				ContractDef.ContractType.PURCHASE.getCode());
		if (CollectionUtils.isEmpty(contracts)) {
			return new Page<>();
		}

		if (CollectionUtils.isNotEmpty(contracts)) {
			wrapper.in(SignReceipt::getContractId,
					contracts.stream().map(Contract::getId).toList());
		}

		if (StringUtils.isNotBlank(sellerName)) {
			wrapper.and(i -> i.apply(StringUtils.isNoneBlank(sellerName),
					"(JSON_EXTRACT(seller_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					sellerName));
		}

		wrapper.like(Objects.nonNull(orderId), SignReceipt::getRelatedOrderIds,
				orderId);

		wrapper.eq(SignReceipt::getType, SignReceiptDef.Type.BUY.getCode());

		wrapper.eq(Objects.nonNull(projectId), SignReceipt::getProjectId,
				projectId);

		wrapper.in(CollectionUtils.isNotEmpty(reconciliationStatus),
				SignReceipt::getReconciliationStatus, reconciliationStatus);
		if (CollectionUtils.isNotEmpty(reconciliationStatus)) {
			wrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.FINISHED.getCode());
		}

		if (CollectionUtils.isNotEmpty(status)) {
			wrapper.and(x -> {
				for (Integer state : status) {
					SignReceiptDef.Status from = SignReceiptDef.Status
							.from(state);
					switch (from) {
						case DRAFT, REVERTED, CONFIRMING ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getInitiator,
									CommonDef.AccountSource.CUSTOM.getCode()));
						case INVALID, INVALIDING, FINISHED, TO_BE_INITIATE ->
							x.or(y -> y.eq(SignReceipt::getStatus, state));
						case TO_BE_CONFIRMED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getInitiator,
									CommonDef.AccountSource.INNER.getCode()));
						case SIGNING ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode()));
						case TO_BE_SIGNED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).in(
									SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode()));
					}
				}
			});
		} else {
			wrapper.and(x -> x
					.in(SignReceipt::getStatus,
							SignReceiptDef.Status.INVALID.getCode(),
							SignReceiptDef.Status.INVALIDING.getCode(),
							SignReceiptDef.Status.FINISHED.getCode(),
							SignReceiptDef.Status.SIGNING.getCode(),
							SignReceiptDef.Status.TO_BE_SIGNED.getCode(),
							SignReceiptDef.Status.CONFIRMING.getCode(),
							SignReceiptDef.Status.TO_BE_INITIATE.getCode())
					.or(y -> y
							.in(SignReceipt::getStatus,
									SignReceiptDef.Status.DRAFT.getCode(),
									SignReceiptDef.Status.REVERTED.getCode())
							.eq(SignReceipt::getInitiator,
									CommonDef.AccountSource.CUSTOM.getCode())));
		}

		wrapper.ge(Objects.nonNull(startTime), SignReceipt::getSignConfirmDate,
				startTime);
		wrapper.le(Objects.nonNull(endTime), SignReceipt::getSignConfirmDate,
				endTime);

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + " ,id desc");
		} else {
			// 默认按照更新时间降序排列
			wrapper.last("order by updated_time desc");
		}

		Page<SignReceipt> paging = repository.selectPage(new Page<>(page, size),
				wrapper);

		List<SignReceiptVo> vos = this.packVos(paging.getRecords(), contracts,
				orderId);
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<SignReceiptVo> adminSalePaging(Integer page, Integer size,
			String key, String purchaserName, String goodsName,
			List<Integer> status, List<Integer> reconciliationStatus,
			String startTime, String endTime, String sortKey, String sortOrder,
			String orderId, String projectId, boolean hasAll, Long userId) {
		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		wrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (Objects.nonNull(userId)) {
			if (!hasAll) {
				// 处理人是自己在的
				List<String> projectIdList = projectService.findByUserId(userId,
						null);
				if (org.apache.commons.collections4.CollectionUtils
						.isNotEmpty(projectIdList)) {
					wrapper.in(SignReceipt::getProjectId, projectIdList);
				} else {
					return Page.of(page, size, 0);
				}
			}
		}
		if (Objects.nonNull(key)) {
			List<String> projectIds = projectService.findByNameLike(key)
					.stream().map(Project::getId).distinct().toList();
			wrapper.and(x -> x.like(SignReceipt::getId, key).or().in(
					CollectionUtils.isNotEmpty(projectIds),
					SignReceipt::getProjectId, projectIds));
		}
		// 根据销售方名称及货物名称查询
		List<Contract> contracts;
		contracts = contractService.find(null, goodsName,
				ContractDef.ContractType.SALES.getCode());
		if (CollectionUtils.isEmpty(contracts)) {
			return new Page<>();
		}

		if (CollectionUtils.isNotEmpty(contracts)) {
			wrapper.in(SignReceipt::getContractId,
					contracts.stream().map(Contract::getId).toList());
		}

		if (StringUtils.isNotBlank(purchaserName)) {
			wrapper.and(i -> i.apply(StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName));
		}

		wrapper.like(Objects.nonNull(orderId), SignReceipt::getRelatedOrderIds,
				orderId);

		wrapper.eq(SignReceipt::getType, SignReceiptDef.Type.SELL.getCode());

		wrapper.eq(Objects.nonNull(projectId), SignReceipt::getProjectId,
				projectId);

		wrapper.in(CollectionUtils.isNotEmpty(reconciliationStatus),
				SignReceipt::getReconciliationStatus, reconciliationStatus);
		if (CollectionUtils.isNotEmpty(reconciliationStatus)) {
			wrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.FINISHED.getCode());
		}

		if (CollectionUtils.isNotEmpty(status)) {
			wrapper.and(x -> {
				for (Integer state : status) {
					SignReceiptDef.Status from = SignReceiptDef.Status
							.from(state);
					switch (from) {
						case DRAFT, REVERTED, CONFIRMING ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getInitiator,
									CommonDef.AccountSource.INNER.getCode()));
						case INVALID, INVALIDING, FINISHED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state));
						case TO_BE_CONFIRMED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getInitiator,
									CommonDef.AccountSource.CUSTOM.getCode()));
						case SIGNING ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getStatus,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode()));
						case TO_BE_SIGNED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).in(
									SignReceipt::getStatus,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode()));
					}
				}
			});
		} else {
			wrapper.and(x -> x
					.in(SignReceipt::getStatus,
							SignReceiptDef.Status.INVALID.getCode(),
							SignReceiptDef.Status.INVALIDING.getCode(),
							SignReceiptDef.Status.FINISHED.getCode(),
							SignReceiptDef.Status.SIGNING.getCode(),
							SignReceiptDef.Status.TO_BE_SIGNED.getCode(),
							SignReceiptDef.Status.CONFIRMING.getCode())
					.or(y -> y
							.in(SignReceipt::getStatus,
									SignReceiptDef.Status.DRAFT.getCode(),
									SignReceiptDef.Status.REVERTED.getCode())
							.eq(SignReceipt::getInitiator,
									CommonDef.AccountSource.INNER.getCode())));
		}

		wrapper.ge(Objects.nonNull(startTime), SignReceipt::getSignConfirmDate,
				startTime);
		wrapper.le(Objects.nonNull(endTime), SignReceipt::getSignConfirmDate,
				endTime);

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + " ,id desc");
		} else {
			// 默认按照更新时间降序排列
			wrapper.last("order by updated_time desc");
		}

		Page<SignReceipt> paging = repository.selectPage(new Page<>(page, size),
				wrapper);

		List<SignReceiptVo> vos = this.packVos(paging.getRecords(), contracts,
				orderId);
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<SignReceiptVo> customBuyPaging(Integer page, Integer size,
			String key, String goodsName, List<Integer> status,
			List<Integer> reconciliationStatus, String startTime,
			String endTime, Long customerId, String sortKey, String sortOrder,
			String orderId, String purchaserName, String projectId) {
		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		wrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.eq(SignReceipt::getPurchaserId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		if (Objects.nonNull(key)) {
			wrapper.and(i -> i.like(SignReceipt::getId, key).or()
					.like(SignReceipt::getContractName, key));
		}

		// 根据货物名称查询
		List<Contract> contracts = contractService.find(null, goodsName,
				ContractDef.ContractType.SALES.getCode(), null);
		if (CollectionUtils.isEmpty(contracts)) {
			return new Page<>();
		}
		if (CollectionUtils.isNotEmpty(contracts)) {
			wrapper.in(SignReceipt::getContractId,
					contracts.stream().map(Contract::getId).toList());
		}

		if (Objects.nonNull(orderId)) {
			List<DeliverGoods> deliverGoods = deliverGoodsService
					.findByOrderIds(Collections.singletonList(orderId));
			if (CollectionUtils.isNotEmpty(deliverGoods)) {
				List<String> deliverGoodsIds = deliverGoods.stream()
						.map(DeliverGoods::getId).collect(Collectors.toList());
				// 动态添加 JSON_CONTAINS 条件
				if (CollectionUtils.isNotEmpty(deliverGoodsIds)) {
					if (deliverGoodsIds.size() > 1) {
						wrapper.and(e -> {
							for (String id : deliverGoodsIds) {
								e.or().apply(
										"JSON_CONTAINS(related_deliver_goods_ids, JSON_QUOTE({0}))",
										id);
							}
						});
					} else {
						wrapper.and(e -> e.apply(
								"JSON_CONTAINS(related_deliver_goods_ids, JSON_QUOTE({0}))",
								deliverGoodsIds.get(0)));
					}
				}
			} else {
				return new Page<>();
			}
		}

		wrapper.eq(Objects.nonNull(projectId), SignReceipt::getProjectId,
				projectId);

		if (CollectionUtils.isNotEmpty(status)) {
			wrapper.and(x -> {
				for (Integer state : status) {
					SignReceiptDef.Status from = SignReceiptDef.Status
							.from(state);
					switch (from) {
						case DRAFT, REVERTED, CONFIRMING ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getInitiator,
									CommonDef.AccountSource.CUSTOM.getCode()));
						case INVALID, INVALIDING, FINISHED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state));
						case TO_BE_CONFIRMED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getInitiator,
									CommonDef.AccountSource.INNER.getCode()));
						case SIGNING ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode()));
						case TO_BE_SIGNED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).in(
									SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode()));
					}
				}
			});
		} else {
			wrapper.and(x -> x
					.in(SignReceipt::getStatus,
							SignReceiptDef.Status.INVALID.getCode(),
							SignReceiptDef.Status.INVALIDING.getCode(),
							SignReceiptDef.Status.FINISHED.getCode(),
							SignReceiptDef.Status.SIGNING.getCode(),
							SignReceiptDef.Status.TO_BE_SIGNED.getCode(),
							SignReceiptDef.Status.CONFIRMING.getCode())
					.or(y -> y
							.in(SignReceipt::getStatus,
									SignReceiptDef.Status.DRAFT.getCode(),
									SignReceiptDef.Status.REVERTED.getCode())
							.eq(SignReceipt::getInitiator,
									CommonDef.AccountSource.CUSTOM.getCode())));
		}

		wrapper.in(CollectionUtils.isNotEmpty(reconciliationStatus),
				SignReceipt::getReconciliationStatus, reconciliationStatus);
		if (CollectionUtils.isNotEmpty(reconciliationStatus)) {
			wrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.FINISHED.getCode());
		}
		wrapper.ge(Objects.nonNull(startTime), SignReceipt::getSignConfirmDate,
				startTime);
		wrapper.le(Objects.nonNull(endTime), SignReceipt::getSignConfirmDate,
				endTime);

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + " ,id desc");
		} else {
			// 默认按照创建时间降序排列
			wrapper.last("order by updated_time desc");
		}

		Page<SignReceipt> paging = repository.selectPage(new Page<>(page, size),
				wrapper);

		List<SignReceiptVo> vos = this.packVos(paging.getRecords(), contracts,
				orderId);
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<SignReceiptVo> customSalePaging(Integer page, Integer size,
			String key, String goodsName, List<Integer> status,
			List<Integer> reconciliationStatus, String startTime,
			String endTime, Long customerId, String sortKey, String sortOrder,
			String purchaserName, String orderId, String projectId) {
		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		wrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.eq(SignReceipt::getSellerId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		if (Objects.nonNull(key)) {
			wrapper.and(i -> i.like(SignReceipt::getId, key).or()
					.like(SignReceipt::getContractName, key));
		}

		// 根据货物名称查询
		List<Contract> contracts = contractService.find(null, goodsName,
				ContractDef.ContractType.PURCHASE.getCode(), null);
		if (CollectionUtils.isEmpty(contracts)) {
			return new Page<>();
		}
		if (CollectionUtils.isNotEmpty(contracts)) {
			wrapper.in(SignReceipt::getContractId,
					contracts.stream().map(Contract::getId).toList());
		}

		if (Objects.nonNull(orderId)) {
			List<DeliverGoods> deliverGoods = deliverGoodsService
					.findByOrderIds(Collections.singletonList(orderId));
			if (CollectionUtils.isNotEmpty(deliverGoods)) {
				List<String> deliverGoodsIds = deliverGoods.stream()
						.map(DeliverGoods::getId).collect(Collectors.toList());
				// 动态添加 JSON_CONTAINS 条件
				if (CollectionUtils.isNotEmpty(deliverGoodsIds)) {
					if (deliverGoodsIds.size() > 1) {
						wrapper.and(e -> {
							for (String id : deliverGoodsIds) {
								e.or().apply(
										"JSON_CONTAINS(related_deliver_goods_ids, JSON_QUOTE({0}))",
										id);
							}
						});
					} else {
						wrapper.and(e -> e.apply(
								"JSON_CONTAINS(related_deliver_goods_ids, JSON_QUOTE({0}))",
								deliverGoodsIds.get(0)));
					}
				}
			} else {
				return new Page<>();
			}
		}

		wrapper.eq(Objects.nonNull(projectId), SignReceipt::getProjectId,
				projectId);

		if (StringUtils.isNotBlank(purchaserName)) {
			wrapper.and(i -> i.apply(StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName));
		}

		if (CollectionUtils.isNotEmpty(status)) {
			wrapper.and(x -> {
				for (Integer state : status) {
					SignReceiptDef.Status from = SignReceiptDef.Status
							.from(state);
					switch (from) {
						case DRAFT, REVERTED, CONFIRMING ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getInitiator,
									CommonDef.AccountSource.INNER.getCode()));
						case INVALID, INVALIDING, FINISHED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state));
						case TO_BE_CONFIRMED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getInitiator,
									CommonDef.AccountSource.CUSTOM.getCode()));
						case SIGNING ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode()));
						case TO_BE_SIGNED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).in(
									SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode()));
					}
				}
			});
		} else {
			wrapper.and(x -> x
					.in(SignReceipt::getStatus,
							SignReceiptDef.Status.INVALID.getCode(),
							SignReceiptDef.Status.INVALIDING.getCode(),
							SignReceiptDef.Status.FINISHED.getCode(),
							SignReceiptDef.Status.SIGNING.getCode(),
							SignReceiptDef.Status.TO_BE_SIGNED.getCode(),
							SignReceiptDef.Status.CONFIRMING.getCode())
					.or(y -> y
							.in(SignReceipt::getStatus,
									SignReceiptDef.Status.DRAFT.getCode(),
									SignReceiptDef.Status.REVERTED.getCode())
							.eq(SignReceipt::getInitiator,
									CommonDef.AccountSource.INNER.getCode())));
		}

		wrapper.in(CollectionUtils.isNotEmpty(reconciliationStatus),
				SignReceipt::getReconciliationStatus, reconciliationStatus);
		if (CollectionUtils.isNotEmpty(reconciliationStatus)) {
			wrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.FINISHED.getCode());
		}
		wrapper.ge(Objects.nonNull(startTime), SignReceipt::getSignConfirmDate,
				startTime);
		wrapper.le(Objects.nonNull(endTime), SignReceipt::getSignConfirmDate,
				endTime);

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + " ,id desc");
		} else {
			// 默认按照创建时间降序排列
			wrapper.last("order by updated_time desc");
		}

		Page<SignReceipt> paging = repository.selectPage(new Page<>(page, size),
				wrapper);

		List<SignReceiptVo> vos = this.packVos(paging.getRecords(), contracts,
				orderId);
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<SignReceiptVo> customPaging(Integer page, Integer size,
			String key, String purchaserName, String goodsName,
			Integer projectType, List<Integer> status,
			List<Integer> reconciliationStatus, String startTime,
			String endTime, Long customerId, String sortKey, String sortOrder,
			String orderId, String projectId) {

		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		wrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		if (Objects.nonNull(key)) {
			wrapper.and(i -> i.like(SignReceipt::getId, key).or()
					.like(SignReceipt::getContractName, key));
		}
		// 根据采购方名称及货物名称查询
		List<Contract> contracts;
		contracts = contractService.customerFind(purchaserName, goodsName, null,
				projectType, customerId);
		if (CollectionUtils.isEmpty(contracts)) {
			return new Page<>();
		}
		if (CollectionUtils.isNotEmpty(contracts)) {
			wrapper.in(SignReceipt::getContractId,
					contracts.stream().map(Contract::getId).toList());
		}

		if (Objects.nonNull(orderId)) {
			List<DeliverGoods> deliverGoods = deliverGoodsService
					.findByOrderIds(Collections.singletonList(orderId));
			if (CollectionUtils.isNotEmpty(deliverGoods)) {
				List<String> deliverGoodsIds = deliverGoods.stream()
						.map(DeliverGoods::getId).collect(Collectors.toList());
				// 动态添加 JSON_CONTAINS 条件
				if (CollectionUtils.isNotEmpty(deliverGoodsIds)) {
					if (deliverGoodsIds.size() > 1) {
						wrapper.and(e -> {
							for (String id : deliverGoodsIds) {
								e.or().apply(
										"JSON_CONTAINS(related_deliver_goods_ids, JSON_QUOTE({0}))",
										id);
							}
						});
					} else {
						wrapper.and(e -> e.apply(
								"JSON_CONTAINS(related_deliver_goods_ids, JSON_QUOTE({0}))",
								deliverGoodsIds.get(0)));
					}
				}
			} else {
				return new Page<>();
			}
		}

		wrapper.eq(Objects.nonNull(projectId), SignReceipt::getProjectId,
				projectId);

		List<Integer> toBeSignedList = new ArrayList<>();
		toBeSignedList.add(
				BusinessContractDef.CommonSignState.SUPPLY_SIGNED.getCode());
		toBeSignedList
				.add(BusinessContractDef.CommonSignState.UNSIGNED.getCode());

		// 只有销售项目才需要判断状态
		if (ContractDef.Type.SELL.match(projectType)) {
			// 查询所有数据，则查询除了pc端草稿和pc端驳回状态的所有状态
			if (CollectionUtils.isEmpty(status)) {
				wrapper.and(x -> x
						.eq(SignReceipt::getInitiator,
								CommonDef.AccountSource.CUSTOM.getCode())
						.in(SignReceipt::getStatus,
								SignReceiptDef.Status.DRAFT.getCode(),
								SignReceiptDef.Status.INVALIDING.getCode(),
								SignReceiptDef.Status.INVALID.getCode(),
								SignReceiptDef.Status.CONFIRMING.getCode(),
								SignReceiptDef.Status.REVERTED.getCode(),
								SignReceiptDef.Status.SIGNING.getCode(),
								SignReceiptDef.Status.FINISHED.getCode())
						.or(y -> y
								.eq(SignReceipt::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.in(SignReceipt::getStatus,
										SignReceiptDef.Status.CONFIRMING
												.getCode(),
										SignReceiptDef.Status.SIGNING.getCode(),
										SignReceiptDef.Status.INVALIDING
												.getCode(),
										SignReceiptDef.Status.INVALID.getCode(),
										SignReceiptDef.Status.FINISHED
												.getCode())));
			} else {
				// 如果没有包含草稿和驳回状态，则直接查询
				if (!status.contains(SignReceiptDef.Status.DRAFT.getCode())
						&& !status.contains(
								SignReceiptDef.Status.REVERTED.getCode())) {
					boolean b = status.removeIf(
							SignReceiptDef.Status.TO_BE_SIGNED::match);
					boolean d = status
							.removeIf(SignReceiptDef.Status.SIGNING::match);
					boolean j = status.removeIf(
							SignReceiptDef.Status.TO_BE_CONFIRMED::match);
					boolean k = status
							.removeIf(SignReceiptDef.Status.CONFIRMING::match);
					// 是否包含待签署状态
					if (b && d) {
						handleStatus(wrapper, status, j, k,
								CommonDef.AccountSource.INNER.getCode(),
								CommonDef.AccountSource.CUSTOM.getCode());
					} else if (!b && !d) {
						handleStatus1(wrapper, status, j, k,
								CommonDef.AccountSource.INNER.getCode(),
								CommonDef.AccountSource.CUSTOM.getCode());
					} else if (b) {
						handleToBeSignedStatus(wrapper, status, j, k,
								toBeSignedList,
								CommonDef.AccountSource.INNER.getCode(),
								CommonDef.AccountSource.CUSTOM.getCode());
					} else {
						handleSigningStatus(wrapper, status, j, k,
								CommonDef.AccountSource.INNER.getCode(),
								CommonDef.AccountSource.CUSTOM.getCode());
					}
				} else {
					// 如果包含草稿和驳回状态，则需要判断发起方，如果是内部发起，则查询所有状态，如果是客户发起，则查询除了草稿和驳回状态的所有状态
					List<Integer> copiedList = new ArrayList<>(status);
					copiedList.removeIf(e -> e
							.equals(SignReceiptDef.Status.DRAFT.getCode())
							|| e.equals(
									SignReceiptDef.Status.REVERTED.getCode()));
					boolean b = copiedList.removeIf(
							SignReceiptDef.Status.TO_BE_SIGNED::match);
					boolean d = copiedList
							.removeIf(SignReceiptDef.Status.SIGNING::match);
					// 查询待确认的状态，则添加另一方的确认中状态
					boolean j = copiedList.removeIf(
							SignReceiptDef.Status.TO_BE_CONFIRMED::match);
					boolean k = copiedList
							.removeIf(SignReceiptDef.Status.CONFIRMING::match);
					if ((!b && !d) || (b && d)) {
						// 如果没有包含待签署和签署中状态，则直接查询
						if (!j && !k) {
							addCommonConditions1(wrapper, status, copiedList,
									CommonDef.AccountSource.CUSTOM.getCode(),
									CommonDef.AccountSource.INNER.getCode());
						} else if (j && k) {
							status.add(
									SignReceiptDef.Status.CONFIRMING.getCode());
							copiedList.add(
									SignReceiptDef.Status.CONFIRMING.getCode());
							addCommonConditions1(wrapper, status, copiedList,
									CommonDef.AccountSource.CUSTOM.getCode(),
									CommonDef.AccountSource.INNER.getCode());
						} else if (j) {
							addCommonConditions1(wrapper, status, copiedList,
									CommonDef.AccountSource.CUSTOM.getCode(),
									CommonDef.AccountSource.INNER.getCode());
							wrapper.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											CommonDef.AccountSource.INNER
													.getCode()));
						} else {
							addCommonConditions1(wrapper, status, copiedList,
									CommonDef.AccountSource.CUSTOM.getCode(),
									CommonDef.AccountSource.INNER.getCode());
							wrapper.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											CommonDef.AccountSource.CUSTOM
													.getCode()));
						}
					} else if (b) {
						// 只包含待签署状态
						if (!j && !k) {
							addConditions(wrapper, status, copiedList,
									toBeSignedList,
									CommonDef.AccountSource.CUSTOM.getCode(),
									CommonDef.AccountSource.INNER.getCode());
						} else if (j && k) {
							status.add(
									SignReceiptDef.Status.CONFIRMING.getCode());
							copiedList.add(
									SignReceiptDef.Status.CONFIRMING.getCode());
							addConditions(wrapper, status, copiedList,
									toBeSignedList,
									CommonDef.AccountSource.CUSTOM.getCode(),
									CommonDef.AccountSource.INNER.getCode());
						} else if (j) {
							addConditions(wrapper, status, copiedList,
									toBeSignedList,
									CommonDef.AccountSource.CUSTOM.getCode(),
									CommonDef.AccountSource.INNER.getCode());
							wrapper.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											CommonDef.AccountSource.INNER
													.getCode()));
						} else {
							addConditions(wrapper, status, copiedList,
									toBeSignedList,
									CommonDef.AccountSource.CUSTOM.getCode(),
									CommonDef.AccountSource.INNER.getCode());
							wrapper.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											CommonDef.AccountSource.CUSTOM
													.getCode()));
						}
					} else {
						// 只包含签署中状态
						if (!j && !k) {
							addCommonConditions(wrapper, status, copiedList,
									CommonDef.AccountSource.CUSTOM.getCode(),
									CommonDef.AccountSource.INNER.getCode());
						} else if (j && k) {
							status.add(
									SignReceiptDef.Status.CONFIRMING.getCode());
							copiedList.add(
									SignReceiptDef.Status.CONFIRMING.getCode());
							addCommonConditions(wrapper, status, copiedList,
									CommonDef.AccountSource.CUSTOM.getCode(),
									CommonDef.AccountSource.INNER.getCode());
						} else if (j) {
							addCommonConditions(wrapper, status, copiedList,
									CommonDef.AccountSource.CUSTOM.getCode(),
									CommonDef.AccountSource.INNER.getCode());
							wrapper.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											CommonDef.AccountSource.INNER
													.getCode()));
						} else {
							addCommonConditions(wrapper, status, copiedList,
									CommonDef.AccountSource.CUSTOM.getCode(),
									CommonDef.AccountSource.INNER.getCode());
							wrapper.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											CommonDef.AccountSource.CUSTOM
													.getCode()));
						}
					}

				}
			}
		}

		wrapper.in(CollectionUtils.isNotEmpty(reconciliationStatus),
				SignReceipt::getReconciliationStatus, reconciliationStatus);
		if (CollectionUtils.isNotEmpty(reconciliationStatus)) {
			wrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.FINISHED.getCode());
		}
		wrapper.ge(Objects.nonNull(startTime), SignReceipt::getSignConfirmDate,
				startTime);
		wrapper.le(Objects.nonNull(endTime), SignReceipt::getSignConfirmDate,
				endTime);

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + " ,id desc");
		} else {
			// 默认按照创建时间降序排列
			wrapper.last("order by updated_time desc");
		}

		Page<SignReceipt> paging = repository.selectPage(new Page<>(page, size),
				wrapper);

		List<SignReceiptVo> vos = this.packVos(paging.getRecords(), contracts,
				orderId);
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<SignReceiptVo> pageSelector(Integer page, Integer size,
			String key, String contractId, List<Integer> status,
			List<String> signReceiptIds, Integer type, String receiptId,
			Integer initiator) {

		// 合同关联的签收列表（已经去除掉已经关联对账单的签收单)
		LambdaQueryWrapper<SignReceipt> queryWrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		queryWrapper.like(Objects.nonNull(key), SignReceipt::getId, key);
		queryWrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(SignReceipt::getContractId, contractId);
		queryWrapper.eq(Objects.nonNull(initiator), SignReceipt::getInitiator,
				initiator);
		queryWrapper.in(CollectionUtils.isNotEmpty(status),
				SignReceipt::getStatus, status);
		queryWrapper.in(CollectionUtils.isNotEmpty(signReceiptIds),
				SignReceipt::getId, signReceiptIds);
		List<String> receiptIds = new ArrayList<>();
		if (InboundDef.InfoType.INBOUND.match(type)) {
			List<Inbound> relateReceipt = inboundService.findRelateReceipt();
			if (CollectionUtils.isNotEmpty(relateReceipt)) {
				List<String> list = relateReceipt.stream()
						.map(Inbound::getReceiptId).filter(Objects::nonNull)
						.toList();
				receiptIds = new ArrayList<>(list);
				if (Objects.nonNull(receiptId)) {
					receiptIds.removeIf(e -> e.equals(receiptId));
				}
			}
		} else {
			List<Outbound> relateReceipt = outboundService.findRelateReceipt();
			if (CollectionUtils.isNotEmpty(relateReceipt)) {
				List<String> list = relateReceipt.stream()
						.map(Outbound::getReceiptId).filter(Objects::nonNull)
						.toList();
				receiptIds = new ArrayList<>(list);
				if (Objects.nonNull(receiptId)) {
					receiptIds.removeIf(e -> e.equals(receiptId));
				}
			}
		}
		queryWrapper.notIn(CollectionUtils.isNotEmpty(receiptIds),
				SignReceipt::getId, receiptIds);

		// 合同关联的签收列表去除掉被对账单已经关联的数据
		Page<SignReceipt> paging = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		List<SignReceiptVo> vos = this.packVos(paging.getRecords(), null, null);
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public SignReceiptVo findVoById(String id) {
		return this.findOne(id).stream().map(e -> {
			Contract contract = contractService.findOne(e.getContractId())
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30151001));
			SignReceiptVo signReceiptVo = new SignReceiptVo();
			signReceiptVo.setSignReceipt(e);
			signReceiptVo.setContract(contract);
			signReceiptVo.setProject(projectService.findOne(e.getProjectId())
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30152013)));
			List<GoodsInfo> list = new ArrayList<>();
			List<DeliverGoods> deliverGoodsList = deliverGoodsService
					.findByIds(e.getRelatedDeliverGoodsIds());
			List<String> orderIds = deliverGoodsList.stream()
					.map(DeliverGoods::getOrderId).distinct().toList();
			List<OrderVo> orderVoList = orderService.findByOrderIds(orderIds,
					deliverGoodsList, contract);
			signReceiptVo.setOrderVoList(orderVoList);
			// 发货单id
			List<String> deliverGoodsIds = deliverGoodsList.stream()
					.map(DeliverGoods::getId).distinct().toList();
			// 船运单信息
			List<TransportOrderShip> transportOrderShips = transportOrderShipService
					.findByDeliverGoodsIds(deliverGoodsIds);
			// 汽运单信息
			List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
					.findByDeliverGoodsIds(deliverGoodsIds);

			// 铁路单信息
			List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
					.findByDeliverGoodsIds(deliverGoodsIds);

			signReceiptVo.setShipList(transportOrderShips);
			signReceiptVo.setVehicleList(transportOrderVehicles);
			signReceiptVo.setRailwayList(transportOrderRailways);

			for (DeliverGoods deliverGoods : deliverGoodsList) {
				if (Objects.nonNull(deliverGoods.getGoodsInfo())) {
					List<GoodsInfo> goodsInfoList = this
							.convertGoodsInfo(deliverGoods.getGoodsInfo());
					for (GoodsInfo goodsInfo : goodsInfoList) {
						goodsInfo.setOrderId(deliverGoods.getOrderId());
						goodsInfo.setDeliverGoodsId(deliverGoods.getId());
					}
					list.addAll(goodsInfoList);
				}
			}
			List<GoodsInfo> result = new ArrayList<>(list);
			result.sort((o1, o2) -> {
				if (Objects.isNull(o1.getReceiptDate())
						|| Objects.isNull(o2.getReceiptDate())) {
					return 0;
				}
				return o2.getReceiptDate().compareTo(o1.getReceiptDate());
			});
			list = result;
			signReceiptVo.setGoodsInfoList(list);
			this.packSignReceiptVo(e, signReceiptVo, contract,
					deliverGoodsList);
			return signReceiptVo;
		}).findFirst().orElse(null);
	}

	/**
	 * 签收下拉列表
	 *
	 * @return
	 */
	@Override
	public List<SignReceiptVo> selector(String reconciliationId,
			String contractId, List<Integer> status,
			List<Integer> reconciliationStatus, List<String> signReceiptIds) {
		Contract contract = contractService.findOne(contractId).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30151001));
		// 传入了对账单id 获取对账单已经关联的签收单id 修改的时候传入 新增不要传入
		List<String> relatedReceiptIds = new ArrayList<>();
		if (Objects.nonNull(reconciliationId)) {
			Reconciliation reconciliation = reconciliationService
					.findOne(reconciliationId)
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30154008));
			// 对账单关联的签收单id
			relatedReceiptIds = reconciliation.getReceiptIds().stream()
					.toList();
		}
		List<String> combinedReceiptIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(signReceiptIds)
				&& CollectionUtils.isNotEmpty(relatedReceiptIds)) {
			// 将signReceiptIds和relatedReceiptIds 合并成一个列表
			combinedReceiptIds = Stream
					.concat(signReceiptIds.stream(), relatedReceiptIds.stream())
					.toList();
		}
		if (CollectionUtils.isNotEmpty(signReceiptIds)) {
			combinedReceiptIds = signReceiptIds;
		}
		if (CollectionUtils.isNotEmpty(relatedReceiptIds)) {
			combinedReceiptIds = relatedReceiptIds;
		}
		// 找出合同已经关联了的对账单
		List<Reconciliation> reconciliations = reconciliationService
				.findByContractId(contractId);
		// 合同关联的对账单里面找出签收单id
		List<String> receiptIds = reconciliations.stream()
				.map(Reconciliation::getReceiptIds).filter(Objects::nonNull)
				.flatMap(arrayString -> Arrays
						.stream(arrayString.toArray(new String[0])))
				.toList();
		// 合同关联的签收列表（已经去除掉已经关联对账单的签收单)
		LambdaQueryWrapper<SignReceipt> queryWrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		queryWrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(SignReceipt::getContractId, contractId);
		queryWrapper.in(CollectionUtils.isNotEmpty(status),
				SignReceipt::getStatus, status);
		queryWrapper.in(CollectionUtils.isNotEmpty(reconciliationStatus),
				SignReceipt::getReconciliationStatus, reconciliationStatus);
		// 去除掉已经关联对账单的签收单id
		queryWrapper.notIn(CollectionUtils.isNotEmpty(receiptIds),
				SignReceipt::getId, receiptIds);
		// 合同关联的签收列表去除掉被对账单已经关联的数据
		List<SignReceipt> signReceipts = repository.selectList(queryWrapper);
		// 根据combinedReceiptIds查询的签收单列表
		List<SignReceipt> signReceiptList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(combinedReceiptIds)) {
			signReceiptList = this.findByIds(combinedReceiptIds);
		}
		// signReceipts和signReceiptList合并为一个列表，并去重
		Map<String, SignReceipt> uniqueSignReceiptsMap = Stream
				.concat(signReceipts.stream(), signReceiptList.stream())
				.collect(Collectors.toMap(SignReceipt::getId, sr -> sr,
						(existing, replacement) -> existing));
		// 将Map中的值转换回list
		List<SignReceipt> uniqueSignReceipts = uniqueSignReceiptsMap.values()
				.stream().toList();
		return this.packSignReceipts(uniqueSignReceipts, contract);

	}

	@Override
	public List<SignReceipt> findByContractId(String contractId) {
		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		this.filterDeleted(wrapper);
		wrapper.eq(SignReceipt::getContractId, contractId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<SignReceipt> findByPurchaserIdAndState(Long customId,
			Integer state) {
		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(customId), SignReceipt::getPurchaserId,
				customId)
				.eq(Objects.nonNull(state), SignReceipt::getStatus, state);
		return repository.selectList(wrapper);
	}

	@Override
	public List<SignReceipt> find(String contractId, List<Integer> status) {
		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(contractId), SignReceipt::getContractId,
				contractId);
		wrapper.in(CollectionUtils.isNotEmpty(status), SignReceipt::getStatus,
				status);
		return repository.selectList(wrapper);
	}

	@Override
	public List<SignReceipt> findNotInStatus(String contractId,
			List<Integer> status) {
		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(contractId), SignReceipt::getContractId,
				contractId);
		wrapper.notIn(CollectionUtils.isNotEmpty(status),
				SignReceipt::getStatus, status);
		return repository.selectList(wrapper);
	}

	@Override
	public List<SignReceipt> findByOrderIds(List<String> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Collections.emptyList();
		}
		LambdaQueryWrapper<SignReceipt> queryWrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		queryWrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		// 动态添加 JSON_CONTAINS 条件
		if (CollectionUtils.isNotEmpty(orderIds)) {
			if (orderIds.size() > 1) {
				queryWrapper.and(wrapper -> {
					for (String id : orderIds) {
						wrapper.or().apply(
								"JSON_CONTAINS(related_order_ids, JSON_QUOTE({0}))",
								id);
					}
				});
			} else {
				queryWrapper.and(wrapper -> wrapper.apply(
						"JSON_CONTAINS(related_order_ids, JSON_QUOTE({0}))",
						orderIds.get(0)));
			}
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<SignReceipt> findByDeliverGoodsIds(List<String> list) {
		LambdaQueryWrapper<SignReceipt> queryWrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		queryWrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		if (CollectionUtils.isNotEmpty(list)) {
			if (list.size() > 1) {
				queryWrapper.and(wrapper -> {
					for (String id : list) {
						wrapper.or().apply(
								"JSON_CONTAINS(related_deliver_goods_ids, JSON_QUOTE({0}))",
								id);
					}
				});
			} else {
				queryWrapper.and(wrapper -> wrapper.apply(
						"JSON_CONTAINS(related_deliver_goods_ids, JSON_QUOTE({0}))",
						list.get(0)));
			}
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<SignReceipt> findByOrderId(String orderId) {
		LambdaQueryWrapper<SignReceipt> queryWrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		queryWrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.and(i -> i.apply(StringUtils.isNotBlank(orderId),
				"JSON_CONTAINS(related_order_ids, JSON_QUOTE({0}))", orderId));
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<SignReceipt> findByProjectId(String projectId) {
		LambdaQueryWrapper<SignReceipt> queryWrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		queryWrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(SignReceipt::getProjectId, projectId);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<SignReceipt> findUnfinished(String projectId) {
		LambdaQueryWrapper<SignReceipt> queryWrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		queryWrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(SignReceipt::getProjectId, projectId);
		queryWrapper.ne(SignReceipt::getStatus,
				SignReceiptDef.Status.FINISHED.getCode());
		return repository.selectList(queryWrapper);
	}

	/**
	 * 根据合同id, 签收状态 对账查询 总金额
	 *
	 * @Author:魏紫萱
	 * @return
	 */
	@Override
	public Optional<BigDecimal> findAmount(String contractId,
			List<Integer> status, List<Integer> reconciliationStatus,
			Integer type) {
		Contract contract = contractService.findOne(contractId).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30151001));
		LambdaQueryWrapper<SignReceipt> queryWrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		queryWrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(SignReceipt::getContractId, contractId);
		queryWrapper.in(CollectionUtils.isNotEmpty(status),
				SignReceipt::getStatus, status);
		queryWrapper.in(CollectionUtils.isNotEmpty(reconciliationStatus),
				SignReceipt::getReconciliationStatus, reconciliationStatus);
		List<SignReceipt> signReceiptList = repository.selectList(queryWrapper);
		// 所有签收单id
		List<String> signReceiptIds = signReceiptList.stream()
				.map(SignReceipt::getId).toList();
		// 发货信息列表
		List<DeliverGoods> deliverGoodsLists = deliverGoodsService
				.findBySignReceiptIds(signReceiptIds);
		// 将发货单根据签收单进行分组
		Map<String, List<DeliverGoods>> signReceiptMap = deliverGoodsLists
				.stream()
				.collect(Collectors.groupingBy(DeliverGoods::getSignReceiptId));
		// 定义好总的金额
		BigDecimal totalAmount = BigDecimal.ZERO;
		// 循环签收单
		for (SignReceipt signReceipt : signReceiptList) {
			// 签收单对应的发货信息列表
			List<DeliverGoods> deliverGoodsList = signReceiptMap
					.get(signReceipt.getId());
			// 循环发货信息里面的货物信息 算出金额
			for (DeliverGoods deliverGoods : deliverGoodsList) {
				// 初始化发货的对账状态为对账中
				int deliverGoodsRecState = DeliverGoodsDef.ReconciliationState.RECONCILIATION
						.getCode();
				String orderId = deliverGoods.getOrderId();
				Order order = orderService.findOne(orderId).orElse(null);
				if (Objects.nonNull(order)) {
					// 根据订单id找到对应的对账
					List<Reconciliation> reconciliations = reconciliationService
							.findByOrderIds(
									new ArrayList<>(List.of(order.getId())),
									false);
					if (CollectionUtils.isNotEmpty(reconciliations)) {
						Reconciliation reconciliation = reconciliations.get(0);
						if (ReconciliationDef.Type.SELL
								.match(reconciliation.getType())) {
							// 如果对账单的状态为对账完成 则发货的对账状态为对账完成
							if (ReconciliationDef.State.FINISHED
									.match(reconciliation.getState())) {
								deliverGoodsRecState = DeliverGoodsDef.ReconciliationState.COMPLETED
										.getCode();
							}
							// 如果对账单的状态为预对账完成 则发货的对账状态为预对账完成
							else if (ReconciliationDef.State.PRE_FINISHED
									.match(reconciliation.getState())) {
								deliverGoodsRecState = DeliverGoodsDef.ReconciliationState.PRE_COMPLETED
										.getCode();

							} else {
								if (CommonDef.Symbol.NO.match(reconciliation
										.getIsConductReconciliation())) {
									deliverGoodsRecState = DeliverGoodsDef.ReconciliationState.PRE_RECONCILIATION
											.getCode();
								}
							}
						}
					}
					// 订单里面的货物信息
					List<GoodsInfo> goodsInfoList1 = this
							.convertGoodsInfo(order.getGoodsInfo());
					// 定义map用于存对应的规格和单价
					Map<String, BigDecimal> modelToPriceMap = new HashMap<>();
					// 循环订单信息里面的货物信息 算出所有规格的价格之和
					BigDecimal totalOrderPrice = BigDecimal.ZERO;
					// 循环订单信息里面的货物信息 算出所有规格总数
					int orderNum = 0;
					// 订单规格平均价
					BigDecimal avePrice = BigDecimal.ZERO;
					// 发货信息里面的规格集合
					Set<String> modelSet = new HashSet<>();
					// 订单里面对应的规格和单价
					for (GoodsInfo orderGoodsInfo : goodsInfoList1) {
						String model = orderGoodsInfo.getModel();
						BigDecimal price = orderGoodsInfo.getUnitPrice();
						// 如果 map 中尚不存在该 model，则添加它
						if (!modelToPriceMap.containsKey(model)) {
							modelToPriceMap.put(model, price);
						}
					}
					// 发货信息里面的货物信息
					// 根据发货单的发货方式：船运用船运单；汽运用汽运单；自提用goodsInfo
					// 自提
					List<GoodsInfo> goodsInfoList = this
							.convertGoodsInfo(deliverGoods.getGoodsInfo());
					// 船运
					List<TransportOrderShip> transportOrderShips = transportOrderShipService
							.findByDeliverGoodsIds(
									List.of(deliverGoods.getId()));
					// 汽运
					List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
							.findByDeliverGoodsIds(
									List.of(deliverGoods.getId()));
					// 铁路
					List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
							.findByDeliverGoodsIds(
									List.of(deliverGoods.getId()));
					// 采购类型的并且是录入企业的数据是存在货物信息里面的
					if (ContractDef.ContractType.PURCHASE
							.match(contract.getContractType())
							&& Objects.isNull(
									contract.getUpstreamSuppliersId())) {
						// 签收方式为合并签收
						if (DeliverGoodsDef.ReceiveWay.MERGE
								.match(deliverGoods.getReceiveWay())) {
							if (CollectionUtils.isNotEmpty(goodsInfoList)) {
								for (GoodsInfo goodsInfo : goodsInfoList) {
									// 将发货信息里面的规格存入set
									modelSet.add(goodsInfo.getModel());
								}
							}
							totalAmount = this.getMergeTotalAmount(deliverGoods,
									modelSet, modelToPriceMap, totalAmount,
									totalOrderPrice, orderNum, avePrice);
						} else {
							// 签收方式为多条签收
							totalAmount = this.getSepTotalAmount(goodsInfoList,
									modelToPriceMap, totalAmount);
						}
					}
					//
					else {
						// 如果发货的对账状态处于预对账中或者对账中 就计算金额 否则会在对账中计算
						if (DeliverGoodsDef.ReconciliationState.RECONCILIATION
								.match(deliverGoodsRecState)
								|| DeliverGoodsDef.ReconciliationState.PRE_RECONCILIATION
										.match(deliverGoodsRecState)) {
							// 签收方式为合并签收
							if (DeliverGoodsDef.ReceiveWay.MERGE
									.match(deliverGoods.getReceiveWay())) {
								switch (DeliverGoodsDef.DeliverWay
										.from(deliverGoods.getDelivery())) {
									case SHIPPING -> {
										// 船运
										if (CollectionUtils.isNotEmpty(
												transportOrderShips)) {
											for (TransportOrderShip ship : transportOrderShips) {
												// 将发货信息里面的规格存入set
												modelSet.add(ship.getModel());
											}
										}
									}
									case CAR -> {
										// 汽运
										if (CollectionUtils.isNotEmpty(
												transportOrderVehicles)) {
											for (TransportOrderVehicle vehicle : transportOrderVehicles) {
												// 将发货信息里面的规格存入set
												modelSet.add(
														vehicle.getGoodsType());
											}
										}
									}
									case TRAIN -> {
										// 铁路
										if (CollectionUtils.isNotEmpty(
												transportOrderRailways)) {
											for (TransportOrderRailway railway : transportOrderRailways) {
												// 将发货信息里面的规格存入set
												modelSet.add(
														railway.getModel());
											}
										}
									}
									case SELF_PICKUP -> {
										// 自提
										if (CollectionUtils
												.isNotEmpty(goodsInfoList)) {
											for (GoodsInfo goodsInfo : goodsInfoList) {
												// 将发货信息里面的规格存入set
												modelSet.add(
														goodsInfo.getModel());
											}
										}
									}
								}
								totalAmount = this.getMergeTotalAmount(
										deliverGoods, modelSet, modelToPriceMap,
										totalAmount, totalOrderPrice, orderNum,
										avePrice);
							}
							// 签收方式为多条签收时可以循环发货信息的货物信息里面的签收数量乘以对应的单价
							else {
								switch (DeliverGoodsDef.DeliverWay
										.from(deliverGoods.getDelivery())) {
									case SHIPPING -> {
										// 船运
										if (CollectionUtils.isNotEmpty(
												transportOrderShips)) {
											for (TransportOrderShip transportOrderShip : transportOrderShips) {
												// 用对应订单里面的货物信息里面的对应规格的单价*发货信息里面的签收重量
												BigDecimal price = modelToPriceMap
														.get(transportOrderShip
																.getModel());
												if (Objects.nonNull(price)
														&& Objects.nonNull(
																transportOrderShip
																		.getReceiptQuantity())) {
													totalAmount = totalAmount
															.add(transportOrderShip
																	.getReceiptQuantity()
																	.multiply(
																			price));
												}
											}
										}
									}
									case CAR -> {
										// 汽运
										if (CollectionUtils.isNotEmpty(
												transportOrderVehicles)) {
											for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicles) {
												// 用对应订单里面的货物信息里面的对应规格的单价*发货信息里面的签收重量
												BigDecimal price = modelToPriceMap
														.get(transportOrderVehicle
																.getGoodsType());
												if (Objects.nonNull(price)
														&& Objects.nonNull(
																transportOrderVehicle
																		.getReceiptQuantity())) {
													totalAmount = totalAmount
															.add(transportOrderVehicle
																	.getReceiptQuantity()
																	.multiply(
																			price));
												}
											}
										}
									}
									case TRAIN -> {
										// 铁路
										if (CollectionUtils.isNotEmpty(
												transportOrderRailways)) {
											for (TransportOrderRailway transportOrderRailway : transportOrderRailways) {
												// 用对应订单里面的货物信息里面的对应规格的单价*发货信息里面的签收重量
												BigDecimal price = modelToPriceMap
														.get(transportOrderRailway
																.getModel());
												if (Objects.nonNull(price)
														&& Objects.nonNull(
																transportOrderRailway
																		.getReceiptQuantity())) {
													totalAmount = totalAmount
															.add(transportOrderRailway
																	.getReceiptQuantity()
																	.multiply(
																			price));
												}
											}
										}
									}
									case SELF_PICKUP ->
										// 自提
										totalAmount = this.getSepTotalAmount(
												goodsInfoList, modelToPriceMap,
												totalAmount);
								}

							}
						}
					}

				}
			}

		}
		return Optional.of(totalAmount);
	}

	@Override
	public Optional<Boolean> checkQuickBound(String id, Integer type) {
		SignReceipt signReceipt = this.findOne(id).orElse(null);
		if (Objects.isNull(signReceipt)) {
			// 签收单不存在
			return Optional.of(Boolean.FALSE);
		}
		if (InboundDef.InfoType.INBOUND.match(type)) {
			// 入库判断
			List<Inbound> inboundList = inboundService
					.findByReceiptId(signReceipt.getId());
			if (CollectionUtils.isNotEmpty(inboundList)) {
				for (Inbound inbound : inboundList) {
					if (!InboundDef.Status.INBOUND_INVALID
							.match(inbound.getState())) {
						return Optional.of(Boolean.FALSE);
					}
				}
			}
		} else {
			// 出库判断
			List<Outbound> outboundList = outboundService
					.findByReceiptId(signReceipt.getId());
			if (CollectionUtils.isNotEmpty(outboundList)) {
				for (Outbound outbound : outboundList) {
					if (!OutboundDef.Status.INVALIDED
							.match(outbound.getState())) {
						return Optional.of(Boolean.FALSE);
					}
				}
			}
		}
		return Optional.of(Boolean.TRUE);
	}

	// 多条签收根据发货信息里面的货物信息计算金额
	private BigDecimal getSepTotalAmount(List<GoodsInfo> goodsInfoList,
			Map<String, BigDecimal> modelToPriceMap, BigDecimal totalAmount) {
		if (CollectionUtils.isNotEmpty(goodsInfoList)) {
			for (GoodsInfo goodsInfo : goodsInfoList) {
				// 用对应订单里面的货物信息里面的对应规格的单价*发货信息里面的签收重量
				BigDecimal price = modelToPriceMap.get(goodsInfo.getModel());
				if (Objects.nonNull(price)
						&& Objects.nonNull(goodsInfo.getReceiptQuantity())) {
					totalAmount = totalAmount.add(
							goodsInfo.getReceiptQuantity().multiply(price));
				}
			}
		}
		return totalAmount;
	}

	// 合并签收根据发货信息里面的货物信息计算金额
	private BigDecimal getMergeTotalAmount(DeliverGoods deliverGoods,
			Set<String> modelSet, Map<String, BigDecimal> modelToPriceMap,
			BigDecimal totalAmount, BigDecimal totalOrderPrice, int orderNum,
			BigDecimal avePrice) {
		List<String> modelList = new ArrayList<>(modelSet);
		// 如果只有一种规格，则用那种规格的单价乘以发货信息的签收数量
		if (modelList.size() == 1) {
			if (Objects.nonNull(deliverGoods.getReceiptWeight())
					&& Objects.nonNull(modelToPriceMap.get(modelList.get(0)))) {
				totalAmount = totalAmount.add(deliverGoods.getReceiptWeight()
						.multiply(modelToPriceMap.get(modelList.get(0))));
			}
		}
		// 如果发货信息里面的规格大于一种，则计算发货信息里面包含的所有规格的单价之和
		else if (modelList.size() > 1) {
			for (String model : modelList) {
				BigDecimal price = modelToPriceMap.get(model);
				if (Objects.nonNull(price)) {
					totalOrderPrice = totalOrderPrice.add(price);
					orderNum++;
				}
			}
			// 计算平均数
			if (orderNum > 0) {
				avePrice = totalOrderPrice.divide(new BigDecimal(orderNum), 2,
						RoundingMode.HALF_UP);
			}
			// 用所包含的规格的单价平均数乘以签收数量
			if (Objects.nonNull(deliverGoods.getReceiptWeight())) {
				totalAmount = totalAmount.add(
						deliverGoods.getReceiptWeight().multiply(avePrice));
			}
		}
		return totalAmount;
	}

	@Override
	public Optional<BigDecimal> findTotalQuantity(String projectId,
			Integer state, LocalDateTime startTime, LocalDateTime endTime,
			Integer type, String param, String purchaserName,
			List<Integer> status, List<Integer> reconciliationStatus) {
		BigDecimal receiptWeight = BigDecimal.ZERO;
		List<Contract> contracts;
		if (StringUtils.isNotBlank(purchaserName)) {
			contracts = contractService.find(purchaserName, null, null, type,
					null);
		} else {
			contracts = contractService.find(null, null, null, type, null);
		}
		if (CollectionUtils.isEmpty(contracts)) {
			return Optional.of(BigDecimal.ZERO);
		}
		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		wrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode())
				.in(CollectionUtils.isNotEmpty(contracts),
						SignReceipt::getContractId,
						contracts.stream().map(Contract::getId).toList())
				.ge(Objects.nonNull(startTime), SignReceipt::getSignConfirmDate,
						startTime)
				.le(Objects.nonNull(endTime), SignReceipt::getSignConfirmDate,
						endTime)
				.eq(SignReceipt::getProjectId, projectId);
		// 签收编号或者合同名称
		if (Objects.nonNull(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(x -> x.like(SignReceipt::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					SignReceipt::getContractId, contractIds));
		}
		List<Integer> toBeSignedList = new ArrayList<>();
		toBeSignedList.add(
				BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode());
		toBeSignedList
				.add(BusinessContractDef.CommonSignState.UNSIGNED.getCode());
		if (ContractDef.Type.SELL.match(type)) {
			if (CollectionUtils.isEmpty(status)) {
				wrapper.and(x -> x
						.eq(SignReceipt::getInitiator,
								CommonDef.AccountSource.INNER.getCode())
						.in(SignReceipt::getStatus,
								SignReceiptDef.Status.DRAFT.getCode(),
								SignReceiptDef.Status.INVALIDING.getCode(),
								SignReceiptDef.Status.INVALID.getCode(),
								SignReceiptDef.Status.REVERTED.getCode(),
								SignReceiptDef.Status.CONFIRMING.getCode(),
								SignReceiptDef.Status.SIGNING.getCode(),
								SignReceiptDef.Status.FINISHED.getCode(),
								SignReceiptDef.Status.TO_BE_INITIATE.getCode())
						.or(y -> y
								.eq(SignReceipt::getInitiator,
										CommonDef.AccountSource.CUSTOM
												.getCode())
								.in(SignReceipt::getStatus,
										SignReceiptDef.Status.CONFIRMING
												.getCode(),
										SignReceiptDef.Status.SIGNING.getCode(),
										SignReceiptDef.Status.INVALIDING
												.getCode(),
										SignReceiptDef.Status.INVALID.getCode(),
										SignReceiptDef.Status.FINISHED
												.getCode())));
			} else {
				if (!status.contains(SignReceiptDef.Status.DRAFT.getCode())
						&& !status.contains(
								SignReceiptDef.Status.REVERTED.getCode())) {
					boolean b = status.removeIf(
							SignReceiptDef.Status.TO_BE_SIGNED::match);
					boolean d = status
							.removeIf(SignReceiptDef.Status.SIGNING::match);
					boolean j = status.removeIf(
							SignReceiptDef.Status.TO_BE_CONFIRMED::match);
					boolean k = status
							.removeIf(SignReceiptDef.Status.CONFIRMING::match);
					// 是否包含待签署状态
					if (b && d) {
						handleStatus(wrapper, status, j, k,
								CommonDef.AccountSource.CUSTOM.getCode(),
								CommonDef.AccountSource.INNER.getCode());
					} else if (!b && !d) {
						handleStatus1(wrapper, status, j, k,
								CommonDef.AccountSource.CUSTOM.getCode(),
								CommonDef.AccountSource.INNER.getCode());
					} else if (b) {
						handleToBeSignedStatus(wrapper, status, j, k,
								toBeSignedList,
								CommonDef.AccountSource.CUSTOM.getCode(),
								CommonDef.AccountSource.INNER.getCode());
					} else {
						handleSigningStatus(wrapper, status, j, k,
								CommonDef.AccountSource.CUSTOM.getCode(),
								CommonDef.AccountSource.INNER.getCode());
					}
				} else {
					List<Integer> copiedList = new ArrayList<>(status);
					boolean b = copiedList.removeIf(
							SignReceiptDef.Status.TO_BE_SIGNED::match);
					boolean d = copiedList
							.removeIf(SignReceiptDef.Status.SIGNING::match);
					boolean j = copiedList.removeIf(
							SignReceiptDef.Status.TO_BE_CONFIRMED::match);
					boolean k = copiedList
							.removeIf(SignReceiptDef.Status.CONFIRMING::match);

					copiedList.removeIf(e -> e
							.equals(SignReceiptDef.Status.DRAFT.getCode())
							|| e.equals(
									SignReceiptDef.Status.REVERTED.getCode()));
					if ((!b && !d) || (b && d)) {
						// 如果没有包含待签署和签署中状态，则直接查询
						if (!j && !k) {
							addCommonConditions1(wrapper, status, copiedList,
									CommonDef.AccountSource.INNER.getCode(),
									CommonDef.AccountSource.CUSTOM.getCode());
						} else if (j && k) {
							status.add(
									SignReceiptDef.Status.CONFIRMING.getCode());
							copiedList.add(
									SignReceiptDef.Status.CONFIRMING.getCode());
							addCommonConditions1(wrapper, status, copiedList,
									CommonDef.AccountSource.INNER.getCode(),
									CommonDef.AccountSource.CUSTOM.getCode());
						} else if (j) {
							addCommonConditions1(wrapper, status, copiedList,
									CommonDef.AccountSource.INNER.getCode(),
									CommonDef.AccountSource.CUSTOM.getCode());
							wrapper.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											CommonDef.AccountSource.CUSTOM
													.getCode()));
						} else {
							addCommonConditions1(wrapper, status, copiedList,
									CommonDef.AccountSource.INNER.getCode(),
									CommonDef.AccountSource.CUSTOM.getCode());
							wrapper.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											CommonDef.AccountSource.INNER
													.getCode()));
						}
					} else if (b) {
						// 只包含待签署状态
						if (!j && !k) {
							addConditions(wrapper, status, copiedList,
									toBeSignedList,
									CommonDef.AccountSource.INNER.getCode(),
									CommonDef.AccountSource.CUSTOM.getCode());
						} else if (j && k) {
							status.add(
									SignReceiptDef.Status.CONFIRMING.getCode());
							copiedList.add(
									SignReceiptDef.Status.CONFIRMING.getCode());
							addConditions(wrapper, status, copiedList,
									toBeSignedList,
									CommonDef.AccountSource.INNER.getCode(),
									CommonDef.AccountSource.CUSTOM.getCode());
						} else if (j) {
							addConditions(wrapper, status, copiedList,
									toBeSignedList,
									CommonDef.AccountSource.INNER.getCode(),
									CommonDef.AccountSource.CUSTOM.getCode());
							wrapper.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											CommonDef.AccountSource.CUSTOM
													.getCode()));
						} else {
							addConditions(wrapper, status, copiedList,
									toBeSignedList,
									CommonDef.AccountSource.INNER.getCode(),
									CommonDef.AccountSource.CUSTOM.getCode());
							wrapper.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											CommonDef.AccountSource.INNER
													.getCode()));
						}
					} else {
						// 只包含签署中状态
						if (!j && !k) {
							addCommonConditions(wrapper, status, copiedList,
									CommonDef.AccountSource.INNER.getCode(),
									CommonDef.AccountSource.CUSTOM.getCode());
						} else if (j && k) {
							status.add(
									SignReceiptDef.Status.CONFIRMING.getCode());
							copiedList.add(
									SignReceiptDef.Status.CONFIRMING.getCode());
							addCommonConditions(wrapper, status, copiedList,
									CommonDef.AccountSource.INNER.getCode(),
									CommonDef.AccountSource.CUSTOM.getCode());
						} else if (j) {
							addCommonConditions(wrapper, status, copiedList,
									CommonDef.AccountSource.INNER.getCode(),
									CommonDef.AccountSource.CUSTOM.getCode());
							wrapper.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											CommonDef.AccountSource.CUSTOM
													.getCode()));
						} else {
							addCommonConditions(wrapper, status, copiedList,
									CommonDef.AccountSource.INNER.getCode(),
									CommonDef.AccountSource.CUSTOM.getCode());
							wrapper.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											CommonDef.AccountSource.INNER
													.getCode()));
						}
					}

				}
			}
		}
		wrapper.in(CollectionUtils.isNotEmpty(reconciliationStatus),
				SignReceipt::getReconciliationStatus, reconciliationStatus);
		if (CollectionUtils.isNotEmpty(reconciliationStatus)) {
			wrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.FINISHED.getCode());
		}
		List<SignReceipt> signReceipts = repository.selectList(wrapper);
		if (CollectionUtils.isNotEmpty(signReceipts)) {
			if (ContractDef.Type.SELL.match(type)) {
				// 销售类型的要过滤出已完成状态的
				List<SignReceipt> orders1 = signReceipts.stream()
						.filter(i -> i.getStatus().equals(state)).toList();
				receiptWeight = orders1.stream()
						.map(SignReceipt::getReceiptWeight)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			} else {
				receiptWeight = signReceipts.stream()
						.map(SignReceipt::getReceiptWeight)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);

			}
		}
		return Optional.of(receiptWeight);
	}

	@Override
	public Optional<BigDecimal> findTotalQuantityBuy(String projectId,
			List<Integer> states, LocalDateTime startTime,
			LocalDateTime endTime, String param, String sellerName,
			List<Integer> status, List<Integer> reconciliationStatus) {
		BigDecimal receiptWeight = BigDecimal.ZERO;
		List<Contract> contracts = contractService.find(null, null,
				ContractDef.ContractType.PURCHASE.getCode());

		if (CollectionUtils.isEmpty(contracts)) {
			return Optional.of(BigDecimal.ZERO);
		}
		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		wrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode())
				.in(CollectionUtils.isNotEmpty(contracts),
						SignReceipt::getContractId,
						contracts.stream().map(Contract::getId).toList())
				.ge(Objects.nonNull(startTime), SignReceipt::getSignConfirmDate,
						startTime)
				.le(Objects.nonNull(endTime), SignReceipt::getSignConfirmDate,
						endTime)
				.eq(SignReceipt::getProjectId, projectId);

		wrapper.eq(SignReceipt::getType, SignReceiptDef.Type.BUY.getCode());

		// 签收编号或者合同名称
		if (Objects.nonNull(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(x -> x.like(SignReceipt::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					SignReceipt::getContractId, contractIds));
		}

		if (CollectionUtils.isNotEmpty(status)) {
			wrapper.and(x -> {
				for (Integer state : status) {
					SignReceiptDef.Status from = SignReceiptDef.Status
							.from(state);
					switch (from) {
						case DRAFT, REVERTED, CONFIRMING ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getInitiator,
									CommonDef.AccountSource.CUSTOM.getCode()));
						case INVALID, INVALIDING, FINISHED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state));
						case TO_BE_CONFIRMED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getInitiator,
									CommonDef.AccountSource.INNER.getCode()));
						case SIGNING ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getStatus,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode()));
						case TO_BE_SIGNED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).in(
									SignReceipt::getStatus,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode()));
					}
				}
			});
		} else {
			wrapper.and(x -> x
					.in(SignReceipt::getStatus,
							SignReceiptDef.Status.INVALID.getCode(),
							SignReceiptDef.Status.INVALIDING.getCode(),
							SignReceiptDef.Status.FINISHED.getCode())
					.or(y -> y
							.in(SignReceipt::getStatus,
									SignReceiptDef.Status.DRAFT.getCode(),
									SignReceiptDef.Status.REVERTED.getCode(),
									SignReceiptDef.Status.CONFIRMING.getCode())
							.eq(SignReceipt::getInitiator,
									CommonDef.AccountSource.CUSTOM.getCode()))
					.or(y -> y
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.TO_BE_CONFIRMED
											.getCode())
							.eq(SignReceipt::getInitiator,
									CommonDef.AccountSource.INNER.getCode()))
					.or(y -> y
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.SIGNING.getCode())
							.eq(SignReceipt::getStatus,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode()))
					.or(y -> y
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.TO_BE_SIGNED
											.getCode())
							.in(SignReceipt::getStatus,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode())));
		}

		wrapper.in(CollectionUtils.isNotEmpty(reconciliationStatus),
				SignReceipt::getReconciliationStatus, reconciliationStatus);
		if (CollectionUtils.isNotEmpty(reconciliationStatus)) {
			wrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.FINISHED.getCode());
		}
		List<SignReceipt> signReceipts = repository.selectList(wrapper);
		if (CollectionUtils.isNotEmpty(signReceipts)) {
			receiptWeight = signReceipts.stream()
					.map(SignReceipt::getReceiptWeight).filter(Objects::nonNull)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		return Optional.of(receiptWeight);
	}

	@Override
	public Optional<BigDecimal> findTotalQuantitySale(String projectId,
			List<Integer> states, LocalDateTime startTime,
			LocalDateTime endTime, String param, String sellerName,
			List<Integer> status, List<Integer> reconciliationStatus) {
		BigDecimal receiptWeight = BigDecimal.ZERO;
		List<Contract> contracts = contractService.find(null, null,
				ContractDef.ContractType.SALES.getCode());
		if (CollectionUtils.isEmpty(contracts)) {
			return Optional.of(BigDecimal.ZERO);
		}
		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		wrapper.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode())
				.in(CollectionUtils.isNotEmpty(contracts),
						SignReceipt::getContractId,
						contracts.stream().map(Contract::getId).toList())
				.ge(Objects.nonNull(startTime), SignReceipt::getSignConfirmDate,
						startTime)
				.le(Objects.nonNull(endTime), SignReceipt::getSignConfirmDate,
						endTime)
				.eq(SignReceipt::getProjectId, projectId);

		wrapper.eq(SignReceipt::getType, SignReceiptDef.Type.SELL.getCode());

		// 签收编号或者合同名称
		if (Objects.nonNull(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(x -> x.like(SignReceipt::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					SignReceipt::getContractId, contractIds));
		}

		if (CollectionUtils.isNotEmpty(status)) {
			wrapper.and(x -> {
				for (Integer state : status) {
					SignReceiptDef.Status from = SignReceiptDef.Status
							.from(state);
					switch (from) {
						case DRAFT, REVERTED, CONFIRMING ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getInitiator,
									CommonDef.AccountSource.INNER.getCode()));
						case INVALID, INVALIDING, FINISHED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state));
						case TO_BE_CONFIRMED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getInitiator,
									CommonDef.AccountSource.CUSTOM.getCode()));
						case SIGNING ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).eq(
									SignReceipt::getStatus,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode()));
						case TO_BE_SIGNED ->
							x.or(y -> y.eq(SignReceipt::getStatus, state).in(
									SignReceipt::getStatus,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode()));
					}
				}
			});
		} else {
			wrapper.and(x -> x
					.in(SignReceipt::getStatus,
							SignReceiptDef.Status.INVALID.getCode(),
							SignReceiptDef.Status.INVALIDING.getCode(),
							SignReceiptDef.Status.FINISHED.getCode())
					.or(y -> y
							.in(SignReceipt::getStatus,
									SignReceiptDef.Status.DRAFT.getCode(),
									SignReceiptDef.Status.REVERTED.getCode(),
									SignReceiptDef.Status.CONFIRMING.getCode())
							.eq(SignReceipt::getInitiator,
									CommonDef.AccountSource.INNER.getCode()))
					.or(y -> y
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.TO_BE_CONFIRMED
											.getCode())
							.eq(SignReceipt::getInitiator,
									CommonDef.AccountSource.CUSTOM.getCode()))
					.or(y -> y
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.SIGNING.getCode())
							.eq(SignReceipt::getStatus,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode()))
					.or(y -> y
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.TO_BE_SIGNED
											.getCode())
							.in(SignReceipt::getStatus,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode(),
									BusinessContractDef.CommonSignState.UNSIGNED
											.getCode())));
		}

		wrapper.in(CollectionUtils.isNotEmpty(reconciliationStatus),
				SignReceipt::getReconciliationStatus, reconciliationStatus);
		if (CollectionUtils.isNotEmpty(reconciliationStatus)) {
			wrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.FINISHED.getCode());
		}
		List<SignReceipt> signReceipts = repository.selectList(wrapper);
		if (CollectionUtils.isNotEmpty(signReceipts)) {
			// 销售类型的要过滤出已完成状态的
			List<SignReceipt> orders1 = signReceipts.stream()
					.filter(i -> states.contains(i.getStatus())).toList();
			receiptWeight = orders1.stream().map(SignReceipt::getReceiptWeight)
					.filter(Objects::nonNull)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		return Optional.of(receiptWeight);
	}

	@Override
	public Optional<BigDecimal> findTotalQuantityOfOrder(String projectId,
			String orderId, LocalDateTime startTime, LocalDateTime endTime,
			Integer state, Integer type) {
		BigDecimal totalQuantity = BigDecimal.ZERO;
		List<Contract> contracts = contractService.find(null, null, null, type,
				null);
		if (CollectionUtils.isEmpty(contracts)) {
			return Optional.of(BigDecimal.ZERO);
		}
		LambdaQueryWrapper<SignReceipt> wrapper = Wrappers
				.query(SignReceipt.class).lambda()
				.eq(SignReceipt::getDel, CommonDef.Symbol.NO.getCode())
				.in(CollectionUtils.isNotEmpty(contracts),
						SignReceipt::getContractId,
						contracts.stream().map(Contract::getId).toList())
				.ge(Objects.nonNull(startTime), SignReceipt::getSignConfirmDate,
						startTime)
				.le(Objects.nonNull(endTime), SignReceipt::getSignConfirmDate,
						endTime)
				.eq(SignReceipt::getProjectId, projectId)
				.eq(Objects.nonNull(state), SignReceipt::getStatus, state);
		List<SignReceipt> signReceipts = repository.selectList(wrapper);
		if (CollectionUtils.isEmpty(signReceipts)) {
			return Optional.of(BigDecimal.ZERO);
		}
		List<String> list = signReceipts.stream().map(SignReceipt::getId)
				.toList();
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findByOrderIdsAndStates(List.of(orderId), List
						.of(DeliverGoodsDef.Status.DELIVER_COMPLETE.getCode()));
		if (CollectionUtils.isEmpty(deliverGoodsList)) {
			return Optional.of(BigDecimal.ZERO);
		}
		deliverGoodsList = deliverGoodsList.stream()
				.filter(e -> list.contains(e.getSignReceiptId())).toList();
		if (CollectionUtils.isEmpty(deliverGoodsList)) {
			return Optional.of(BigDecimal.ZERO);
		}
		for (DeliverGoods deliverGoods : deliverGoodsList) {
			totalQuantity = totalQuantity.add(deliverGoods.getReceiptWeight());
		}
		return Optional.of(totalQuantity);
	}

	/**
	 * @description: 新增签收单
	 * @author: 彭湃
	 * @date: 2025/1/14 14:16
	 * @param: [signReceipt,
	 *             deliverGoodsList]
	 * @return: com.zhihaoscm.domain.bean.entity.SignReceipt
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<SignReceipt> create(SignReceipt signReceipt,
			List<DeliverDto> deliverDtoList, Integer saveType) {

		Project project = projectService.findOne(signReceipt.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService.findOne(signReceipt.getContractId())
				.orElse(new Contract());
		// 按规则设置签收单id
		signReceipt.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(),
				RedisKeys.Cache.PURCHASE_GOODS_RECEIPT_CODE_GENERATOR,
				ContractDef.Type.from(contract.getContractType()).getStr()
						+ AutoCodeDef.BusinessRuleCode.RECEIPT_SUFFIX.getCode(),
				4, AutoCodeDef.DATE_TYPE.yy));
		fillInfo(signReceipt, deliverDtoList);
		// 设置草稿状态
		if (DeliverGoodsDef.SignMode.OFFLINE.match(signReceipt.getSignType())
				&& DeliverGoodsDef.SaveType.SAVE_AND_COMMIT.match(saveType)) {
			signReceipt.setStatus(SignReceiptDef.Status.CONFIRMING.getCode());
			if (CommonDef.AccountSource.CUSTOM
					.match(signReceipt.getInitiator())) {
				ThreadPoolUtil.scheduleTask(
						() -> SpringUtil.getBean(SignReceiptService.class)
								.notice(signReceipt, 1),
						3, TimeUnit.SECONDS,
						ThreadPoolUtil.getUserScheduledExecutor());
			}
		} else if (DeliverGoodsDef.SignMode.ONLINE
				.match(signReceipt.getSignType())
				&& DeliverGoodsDef.SaveType.SAVE_AND_COMMIT.match(saveType)) {
			if (CommonDef.AccountSource.INNER
					.match(signReceipt.getInitiator())) {
				signReceipt.setStatus(
						SignReceiptDef.Status.TO_BE_INITIATE.getCode());
			}
		} else {
			signReceipt.setStatus(SignReceiptDef.Status.DRAFT.getCode());
		}
		signReceipt.setPurchaserInputId(contract.getDownstreamId());
		signReceipt.setPurchaserId(contract.getDownstreamPurchasersId());
		signReceipt.setSellerId(contract.getSupplierChainId());
		signReceipt.setPurchaserEnterprise(
				contract.getDownstreamPurchasersEnterprise());
		signReceipt.setSellerEnterprise(contract.getSupplierChainEnterprise());
		signReceipt.setContractName(contract.getName());
		SignReceipt resource = this.create(signReceipt);
		List<DeliverGoods> deliverGoodsList = deliverDtoList.stream()
				.map(DeliverDto::getDeliverGoods).toList();
		List<TransportOrderShip> shipList = deliverDtoList.stream()
				.map(DeliverDto::getTransportOrderShips)
				.filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
				.toList();
		List<TransportOrderVehicle> vehicleList = deliverDtoList.stream()
				.map(DeliverDto::getTransportOrderVehicles)
				.filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
				.toList();
		List<TransportOrderRailway> railwayList = deliverDtoList.stream()
				.map(DeliverDto::getTransportOrderRailways)
				.filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
				.toList();
		// 更新发货单信息
		deliverGoodsService.batchUpdate(deliverGoodsList);
		if (CollectionUtils.isNotEmpty(shipList)) {
			transportOrderShipService.batchUpdate(shipList);
		}
		if (CollectionUtils.isNotEmpty(vehicleList)) {
			transportOrderVehicleService.batchUpdate(vehicleList);
		}
		if (CollectionUtils.isNotEmpty(railwayList)) {
			transportOrderRailwayService.batchUpdate(railwayList);
		}
		if (ContractDef.Type.SELL.match(contract.getContractType())) {
			// 销售项目下的订单在关联签收单后，更新订单中的签收单状态为进行中
			List<String> orderIds = deliverGoodsList.stream()
					.map(DeliverGoods::getOrderId).distinct().toList();
			List<Order> orderList = orderService.findByIds(orderIds);
			orderList.forEach(e -> e.setReceiveStatus(
					OrderDef.BusinessStatus.IN_PROGRESS.getCode()));
			orderService.batchUpdate(orderList);
		}
		return Optional
				.of(this.update(this.relate(resource, deliverGoodsList)));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<SignReceipt> createAdmin(SignReceipt signReceipt,
			List<DeliverGoods> deliverGoodsList) {

		Project project = projectService.findOne(signReceipt.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService.findOne(signReceipt.getContractId())
				.orElse(new Contract());
		// 按规则设置签收单id
		signReceipt.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(),
				RedisKeys.Cache.PURCHASE_GOODS_RECEIPT_CODE_GENERATOR,
				ContractDef.Type.from(contract.getContractType()).getStr()
						+ AutoCodeDef.BusinessRuleCode.RECEIPT_SUFFIX.getCode(),
				4, AutoCodeDef.DATE_TYPE.yy));
		signReceipt.setContractName(contract.getName());
		SignReceipt resource = this.create(signReceipt);

		if (ContractDef.ContractType.PURCHASE
				.match(contract.getContractType())) {
			signReceipt.setPurchaserId(contract.getSupplierChainId());
			signReceipt.setSellerInputId(contract.getUpstreamId());
			signReceipt.setSellerId(contract.getUpstreamSuppliersId());
			signReceipt.setPurchaserEnterprise(
					contract.getSupplierChainEnterprise());
			signReceipt.setSellerEnterprise(
					contract.getUpstreamSuppliersEnterprise());
		} else {
			signReceipt.setPurchaserInputId(contract.getDownstreamId());
			signReceipt.setPurchaserId(contract.getDownstreamPurchasersId());
			signReceipt.setSellerId(contract.getSupplierChainId());
			signReceipt.setPurchaserEnterprise(
					contract.getDownstreamPurchasersEnterprise());
			signReceipt
					.setSellerEnterprise(contract.getSupplierChainEnterprise());
		}

		if (SignReceiptDef.Type.BUY.match(signReceipt.getType())
				&& SignReceiptDef.Status.CONFIRMING
						.match(signReceipt.getStatus())) {
			this.sendNotice(signReceipt,
					wxSubscriptionProperties.getUnConfirmSignReceiptCode(),
					MessageFormat.format(
							UserMessageConstants.SIGN_RECEIPT_UNCONFIRMED_TEMPLATE,
							signReceipt.getId()),
					SignReceiptDef.Type.BUY.getCode());
		}
		return Optional
				.of(this.update(this.relate(resource, deliverGoodsList)));
	}

	/**
	 * @description: 修改签收单
	 * @author: 彭湃
	 * @date: 2025/1/14 14:50
	 * @param: [resource,
	 *             deliverGoodsList]
	 * @return: com.zhihaoscm.domain.bean.entity.SignReceipt
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<SignReceipt> update(SignReceipt resource,
			List<DeliverDto> deliverDtoList, Integer saveType) {

		Contract contract = contractService.findOne(resource.getContractId())
				.orElse(new Contract());
		// 查找原先的签收单，将之前关联的发货单签收信息清空
		SignReceipt signReceipt = this.findOne(resource.getId()).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30097001));
		ArrayString deliverGoodsIds = signReceipt.getRelatedDeliverGoodsIds();
		List<DeliverGoods> deliverGoodsList1 = deliverGoodsService
				.findByIds(deliverGoodsIds);
		// 原先关联的汽运船运的签收相关都还原
		List<TransportOrderShip> oldShipList = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		resource.setContractName(contract.getName());
		if (CollectionUtils.isNotEmpty(oldShipList)) {
			for (TransportOrderShip transportOrderShip : oldShipList) {
				transportOrderShip.setReceiptDate(null);
				transportOrderShip.setReceiptQuantity(null);
				transportOrderShip.setReceiptRemark(null);
			}
			transportOrderShipService.batchUpdate(oldShipList);
		}
		List<TransportOrderVehicle> oldVehicleList = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(oldVehicleList)) {
			for (TransportOrderVehicle transportOrderVehicle : oldVehicleList) {
				transportOrderVehicle.setReceiptDate(null);
				transportOrderVehicle.setReceiptQuantity(null);
				transportOrderVehicle.setReceiptRemark(null);
			}
			transportOrderVehicleService.batchUpdate(oldVehicleList);
		}
		List<TransportOrderRailway> railWayList = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(railWayList)) {
			for (TransportOrderRailway transportOrderRailway : railWayList) {
				transportOrderRailway.setReceiptDate(null);
				transportOrderRailway.setReceiptQuantity(null);
				transportOrderRailway.setReceiptRemark(null);
			}
			transportOrderRailwayService.batchUpdate(railWayList);
		}
		for (DeliverGoods deliverGoods : deliverGoodsList1) {
			deliverGoods.setReceiptWeight(null);
			deliverGoods.setReceiveWay(null);
			deliverGoods.setSignReceiptId(null);
			if (Objects.nonNull(deliverGoods.getGoodsInfo())) {
				List<GoodsInfo> list = this
						.convertGoodsInfo(deliverGoods.getGoodsInfo());
				for (GoodsInfo goodsInfo : list) {
					goodsInfo.setReceiptQuantity(null);
					goodsInfo.setReceiptDate(null);
					goodsInfo.setReceiptRemark(null);
					goodsInfo.setReceiptWay(null);
				}
				deliverGoods.setGoodsInfo(deliverGoodsService
						.convertJson(deliverGoods.getGoodsInfo(), list));
			}
		}
		deliverGoodsService.batchUpdate(deliverGoodsList1);

		this.fillInfo(resource, deliverDtoList);
		if (DeliverGoodsDef.SignMode.OFFLINE.match(resource.getSignType())
				&& DeliverGoodsDef.SaveType.SAVE_AND_COMMIT.match(saveType)) {
			resource.setStatus(SignReceiptDef.Status.CONFIRMING.getCode());
			if (CommonDef.AccountSource.CUSTOM.match(resource.getInitiator())) {
				SpringUtil.getBean(SignReceiptService.class).notice(resource,
						1);
			}
		} else if (DeliverGoodsDef.SignMode.ONLINE.match(resource.getSignType())
				&& DeliverGoodsDef.SaveType.SAVE_AND_COMMIT.match(saveType)) {
			if (CommonDef.AccountSource.INNER.match(resource.getInitiator())) {
				resource.setStatus(
						SignReceiptDef.Status.TO_BE_INITIATE.getCode());
			}
		} else {
			resource.setStatus(SignReceiptDef.Status.DRAFT.getCode());
		}
		List<DeliverGoods> deliverGoodsList = deliverDtoList.stream()
				.map(DeliverDto::getDeliverGoods).toList();
		this.updateAllProperties(resource);
		List<TransportOrderShip> shipList = deliverDtoList.stream()
				.map(DeliverDto::getTransportOrderShips)
				.filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
				.toList();
		List<TransportOrderVehicle> vehicleList = deliverDtoList.stream()
				.map(DeliverDto::getTransportOrderVehicles)
				.filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
				.toList();
		List<TransportOrderRailway> railwayList = deliverDtoList.stream()
				.map(DeliverDto::getTransportOrderRailways)
				.filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream)
				.toList();
		// 更新发货单信息
		deliverGoodsService.batchUpdate(deliverGoodsList);
		if (CollectionUtils.isNotEmpty(shipList)) {
			transportOrderShipService.batchUpdate(shipList);
		}
		if (CollectionUtils.isNotEmpty(vehicleList)) {
			transportOrderVehicleService.batchUpdate(vehicleList);
		}
		if (CollectionUtils.isNotEmpty(railwayList)) {
			transportOrderRailwayService.batchUpdate(railwayList);
		}

		// 更新订单中的签收单状态
		if (ContractDef.Type.SELL.match(contract.getContractType())) {
			List<String> oldOrderIds = deliverGoodsList1.stream()
					.map(DeliverGoods::getOrderId).distinct().toList();
			List<String> newOrderIds = deliverGoodsList.stream()
					.map(DeliverGoods::getOrderId).distinct().toList();
			changeOrderStatus(oldOrderIds, newOrderIds);
		}
		return Optional.of(resource);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<SignReceipt> updateAdmin(SignReceipt signReceipt,
			List<DeliverGoods> deliverGoodsList) {
		// 取消原先关联的发货单
		SignReceipt old = this.findOne(signReceipt.getId()).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30097001));
		List<Order> orderList = orderService
				.findByIds(old.getRelatedOrderIds());
		List<DeliverGoods> oldGoods = deliverGoodsService
				.findByIds(signReceipt.getRelatedDeliverGoodsIds());
		List<TransportOrderShip> shipList = transportOrderShipService
				.findByDeliverGoodsIds(List.of(old.getId()));
		List<TransportOrderRailway> railwayList = transportOrderRailwayService
				.findByDeliverGoodsIds(List.of(old.getId()));
		List<TransportOrderVehicle> vehicleList = transportOrderVehicleService
				.findByDeliverGoodsIds(List.of(old.getId()));
		Map<String, Order> oldOrderMap = orderList.stream()
				.collect(Collectors.toMap(Order::getId, e -> e));
		Set<Order> ordersSet = new HashSet<>();
		if (CollectionUtils.isNotEmpty(oldGoods)) {
			for (DeliverGoods deliverGoods : oldGoods) {
				Order order = oldOrderMap.get(deliverGoods.getOrderId());
				deliverGoods.setSignReceiptId(null);
				List<GoodsInfo> goodsInfoList = this
						.convertGoodsInfo(deliverGoods.getGoodsInfo());
				for (GoodsInfo goodsInfo : goodsInfoList) {
					if (Objects.nonNull(goodsInfo.getReceiptQuantity())) {
						order.setReceivedWeight(order.getReceivedWeight()
								.subtract(goodsInfo.getReceiptQuantity()));
						deliverGoods.setReceiptWeight(
								deliverGoods.getReceiptWeight().subtract(
										goodsInfo.getReceiptQuantity()));
					}
					goodsInfo.setReceiptQuantity(null);
					goodsInfo.setReceiptDate(null);
					goodsInfo.setReceiptRemark(null);
				}
				deliverGoods.setGoodsInfo(deliverGoodsService.convertJson(
						deliverGoods.getGoodsInfo(), goodsInfoList));
				ordersSet.add(order);
			}
		}
		if (CollectionUtils.isNotEmpty(shipList)) {
			shipList.forEach(e -> {
				e.setReceiptDate(null);
				e.setReceiptQuantity(null);
				e.setReceiptRemark(null);
			});
			transportOrderShipService.batchUpdate(shipList);
		}
		if (CollectionUtils.isNotEmpty(railwayList)) {
			railwayList.forEach(e -> {
				e.setReceiptDate(null);
				e.setReceiptQuantity(null);
				e.setReceiptRemark(null);
			});
			transportOrderRailwayService.batchUpdate(railwayList);
		}
		if (CollectionUtils.isNotEmpty(vehicleList)) {
			vehicleList.forEach(e -> {
				e.setReceiptDate(null);
				e.setReceiptQuantity(null);
				e.setReceiptRemark(null);
			});
			transportOrderVehicleService.batchUpdate(vehicleList);
		}
		List<Order> orders = new ArrayList<>(ordersSet);
		orderService.batchUpdate(orders);
		deliverGoodsService.batchUpdate(oldGoods);
		// 关联新的发货单
		this.relate(signReceipt, deliverGoodsList);
		signReceipt.setContractName(contractService
				.findOne(signReceipt.getContractId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30151001))
				.getName());
		return Optional.of(super.update(signReceipt));
	}

	/**
	 * @description: 删除签收单，更新发货单信息
	 * @param: [resource]
	 * @return: void
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(SignReceipt resource) {
		// 删除签收单前，先将签收单关联的发货单的签收信息清空
		Contract contract = contractService.findOne(resource.getContractId())
				.orElse(new Contract());
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findByIds(resource.getRelatedDeliverGoodsIds());
		List<String> deliverGoodsIds = deliverGoodsList.stream()
				.map(DeliverGoods::getId).toList();
		// 原先关联的汽运船运的签收相关都还原
		List<TransportOrderShip> oldShipList = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(oldShipList)) {
			for (TransportOrderShip transportOrderShip : oldShipList) {
				transportOrderShip.setReceiptDate(null);
				transportOrderShip.setReceiptQuantity(null);
				transportOrderShip.setReceiptRemark(null);
			}
			transportOrderShipService.batchUpdate(oldShipList);
		}
		List<TransportOrderVehicle> oldVehicleList = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(oldVehicleList)) {
			for (TransportOrderVehicle transportOrderVehicle : oldVehicleList) {
				transportOrderVehicle.setReceiptDate(null);
				transportOrderVehicle.setReceiptQuantity(null);
				transportOrderVehicle.setReceiptRemark(null);
			}
			transportOrderVehicleService.batchUpdate(oldVehicleList);
		}
		List<TransportOrderRailway> oldRailwayList = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(oldRailwayList)) {
			for (TransportOrderRailway transportOrderRailway : oldRailwayList) {
				transportOrderRailway.setReceiptDate(null);
				transportOrderRailway.setReceiptQuantity(null);
				transportOrderRailway.setReceiptRemark(null);
			}
			transportOrderRailwayService.batchUpdate(oldRailwayList);
		}
		List<String> orderIds = deliverGoodsList.stream()
				.map(DeliverGoods::getOrderId).distinct().toList();
		List<Order> orderList = orderService.findByIds(orderIds);
		Map<String, Order> orderMap = orderList.stream()
				.collect(Collectors.toMap(Order::getId, e -> e));
		for (DeliverGoods deliverGoods : deliverGoodsList) {
			Order order = orderMap.get(deliverGoods.getOrderId());
			if (Objects.nonNull(deliverGoods.getReceiptWeight())
					&& Objects.nonNull(order.getReceivedWeight())
					&& ContractDef.Type.BUY.match(contract.getContractType())) {
				order.setReceivedWeight(order.getReceivedWeight()
						.subtract(deliverGoods.getReceiptWeight()));
			}
			orderMap.put(order.getId(), order);
			deliverGoods.setReceiptWeight(null);
			deliverGoods.setReceiveWay(null);
			deliverGoods.setSignReceiptId(null);
			if (Objects.nonNull(deliverGoods.getGoodsInfo())) {
				List<GoodsInfo> list = this
						.convertGoodsInfo(deliverGoods.getGoodsInfo());
				for (GoodsInfo goodsInfo : list) {
					goodsInfo.setReceiptQuantity(null);
					goodsInfo.setReceiptDate(null);
					goodsInfo.setReceiptRemark(null);
					goodsInfo.setReceiptWay(null);
				}
				deliverGoods.setGoodsInfo(deliverGoodsService
						.convertJson(deliverGoods.getGoodsInfo(), list));
			}
		}
		List<Order> orders = new ArrayList<>(orderMap.values());
		orderService.batchUpdate(orders);
		deliverGoodsService.batchUpdate(deliverGoodsList);
		// 更新订单中的签收单状态
		if (ContractDef.Type.SELL.match(contract.getContractType())) {
			this.changeOrderStatus(orderIds, List.of());
		}
		super.delete(resource.getId());
	}

	// 删除签收单前，先将签收单关联的发货单的签收信息清空
	@Override
	public void revertDeliverAndOrder(SignReceipt resource) {
		Contract contract = contractService.findOne(resource.getContractId())
				.orElse(new Contract());
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findByIds(resource.getRelatedDeliverGoodsIds());
		List<String> deliverGoodsIds = deliverGoodsList.stream()
				.map(DeliverGoods::getId).toList();
		Map<String, DeliverGoods> deliverGoodsMap = deliverGoodsList.stream()
				.collect(Collectors.toMap(DeliverGoods::getId, e -> e));
		List<GoodsInfo> goodsInfoList = new ArrayList<>();
		for (DeliverGoods deliverGoods : deliverGoodsList) {
			if (Objects.nonNull(deliverGoods.getGoodsInfo())) {
				List<GoodsInfo> list = this
						.convertGoodsInfo(deliverGoods.getGoodsInfo());
				list.forEach(goodsInfo -> {
					goodsInfo.setOrderId(deliverGoods.getOrderId());
					goodsInfo.setDeliverGoodsId(deliverGoods.getId());
					goodsInfo.setTransportType(
							DeliverGoodsDef.DeliverWay.SELF_PICKUP.getCode());
					goodsInfo.setReceiptWay(deliverGoods.getReceiveWay());
					if (DeliverGoodsDef.ReceiveWay.MERGE
							.match(deliverGoods.getReceiveWay())) {
						goodsInfo.setReceiptQuantity(
								deliverGoods.getReceiptWeight());
					}
				});
				goodsInfoList.addAll(list);
			}
		}
		// 原先关联的汽运船运的签收相关都还原
		List<TransportOrderShip> oldShipList = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(oldShipList)) {
			for (TransportOrderShip transportOrderShip : oldShipList) {
				GoodsInfo goodsInfo = new GoodsInfo();
				goodsInfo.setOrderId(transportOrderShip.getOrderId());
				goodsInfo.setDeliverGoodsId(transportOrderShip.getGoodsId());
				goodsInfo.setTransportType(
						DeliverGoodsDef.DeliverWay.SHIPPING.getCode());
				goodsInfo.setModel(transportOrderShip.getModel());
				goodsInfo.setGoodsName(transportOrderShip.getGoodsType());
				goodsInfo.setDeliveryQuantity(
						new BigDecimal(transportOrderShip.getTon()));
				goodsInfo.setReceiptWay(deliverGoodsMap
						.get(transportOrderShip.getGoodsId()).getReceiveWay());
				if (DeliverGoodsDef.ReceiveWay.SEPARATE.match(
						deliverGoodsMap.get(transportOrderShip.getGoodsId())
								.getReceiveWay())) {
					goodsInfo.setReceiptQuantity(
							transportOrderShip.getReceiptQuantity());
				} else {
					goodsInfo.setReceiptQuantity(
							deliverGoodsMap.get(transportOrderShip.getGoodsId())
									.getReceiptWeight());
				}
				goodsInfo.setReceiptDate(transportOrderShip.getReceiptDate());
				goodsInfo.setReceiptRemark(
						transportOrderShip.getReceiptRemark());
				goodsInfo.setTransportToolsName(
						transportOrderShip.getShipName());
				goodsInfo.setShipId(transportOrderShip.getId());
				goodsInfoList.add(goodsInfo);

				transportOrderShip.setReceiptDate(null);
				transportOrderShip.setReceiptQuantity(null);
				transportOrderShip.setReceiptRemark(null);
			}
			transportOrderShipService.batchUpdate(oldShipList);
		}
		List<TransportOrderVehicle> oldVehicleList = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(oldVehicleList)) {
			for (TransportOrderVehicle transportOrderVehicle : oldVehicleList) {
				GoodsInfo goodsInfo = new GoodsInfo();
				goodsInfo.setOrderId(transportOrderVehicle.getOrderId());
				goodsInfo.setDeliverGoodsId(
						transportOrderVehicle.getDeliverGoodsId());
				goodsInfo.setTransportType(
						DeliverGoodsDef.DeliverWay.CAR.getCode());
				goodsInfo.setGoodsName(transportOrderVehicle.getGoodsName());
				goodsInfo.setModel(transportOrderVehicle.getGoodsType());
				goodsInfo.setDeliveryQuantity(
						transportOrderVehicle.getTransportWeight());
				goodsInfo.setReceiptWay(deliverGoodsMap
						.get(transportOrderVehicle.getDeliverGoodsId())
						.getReceiveWay());
				if (DeliverGoodsDef.ReceiveWay.SEPARATE.match(deliverGoodsMap
						.get(transportOrderVehicle.getDeliverGoodsId())
						.getReceiveWay())) {
					goodsInfo.setReceiptQuantity(
							transportOrderVehicle.getReceiptQuantity());
				} else {
					goodsInfo.setReceiptQuantity(deliverGoodsMap
							.get(transportOrderVehicle.getDeliverGoodsId())
							.getReceiptWeight());
				}
				goodsInfo
						.setReceiptDate(transportOrderVehicle.getReceiptDate());
				goodsInfo.setReceiptRemark(
						transportOrderVehicle.getReceiptRemark());
				goodsInfo.setTransportToolsName(
						transportOrderVehicle.getDriverLicensePlate());
				goodsInfo.setVehicleId(transportOrderVehicle.getId());
				goodsInfoList.add(goodsInfo);

				transportOrderVehicle.setReceiptDate(null);
				transportOrderVehicle.setReceiptQuantity(null);
				transportOrderVehicle.setReceiptRemark(null);
			}
			transportOrderVehicleService.batchUpdate(oldVehicleList);
		}
		List<TransportOrderRailway> oldRailwayList = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		if (CollectionUtils.isNotEmpty(oldRailwayList)) {
			for (TransportOrderRailway transportOrderRailway : oldRailwayList) {
				GoodsInfo goodsInfo = new GoodsInfo();
				goodsInfo.setOrderId(transportOrderRailway.getOrderId());
				goodsInfo.setDeliverGoodsId(
						transportOrderRailway.getDeliverGoodsId());
				goodsInfo.setTransportType(
						DeliverGoodsDef.DeliverWay.TRAIN.getCode());
				goodsInfo.setGoodsName(transportOrderRailway.getGoodsName());
				goodsInfo.setModel(transportOrderRailway.getModel());
				goodsInfo.setDeliveryQuantity(
						transportOrderRailway.getTransportWeight());
				goodsInfo.setReceiptWay(deliverGoodsMap
						.get(transportOrderRailway.getDeliverGoodsId())
						.getReceiveWay());
				if (DeliverGoodsDef.ReceiveWay.SEPARATE.match(deliverGoodsMap
						.get(transportOrderRailway.getDeliverGoodsId())
						.getReceiveWay())) {
					goodsInfo.setReceiptQuantity(
							transportOrderRailway.getReceiptQuantity());
				} else {
					goodsInfo.setReceiptQuantity(deliverGoodsMap
							.get(transportOrderRailway.getDeliverGoodsId())
							.getReceiptWeight());
				}
				goodsInfo
						.setReceiptDate(transportOrderRailway.getReceiptDate());
				goodsInfo.setReceiptRemark(
						transportOrderRailway.getReceiptRemark());
				goodsInfo.setTransportToolsName(
						transportOrderRailway.getCarTypeNumber());
				goodsInfo.setRailwayId(transportOrderRailway.getId());

				transportOrderRailway.setReceiptDate(null);
				transportOrderRailway.setReceiptQuantity(null);
				transportOrderRailway.setReceiptRemark(null);
			}
			transportOrderRailwayService.batchUpdate(oldRailwayList);
		}
		List<String> orderIds = deliverGoodsList.stream()
				.map(DeliverGoods::getOrderId).distinct().toList();
		for (DeliverGoods deliverGoods : deliverGoodsList) {
			deliverGoods.setReceiptWeight(null);
			deliverGoods.setReceiveWay(null);
			deliverGoods.setSignReceiptId(null);
			if (Objects.nonNull(deliverGoods.getGoodsInfo())) {
				List<GoodsInfo> list = this
						.convertGoodsInfo(deliverGoods.getGoodsInfo());
				for (GoodsInfo goodsInfo : list) {
					goodsInfo.setReceiptQuantity(null);
					goodsInfo.setReceiptDate(null);
					goodsInfo.setReceiptRemark(null);
					goodsInfo.setReceiptWay(null);
				}
				deliverGoods.setGoodsInfo(deliverGoodsService
						.convertJson(deliverGoods.getGoodsInfo(), list));
			}
		}
		deliverGoodsService.batchUpdate(deliverGoodsList);
		// 更新订单中的签收单状态
		if (ContractDef.Type.SELL.match(contract.getContractType())) {
			this.changeOrderStatus(orderIds, List.of());
		}
		resource.setDeliveredInfo(JSONArray.toJSONString(goodsInfoList));
		super.update(resource);
	}

	/*
	 * @description: 作废签收单修改关联订单签收重量
	 *
	 * @author: pp
	 *
	 * @date: 2025/6/6 9:54
	 *
	 * @param: [resource, flag]
	 *
	 * @return: void
	 **/
	@Override
	public void changeOrderReceiptWeight(SignReceipt resource, Boolean flag) {
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findByIds(resource.getRelatedDeliverGoodsIds());
		List<String> orderIds = deliverGoodsList.stream()
				.map(DeliverGoods::getOrderId).distinct().toList();
		List<Order> orderList = orderService.findByIds(orderIds);
		Map<String, Order> orderMap = orderList.stream()
				.collect(Collectors.toMap(Order::getId, e -> e));
		for (DeliverGoods deliverGoods : deliverGoodsList) {
			Order order = orderMap.get(deliverGoods.getOrderId());
			if (Objects.nonNull(deliverGoods.getReceiptWeight())) {
				if (flag) {
					order.setReceivedWeight(order.getReceivedWeight()
							.subtract(deliverGoods.getReceiptWeight()));
				} else {
					if (Objects.isNull(order.getReceivedWeight())) {
						order.setReceivedWeight(BigDecimal.ZERO);
					}
					order.setReceivedWeight(order.getReceivedWeight()
							.add(deliverGoods.getReceiptWeight()));
				}
			}
			orderMap.put(order.getId(), order);
		}
		List<Order> orders = new ArrayList<>(orderMap.values());
		orderService.batchUpdate(orders);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteBuy(SignReceipt resource) {
		List<DeliverGoods> deliverGoods = deliverGoodsService
				.findByIds(resource.getRelatedDeliverGoodsIds());
		for (DeliverGoods goods : deliverGoods) {
			goods.setReceiveWay(null);
			List<GoodsInfo> list = this.convertGoodsInfo(goods.getGoodsInfo());
			for (GoodsInfo goodsInfo : list) {
				BigDecimal receiptQuantity = goodsInfo.getReceiptQuantity();
				goods.setReceiptWeight(Objects.isNull(receiptQuantity)
						? goods.getReceiptWeight()
						: goods.getReceiptWeight().subtract(receiptQuantity));
				goodsInfo.setReceiptQuantity(null);
				goodsInfo.setReceiptDate(null);
				goodsInfo.setReceiptRemark(null);
			}
			Gson gson = new GsonBuilder().registerTypeAdapter(
					LocalDateTime.class, new LocalDateTimeAdapter()).create();
			goods.setGoodsInfo(gson.toJson(list));
			goods.setSignReceiptId(null);
			goods.setReceiptWeight(null);
		}
		deliverGoodsService.batchUpdate(deliverGoods);
		super.delete(resource.getId());
	}

	/**
	 * @description: 驳回操作
	 * @author: 彭湃
	 * @date: 2025/1/15 13:45
	 * @param: [resource]
	 * @return: com.zhihaoscm.domain.bean.entity.SignReceipt
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<SignReceipt> revert(SignReceipt resource,
			Boolean isRevoke) {
		if (Objects.nonNull(resource.getSignReceiptFileId())
				&& OrderDef.SignType.ONLINE.match(resource.getSignType())) {
			fileService.batchUnActive(List.of(resource.getSignReceiptFileId()));
			resource.setSignReceiptFileId(null);
			if (isRevoke
					&& OrderDef.SignType.ONLINE.match(resource.getSignType())) {
				// 撤销合同
				contractRecordService.revoke(resource.getId(),
						PurchaseContractDef.CorrelationTable.GOODS_RECEIPT);
			}
		}
		resource.setStatus(SignReceiptDef.Status.REVERTED.getCode());
		if (SignReceiptDef.Type.SELL.match(resource.getType())) {
			if (OrderDef.SignType.OFFLINE.match(resource.getSignType())
					&& CommonDef.AccountSource.INNER
							.match(resource.getInitiator())) {
				SpringUtil.getBean(SignReceiptService.class).notice(resource,
						3);
			}
		} else {
			if (OrderDef.SignType.OFFLINE.match(resource.getSignType())
					&& CommonDef.AccountSource.CUSTOM
							.match(resource.getInitiator())) {
				SpringUtil.getBean(SignReceiptService.class).notice(resource,
						3);
			}
		}

		return Optional.of(this.update(resource));
	}

	/**
	 * @description: 确认操作
	 * @author: 彭湃
	 * @date: 2025/1/15 13:45
	 * @param: [resource]
	 * @return: com.zhihaoscm.domain.bean.entity.SignReceipt
	 **/
	@Override
	public Optional<SignReceipt> confirm(SignReceipt resource) {

		Contract contract = contractService.findOne(resource.getContractId())
				.orElse(new Contract());
		resource.setStatus(SignReceiptDef.Status.FINISHED.getCode());
		resource.setReconciliationStatus(
				OrderDef.BusinessStatus.NOT_STARTED.getCode());
		SignReceipt signReceipt = this.updateAllProperties(resource);

		// 更新订单中的签收单状态
		if (ContractDef.Type.SELL.match(contract.getContractType())) {
			List<DeliverGoods> deliverGoodsList = deliverGoodsService
					.findByIds(resource.getRelatedDeliverGoodsIds());
			List<String> orderIds = deliverGoodsList.stream()
					.map(DeliverGoods::getOrderId).toList();
			Map<String, List<DeliverGoods>> collect = deliverGoodsList.stream()
					.collect(Collectors.groupingBy(DeliverGoods::getOrderId));
			List<Order> orderList = orderService.findByIds(orderIds);
			// 完成签收单后变更订单中的签收重量
			this.changeOrderReceiptWeight(orderList, collect);
			// 签收单完成后判断订单中的签收单状态是否可以改为已完成状态
			this.completedChangeOrderStatus(orderIds);
		}

		if (CommonDef.AccountSource.INNER.match(resource.getSignType())) {
			SpringUtil.getBean(SignReceiptService.class).notice(resource, 2);
		}
		return Optional.of(signReceipt);
	}

	/**
	 * @description: 完成签收单后变更订单中的签收重量
	 * @param: [orderIds]
	 * @return: void
	 **/
	@Override
	public void changeOrderReceiptWeight(List<Order> orderList,
			Map<String, List<DeliverGoods>> collect) {
		Map<String, Order> orderMap = orderList.stream()
				.collect(Collectors.toMap(Order::getId, i -> i));
		List<Order> list = new ArrayList<>();
		for (Map.Entry<String, List<DeliverGoods>> entry : collect.entrySet()) {
			Order order = orderMap.get(entry.getKey());
			entry.getValue().forEach(e -> {
				BigDecimal sum;
				sum = Objects.nonNull(e.getReceiptWeight())
						? e.getReceiptWeight()
						: BigDecimal.ZERO;
				if (Objects.isNull(order.getReceivedWeight())) {
					order.setReceivedWeight(sum);
				} else {
					order.setReceivedWeight(order.getReceivedWeight().add(sum));
				}
			});
			list.add(order);
		}
		orderService.batchUpdate(list);
	}

	@Override
	public Optional<SignReceipt> revoke(SignReceipt signReceipt) {
		signReceipt.setStatus(SignReceiptDef.Status.DRAFT.getCode());
		return Optional.of(super.updateAllProperties(signReceipt));
	}

	/**
	 * @description: 发起签署
	 * @author: 彭湃
	 * @date: 2025/1/15 15:13
	 * @param: [resource]
	 * @return: com.zhihaoscm.domain.bean.entity.SignReceipt
	 **/
	@Transactional(rollbackFor = Exception.class)
	@Override
	public Optional<ContractPageResponse> initiateSign(SignReceipt resource,
			Integer origin, Integer initiateType) {

		Long fileId = this.getFileId(resource);
		if (Objects.nonNull(fileId)) {
			resource.setSignReceiptFileId(fileId);
			this.updateAllProperties(resource);
		} else {
			log.info("订单生成pdf文件失败");
			return Optional.empty();
		}
		// 设置供应链签署人
		String name = "";
		switch (CommonDef.AccountSource.from(initiateType)) {
			case CUSTOM -> {
				name = resource.getPurchaserEnterprise().getName() + "签收单";
				User user1 = adminSealService
						.findByType(SignerSettings.billType.receipt)
						.orElse(null);
				if (Objects.nonNull(user1)) {
					resource.setSupplierSigner(user1.getName());
					resource.setSupplierSignerId(user1.getId());
				}
			}
			case INNER -> {
				name = resource.getSellerEnterprise().getName() + "签收单";
				User user = UserContextHolder.getUser();
				if (Objects.nonNull(user)) {
					resource.setSupplierSigner(user.getName());
					resource.setSupplierSignerId(user.getId());
				}
			}
		}

		// 发起合同
		Map<Long, String> customerMap = contractRecordService.draft(name,
				List.of(resource.getPurchaserId(), resource.getSellerId()),
				List.of(resource.getSignReceiptFileId()), resource.getId(),
				PurchaseContractDef.CorrelationTable.GOODS_RECEIPT, null, null);
		// 设置文件id
		resource.setSignReceiptFileId(
				contractRecordService.download(resource.getId(),
						PurchaseContractDef.CorrelationTable.GOODS_RECEIPT));

		resource.getPurchaserEnterprise()
				.setSignMobile(customerMap.get(resource.getPurchaserId()));
		resource.getSellerEnterprise()
				.setSignMobile(customerMap.get(resource.getSellerId()));

		resource.setStatus(SignReceiptDef.Status.SIGNING.getCode());
		resource.setSignStatus(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode());
		this.updateAllProperties(resource);

		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				resource.getPurchaserEnterprise().getSignMobile(),
				resource.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}

		// 获取签署链接
		return contractRecordService.sign(resource.getId(),
				PurchaseContractDef.CorrelationTable.GOODS_RECEIPT, origin);

	}

	@Override
	public Optional<ContractPageResponse> signing(SignReceipt signReceipt,
			Integer origin) {
		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				signReceipt.getPurchaserEnterprise().getSignMobile(),
				signReceipt.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}
		return contractRecordService.sign(signReceipt.getId(),
				PurchaseContractDef.CorrelationTable.GOODS_RECEIPT, origin);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Optional<ContractPageResponse> invalid(SignReceipt signReceipt,
			Integer origin) {
		// 调用契约锁撤销合同接口
		contractRecordService.revoke(signReceipt.getId(),
				PurchaseContractDef.CorrelationTable.GOODS_RECEIPT);
		// 作废后获取作废合同id
		Long fileId = contractRecordService.detail(signReceipt.getId(),
				PurchaseContractDef.CorrelationTable.GOODS_RECEIPT);
		signReceipt.setInvalidRevokeReason(null);
		signReceipt.setInvalidRevokeTime(null);
		signReceipt.setPurchaseInvalidTime(null);
		signReceipt.setSellerInvalidTime(null);
		signReceipt.setInvalidFileId(fileId);
		signReceipt.setStatus(SignReceiptDef.Status.INVALIDING.getCode());
		signReceipt.setInvalidSignState(
				PurchaseContractDef.CommonSignState.UNSIGNED.getCode());

		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findByIds(signReceipt.getRelatedDeliverGoodsIds());
		List<String> orderIds = deliverGoodsList.stream()
				.map(DeliverGoods::getOrderId).distinct().toList();
		this.changeOrderStatus(orderIds, List.of());

		this.changeOrderReceiptWeight(signReceipt, true);

		super.updateAllProperties(signReceipt);

		if (SignReceiptDef.Type.SELL.match(signReceipt.getType())) {
			if (CommonDef.UserType.INNER.match(origin)) {
				this.sendNotice(signReceipt,
						wxSubscriptionProperties.getReceiptNullifyConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.SIGN_RECEIPT_INVALID_TEMPLATE,
								signReceipt.getId()),
						SignReceiptDef.Type.SELL.getCode());
			} else {
				SpringUtil.getBean(SignReceiptService.class).notice(signReceipt,
						5);
			}
		} else {
			if (CommonDef.UserType.INNER.match(origin)) {
				SpringUtil.getBean(SignReceiptService.class).notice(signReceipt,
						5);
			} else {
				this.sendNotice(signReceipt,
						wxSubscriptionProperties.getReceiptNullifyConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.SIGN_RECEIPT_INVALID_TEMPLATE,
								signReceipt.getId()),
						SignReceiptDef.Type.BUY.getCode());
			}
		}

		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				signReceipt.getPurchaserEnterprise().getSignMobile(),
				signReceipt.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}
		return contractRecordService.sign(signReceipt.getId(),
				PurchaseContractDef.CorrelationTable.GOODS_RECEIPT,
				CertificationDef.Origin.PC.getCode());

	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Optional<SignReceipt> invalidOffLine(SignReceipt signReceipt,
			Integer initiator) {
		// 作废合同
		signReceipt.setStatus(SignReceiptDef.Status.INVALIDING.getCode());
		signReceipt.setInvalidInitiator(initiator);
		signReceipt.setInvalidRevokeReason(null);
		signReceipt.setInvalidRevokeTime(null);
		signReceipt.setPurchaseInvalidTime(null);
		signReceipt.setSellerInvalidTime(null);

		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findByIds(signReceipt.getRelatedDeliverGoodsIds());
		List<String> orderIds = deliverGoodsList.stream()
				.map(DeliverGoods::getOrderId).distinct().toList();
		this.changeOrderStatus(orderIds, List.of());

		this.changeOrderReceiptWeight(signReceipt, true);

		if (SignReceiptDef.Type.SELL.match(signReceipt.getType())) {
			if (CommonDef.UserType.INNER.match(initiator)) {
				signReceipt.setInvalidSignState(
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
				signReceipt.setSellerInvalidTime(LocalDateTime.now());
				this.sendNotice(signReceipt,
						wxSubscriptionProperties.getReceiptNullifyConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.SIGN_RECEIPT_INVALID_TEMPLATE,
								signReceipt.getId()),
						SignReceiptDef.Type.SELL.getCode());
			} else {
				signReceipt.setInvalidSignState(
						BusinessContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				signReceipt.setPurchaseInvalidTime(LocalDateTime.now());
				SpringUtil.getBean(SignReceiptService.class).notice(signReceipt,
						5);
			}
		} else {
			if (CommonDef.UserType.INNER.match(initiator)) {
				signReceipt.setInvalidSignState(
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
				signReceipt.setSellerInvalidTime(LocalDateTime.now());
				SpringUtil.getBean(SignReceiptService.class).notice(signReceipt,
						5);
			} else {
				signReceipt.setInvalidSignState(
						BusinessContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				signReceipt.setPurchaseInvalidTime(LocalDateTime.now());
				this.sendNotice(signReceipt,
						wxSubscriptionProperties.getReceiptNullifyConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.SIGN_RECEIPT_INVALID_TEMPLATE,
								signReceipt.getId()),
						SignReceiptDef.Type.BUY.getCode());
			}
		}
		return Optional.of(super.updateAllProperties(signReceipt));
	}

	@Override
	public Optional<SignReceipt> confirmInvalid(SignReceipt signReceipt) {
		// 确认作废合同
		signReceipt.setStatus(SignReceiptDef.Status.INVALID.getCode());
		signReceipt.setInvalidSignState(
				BusinessContractDef.CommonSignState.COMPLETED.getCode());
		if (CommonDef.UserType.INNER.match(signReceipt.getInvalidInitiator())) {
			signReceipt.setPurchaseInvalidTime(LocalDateTime.now());
		} else {
			signReceipt.setSellerInvalidTime(LocalDateTime.now());
		}
		revertDeliverAndOrder(signReceipt);
		return Optional.of(super.updateAllProperties(signReceipt));
	}

	@Override
	public Optional<SignReceipt> revertInvalid(SignReceipt signReceipt) {
		// 撤销作废合同
		signReceipt.setStatus(SignReceiptDef.Status.FINISHED.getCode());
		signReceipt.setInvalidSignState(null);
		signReceipt.setInvalidRevokeTime(LocalDateTime.now());
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findByIds(signReceipt.getRelatedDeliverGoodsIds());
		List<String> orderIds = deliverGoodsList.stream()
				.map(DeliverGoods::getOrderId).toList();
		SignReceipt signReceipt1 = super.updateAllProperties(signReceipt);
		// 签收单完成后判断订单中的签收单状态是否可以改为已完成状态
		this.completedChangeOrderStatus(orderIds);

		this.changeOrderReceiptWeight(signReceipt1, false);

		if (SignReceiptDef.Type.SELL.match(signReceipt.getType())) {
			if (CommonDef.UserType.INNER
					.match(signReceipt.getInvalidInitiator())) {
				SpringUtil.getBean(SignReceiptService.class).notice(signReceipt,
						6);
			} else {
				this.sendNotice(signReceipt,
						wxSubscriptionProperties.getReceiptNullifyDismissCode(),
						MessageFormat.format(
								UserMessageConstants.SIGN_RECEIPT_INVALID_DISMISS_TEMPLATE,
								signReceipt.getId()),
						SignReceiptDef.Type.SELL.getCode());
			}
		} else {
			if (CommonDef.UserType.INNER
					.match(signReceipt.getInvalidInitiator())) {
				this.sendNotice(signReceipt,
						wxSubscriptionProperties.getReceiptNullifyDismissCode(),
						MessageFormat.format(
								UserMessageConstants.SIGN_RECEIPT_INVALID_DISMISS_TEMPLATE,
								signReceipt.getId()),
						SignReceiptDef.Type.BUY.getCode());
			} else {
				SpringUtil.getBean(SignReceiptService.class).notice(signReceipt,
						6);
			}
		}
		return Optional.of(signReceipt1);
	}

	@Override
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = "{{#success}}", type = LogDef.RECEIPT_INFO, bizNo = "{{#resource.getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#resource.getId()}}"),
			@LogRecord.KeyValuePair(key = "#projectId#", value = "{{#code}}") }, messageType = LogDef.MESSAGE_TYPE_ORDER, permission = LogDef.PROJECT_DEAL)
	public void notice(SignReceipt resource, Integer type) {
		Project project = projectService.findOne(resource.getProjectId())
				.orElse(new Project());
		LogRecordContext.putVariable("code", project.getName());
		switch (type) {
			case 1 -> LogRecordContext.putVariable("success",
					LogDef.SIGN_FOR_PENDING_CONFIRMATION);
			case 2 -> LogRecordContext.putVariable("success",
					LogDef.RECEIPT_CONFIRMED);
			case 3 -> LogRecordContext.putVariable("success",
					LogDef.RECEIPT_REJECTED);
			case 4 -> LogRecordContext.putVariable("success",
					LogDef.SIGN_IN_COMPLETED);
			case 5 -> LogRecordContext.putVariable("success",
					LogDef.SIGN_IN_INVALID_ADD);
			case 6 -> LogRecordContext.putVariable("success",
					LogDef.SIGN_IN_INVALID_REJECTED);
			default -> {
			}
		}
		log.info("签收发送通知:{}", resource.getId());
	}

	/**
	 * @description: 变更签收单信息，更新发货单信息
	 * @author: 彭湃
	 * @date: 2025/1/14 14:49
	 * @param: [signReceipt,
	 *             deliverGoodsList]
	 * @return: void
	 **/
	private void fillInfo(SignReceipt signReceipt,
			List<DeliverDto> deliverDtoList) {
		BigDecimal totalWeight = BigDecimal.ZERO;
		ArrayString deliverGoodsIds = new ArrayString();
		for (DeliverDto deliverDto : deliverDtoList) {
			DeliverGoods deliverGoods = deliverDto.getDeliverGoods();
			if (DeliverGoodsDef.ReceiveWay.SEPARATE
					.match(deliverGoods.getReceiveWay())) {
				// 签收方式为多条签收，计算每条签收重量
				DeliverGoodsDef.DeliverWay from = DeliverGoodsDef.DeliverWay
						.from(deliverGoods.getDelivery());
				switch (from) {
					case SELF_PICKUP -> {
						List<GoodsInfo> goodsInfoList = convertGoodsInfo(
								deliverGoods.getGoodsInfo());
						BigDecimal totalQuantity = BigDecimal.ZERO;
						for (GoodsInfo goodsInfo : goodsInfoList) {
							totalQuantity = totalQuantity
									.add(goodsInfo.getReceiptQuantity());
						}
						totalWeight = totalWeight.add(totalQuantity);
						deliverGoods.setReceiptWeight(totalQuantity);
					}
					case SHIPPING -> {
						List<TransportOrderShip> transportOrderShips = deliverDto
								.getTransportOrderShips();
						BigDecimal totalQuantity = BigDecimal.ZERO;
						for (TransportOrderShip transportOrderShip : transportOrderShips) {
							totalQuantity = totalQuantity.add(
									transportOrderShip.getReceiptQuantity());
						}
						totalWeight = totalWeight.add(totalQuantity);
						deliverGoods.setReceiptWeight(totalQuantity);
					}
					case CAR -> {
						List<TransportOrderVehicle> transportOrderVehicles = deliverDto
								.getTransportOrderVehicles();
						BigDecimal totalQuantity = BigDecimal.ZERO;
						for (TransportOrderVehicle transportOrderVehicle : transportOrderVehicles) {
							totalQuantity = totalQuantity.add(
									transportOrderVehicle.getReceiptQuantity());
						}
						totalWeight = totalWeight.add(totalQuantity);
						deliverGoods.setReceiptWeight(totalQuantity);
					}
					case TRAIN -> {
						List<TransportOrderRailway> transportOrderRailways = deliverDto
								.getTransportOrderRailways();
						BigDecimal totalQuantity = BigDecimal.ZERO;
						for (TransportOrderRailway transportOrderRailway : transportOrderRailways) {
							totalQuantity = totalQuantity.add(
									transportOrderRailway.getReceiptQuantity());
						}
						totalWeight = totalWeight.add(totalQuantity);
						deliverGoods.setReceiptWeight(totalQuantity);
					}
				}

			} else {
				// 签收方式为一次签收，直接取发货单重量
				totalWeight = totalWeight.add(deliverGoods.getReceiptWeight());
			}
			deliverGoods.setSignReceiptId(signReceipt.getId());
			deliverGoodsIds.add(deliverGoods.getId());
		}
		signReceipt.setRelatedDeliverGoodsIds(deliverGoodsIds);
		signReceipt.setReceiptWeight(totalWeight);
	}

	private List<SignReceiptVo> packVos(List<SignReceipt> list,
			List<Contract> contracts, String orderId) {

		if (CollectionUtils.isEmpty(contracts)) {
			contracts = contractService.findByIds(list.stream()
					.map(SignReceipt::getContractId).distinct().toList());
		}
		// 查询合同信息
		List<String> projectIds = list.stream().map(SignReceipt::getProjectId)
				.distinct().toList();

		Map<String, DeliverGoods> deliverGoodsMap;
		if (StringUtils.isNotBlank(orderId)) {
			List<DeliverGoods> deliverGoods = deliverGoodsService
					.findByOrderIds(List.of(orderId));
			deliverGoodsMap = deliverGoods.stream()
					.collect(Collectors.toMap(DeliverGoods::getId, e -> e));
		} else {
			deliverGoodsMap = null;
		}

		Map<String, Project> projectMap = projectService.findByIds(projectIds)
				.stream().collect(Collectors.toMap(Project::getId, e -> e));

		Map<String, Contract> contractMap = contracts.stream()
				.collect(Collectors.toMap(Contract::getId, e -> e));
		return list.stream().map(e -> {
			SignReceiptVo signReceiptVo = new SignReceiptVo();

			if (Objects.nonNull(deliverGoodsMap)
					&& !SignReceiptDef.Status.INVALID.match(e.getStatus())) {
				ArrayString relatedDeliverGoodsIds = e
						.getRelatedDeliverGoodsIds();
				if (CollectionUtils.isNotEmpty(relatedDeliverGoodsIds)) {
					BigDecimal sum = BigDecimal.ZERO;
					for (String id : relatedDeliverGoodsIds) {
						DeliverGoods item = deliverGoodsMap.get(id);
						if (Objects.nonNull(item)
								&& Objects.nonNull(item.getReceiptWeight())) {
							sum = sum.add(item.getReceiptWeight());
						}
					}
					e.setReceiptWeight(sum);
				}
			}

			if (SignReceiptDef.Status.INVALID.match(e.getStatus())
					&& Objects.nonNull(e.getDeliveredInfo())
					&& Objects.nonNull(orderId)) {
				List<GoodsInfo> goodsInfoList = convertGoodsInfo(
						e.getDeliveredInfo());
				List<GoodsInfo> mergeList = goodsInfoList.stream()
						.filter(k -> DeliverGoodsDef.ReceiveWay.MERGE
								.match(k.getReceiptWay()))
						.toList();
				List<GoodsInfo> separateList = goodsInfoList.stream()
						.filter(k -> DeliverGoodsDef.ReceiveWay.SEPARATE
								.match(k.getReceiptWay()))
						.toList();
				List<GoodsInfo> result = new ArrayList<>(separateList);
				Map<String, List<GoodsInfo>> collect = Map.of();
				if (CollectionUtils.isNotEmpty(mergeList)) {
					collect = mergeList.stream().collect(Collectors
							.groupingBy(GoodsInfo::getDeliverGoodsId));
				}
				if (CollectionUtils.isNotEmpty(collect)) {
					for (Map.Entry<String, List<GoodsInfo>> entry : collect
							.entrySet()) {
						List<GoodsInfo> value = entry.getValue();
						if (CollectionUtils.isNotEmpty(value)) {
							result.add(value.get(0));
						}
					}
				}
				e.setReceiptWeight(BigDecimal.ZERO);
				result.stream().filter(k -> k.getOrderId().equals(orderId))
						.forEach(h -> {
							if (Objects.nonNull(h.getReceiptQuantity())) {
								e.setReceiptWeight(e.getReceiptWeight()
										.add(h.getReceiptQuantity()));
							}
						});
			}

			signReceiptVo.setContract(contractMap.get(e.getContractId()));
			signReceiptVo.setSignReceipt(e);
			signReceiptVo.setProject(projectMap.get(e.getProjectId()));
			return signReceiptVo;
		}).toList();

	}

	private List<SignReceiptVo> packSignReceipts(List<SignReceipt> list,
			Contract contract) {
		if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
			return List.of();
		}
		// 查询项目信息
		List<String> projectIds = list.stream().map(SignReceipt::getProjectId)
				.distinct().toList();
		Map<String, Project> projectMap = projectService.findByIds(projectIds)
				.stream().collect(Collectors.toMap(Project::getId, e -> e));
		// 根据签收信息查询出所有的签收单id
		List<String> signReceiptList = list.stream().map(SignReceipt::getId)
				.distinct().toList();
		// 根据签收单找到所有发货单id
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findBySignReceiptIds(signReceiptList);
		// 发货单的所有id
		List<String> deliverGoodsIds = deliverGoodsList.stream()
				.map(DeliverGoods::getId).distinct().toList();
		// 船运单信息
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		// 汽运单信息
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		// 铁路单信息
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		// 货物信息
		List<GoodsInfo> goodsInfos = new ArrayList<>();
		for (DeliverGoods deliverGoods : deliverGoodsList) {
			if (Objects.nonNull(deliverGoods.getGoodsInfo())) {
				List<GoodsInfo> goodsInfoList = this
						.convertGoodsInfo(deliverGoods.getGoodsInfo());
				for (GoodsInfo goodsInfo : goodsInfoList) {
					goodsInfo.setOrderId(deliverGoods.getOrderId());
					goodsInfo.setDeliverGoodsId(deliverGoods.getId());
				}
				goodsInfos.addAll(goodsInfoList);
			}
		}
		// 根据发货信息查询出所有的订单id
		List<String> orderIdList = deliverGoodsList.stream()
				.map(DeliverGoods::getOrderId).toList();
		List<OrderVo> orderVoList = orderService.findByOrderIds(orderIdList,
				deliverGoodsList, contract);
		List<SignReceiptVo> signReceipts = new ArrayList<>();
		for (SignReceipt e : list) {
			SignReceiptVo signReceiptVo = new SignReceiptVo();
			signReceiptVo.setSignReceipt(e);
			signReceiptVo.setProject(projectMap.get(e.getProjectId()));
			// 同一个合同下的签收单是同一个合同
			signReceiptVo.setContract(contract);

			// 该签收单关联的船运单
			List<TransportOrderShip> filteredTransportOrderShips = transportOrderShips
					.stream()
					.filter(transportOrderShip -> e.getRelatedDeliverGoodsIds()
							.contains(transportOrderShip.getGoodsId()))
					.toList();
			signReceiptVo.setShipList(filteredTransportOrderShips);
			// 该签收单关联的船运单
			List<TransportOrderVehicle> filteredTransportOrderVehicles = transportOrderVehicles
					.stream()
					.filter(transportOrderVehicle -> e
							.getRelatedDeliverGoodsIds().contains(
									transportOrderVehicle.getDeliverGoodsId()))
					.toList();
			signReceiptVo.setVehicleList(filteredTransportOrderVehicles);
			// 该签收单关联的船运单
			List<TransportOrderRailway> filteredTransportOrderRailways = transportOrderRailways
					.stream()
					.filter(transportOrderRailway -> e
							.getRelatedDeliverGoodsIds().contains(
									transportOrderRailway.getDeliverGoodsId()))
					.toList();
			signReceiptVo.setRailwayList(filteredTransportOrderRailways);
			// 该签收单关联的货物信息
			List<GoodsInfo> filteredGoodsInfos = goodsInfos.stream()
					.filter(goodsInfo -> e.getRelatedDeliverGoodsIds()
							.contains(goodsInfo.getDeliverGoodsId()))
					.toList();
			signReceiptVo.setGoodsInfoList(filteredGoodsInfos);
			// 该签收单关联的发货单
			List<DeliverGoods> filteredDeliverGoods = deliverGoodsList.stream()
					.filter(deliverGoods -> e.getRelatedDeliverGoodsIds()
							.contains(deliverGoods.getId()))
					.toList();
			signReceiptVo.setDeliverGoodsList(filteredDeliverGoods);
			List<String> orderIds = filteredDeliverGoods.stream()
					.map(DeliverGoods::getOrderId).toList();
			// 该签收单关联的订单
			List<OrderVo> filteredOrderVos = orderVoList.stream().filter(
					orderVo -> orderIds.contains(orderVo.getOrder().getId()))
					.toList();
			signReceiptVo.setOrderVoList(filteredOrderVos);
			signReceipts.add(signReceiptVo);
		}
		return signReceipts;
	}

	private List<GoodsInfo> convertGoodsInfo(String data) {
		Gson gson = new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();
		Type listType = new TypeToken<List<GoodsInfo>>() {
		}.getType();
		return gson.fromJson(data, listType);
	}

	/**
	 * @description: 更新订单中的签收单状态
	 * @author: 彭湃
	 * @date: 2025/1/16 16:41
	 * @param: [oldOrderIds,
	 *             newOrderIds]
	 * @return: void
	 **/
	private void changeOrderStatus(List<String> oldOrderIds,
			List<String> newOrderIds) {
		Map<String, Order> oldOrderMap = orderService.findByIds(oldOrderIds)
				.stream().collect(Collectors.toMap(Order::getId, e -> e));
		// 查找出所有orderId对应的发货单
		List<DeliverGoods> byOrderIds = deliverGoodsService
				.findByOrderIds(oldOrderIds);
		Map<String, List<DeliverGoods>> collect = byOrderIds.stream()
				.collect(Collectors.groupingBy(DeliverGoods::getOrderId));
		List<Order> list = new ArrayList<>();
		for (Map.Entry<String, List<DeliverGoods>> entry : collect.entrySet()) {
			List<DeliverGoods> value = entry.getValue();
			boolean flag = false;
			for (DeliverGoods deliverGoods : value) {
				if (Objects.nonNull(deliverGoods.getSignReceiptId())) {
					flag = true;
					break;
				}
			}
			Order order = oldOrderMap.get(entry.getKey());
			if (!flag) {
				order.setReceiveStatus(
						OrderDef.BusinessStatus.NOT_STARTED.getCode());
			} else {
				order.setReceiveStatus(
						OrderDef.BusinessStatus.IN_PROGRESS.getCode());
			}
			list.add(order);
		}
		if (CollectionUtils.isNotEmpty(list)) {
			orderService.batchUpdate(list);
		}
		if (CollectionUtils.isNotEmpty(newOrderIds)) {
			List<Order> orderList = orderService.findByIds(newOrderIds);
			orderList.forEach(e -> e.setReceiveStatus(
					OrderDef.BusinessStatus.IN_PROGRESS.getCode()));
			orderService.batchUpdate(orderList);
		}
	}

	/**
	 * @description: 签收单完成后判断订单中的签收单状态是否可以改为已完成状态
	 * @author: 彭湃
	 * @date: 2025/1/16 17:14
	 * @param: [orderIds]
	 * @return: void
	 **/
	@Override
	public void completedChangeOrderStatus(List<String> orderIds) {
		List<Order> orderList = orderService.findByIds(orderIds);
		Map<String, Order> orderMap = orderList.stream()
				.collect(Collectors.toMap(Order::getId, i -> i));
		List<DeliverGoods> deliverGoods = deliverGoodsService
				.findByOrderIds(orderIds);
		deliverGoods = deliverGoods.stream().filter(
				e -> !DeliverGoodsDef.Status.DELIVER_CANCEL.match(e.getStatus())
						&& !DeliverGoodsDef.Status.DELIVER_INVALID
								.match(e.getStatus()))
				.toList();
		Map<String, List<DeliverGoods>> collect = deliverGoods.stream()
				.collect(Collectors.groupingBy(DeliverGoods::getOrderId));
		List<Order> result = new ArrayList<>();
		for (Map.Entry<String, List<DeliverGoods>> entry : collect.entrySet()) {
			List<DeliverGoods> value = entry.getValue();
			boolean flag = true;
			for (DeliverGoods goods : value) {
				if (Objects.isNull(goods.getSignReceiptId())) {
					flag = false;
					break;
				}
			}
			if (!flag) {
				continue;
			}
			List<String> list = value.stream()
					.map(DeliverGoods::getSignReceiptId).distinct().toList();
			List<SignReceipt> signReceipts = this.findByIds(list);
			for (SignReceipt signReceipt : signReceipts) {
				if (!SignReceiptDef.Status.FINISHED.getCode()
						.equals(signReceipt.getStatus())) {
					flag = false;
					break;
				}
			}
			if (!flag) {
				continue;
			}

			Order order = orderMap.get(entry.getKey());
			// 只有订单的发货状态为已完成的时候才设置订单的签收状态为已完成
			if (OrderDef.BusinessStatus.COMPLETED
					.match(order.getDeliveryStatus())) {
				order.setReceiveStatus(
						OrderDef.BusinessStatus.COMPLETED.getCode());
				result.add(order);
			}
		}
		orderService.batchUpdate(result);
	}

	@Override
	public Optional<SignReceiptCountVo> staticsAdminSignReceipt(
			boolean isManage, boolean isSeal) {
		SignReceiptCountVo signReceiptCountVo = new SignReceiptCountVo();
		signReceiptCountVo.setWaitConfirm(0L);
		signReceiptCountVo.setToBeSigned(0L);
		signReceiptCountVo.setReject(0L);
		signReceiptCountVo.setInvaliding(0L);
		signReceiptCountVo.setPurchaseToBeSigned(0L);
		signReceiptCountVo.setPurchaseRejected(0L);
		signReceiptCountVo.setPurchaseInvaliding(0L);
		signReceiptCountVo.setSaleToBeConfirmed(0L);
		signReceiptCountVo.setSaleRejected(0L);
		signReceiptCountVo.setSaleInvaliding(0L);
		List<String> projectIds = projectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		LambdaQueryWrapper<SignReceipt> queryWrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计销售待确认
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper
						.and(x -> x
								.eq(SignReceipt::getStatus,
										SignReceiptDef.Status.CONFIRMING
												.getCode())
								.eq(SignReceipt::getInitiator,
										CommonDef.AccountSource.CUSTOM
												.getCode()));
				queryWrapper.in(SignReceipt::getProjectId, projectIds);
				queryWrapper.in(SignReceipt::getType,
						SignReceiptDef.Type.SELL.getCode());
				signReceiptCountVo.setSaleToBeConfirmed(
						repository.selectCount(queryWrapper));

				// 统计销售作废中
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(SignReceipt::getStatus,
						SignReceiptDef.Status.INVALIDING.getCode());
				queryWrapper.in(SignReceipt::getProjectId, projectIds);
				queryWrapper.in(SignReceipt::getType,
						SignReceiptDef.Type.SELL.getCode());
				signReceiptCountVo.setSaleInvaliding(
						repository.selectCount(queryWrapper));

				// 统计采购已驳回
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(SignReceipt::getStatus,
						SignReceiptDef.Status.REVERTED.getCode());
				queryWrapper.in(SignReceipt::getType,
						SignReceiptDef.Type.BUY.getCode());
				signReceiptCountVo.setPurchaseRejected(
						repository.selectCount(queryWrapper));

				// 统计采购作废中
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(SignReceipt::getStatus,
						SignReceiptDef.Status.INVALIDING.getCode());
				queryWrapper.in(SignReceipt::getProjectId, projectIds);
				queryWrapper.in(SignReceipt::getType,
						SignReceiptDef.Type.BUY.getCode());
				signReceiptCountVo.setPurchaseInvaliding(
						repository.selectCount(queryWrapper));
			}
		}

		if (isSeal) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计销售待签署
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(SignReceipt::getStatus,
						SignReceiptDef.Status.SIGNING.getCode());
				queryWrapper.and(x -> x
						.eq(SignReceipt::getSignType,
								BusinessContractDef.CommonSignState.UNSIGNED
										.getCode())
						.or().eq(SignReceipt::getSignType,
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode()));
				queryWrapper.in(SignReceipt::getProjectId, projectIds);
				queryWrapper.in(SignReceipt::getType,
						SignReceiptDef.Type.SELL.getCode());
				signReceiptCountVo
						.setSaleRejected(repository.selectCount(queryWrapper));

				// 统计采购待签署
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(SignReceipt::getStatus,
						SignReceiptDef.Status.SIGNING.getCode());
				queryWrapper.and(x -> x
						.eq(SignReceipt::getSignType,
								BusinessContractDef.CommonSignState.UNSIGNED
										.getCode())
						.or().eq(SignReceipt::getSignType,
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode()));
				queryWrapper.in(SignReceipt::getProjectId, projectIds);
				queryWrapper.in(SignReceipt::getType,
						SignReceiptDef.Type.BUY.getCode());
				signReceiptCountVo
						.setSaleRejected(repository.selectCount(queryWrapper));
			}
		}
		return Optional.of(signReceiptCountVo);
	}

	@Override
	public Optional<SignReceiptCountVo> staticsCustomerSignReceipt(
			boolean isSeal, boolean isPermission) {
		SignReceiptCountVo signReceiptCountVo = new SignReceiptCountVo();
		signReceiptCountVo.setWaitConfirm(0L);
		signReceiptCountVo.setToBeSigned(0L);
		signReceiptCountVo.setReject(0L);
		signReceiptCountVo.setInvaliding(0L);
		signReceiptCountVo.setPurchaseToBeSigned(0L);
		signReceiptCountVo.setPurchaseRejected(0L);
		signReceiptCountVo.setPurchaseInvaliding(0L);
		signReceiptCountVo.setSaleToBeConfirmed(0L);
		signReceiptCountVo.setSaleRejected(0L);
		signReceiptCountVo.setSaleInvaliding(0L);
		LambdaQueryWrapper<SignReceipt> queryWrapper = Wrappers
				.lambdaQuery(SignReceipt.class);
		Long customerId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();
		if (isPermission) {
			// 统计采购已驳回
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.REVERTED.getCode());
			queryWrapper.eq(SignReceipt::getPurchaserId, customerId);
			signReceiptCountVo
					.setPurchaseRejected(repository.selectCount(queryWrapper));

			// 统计采购作废中
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(SignReceipt::getPurchaserId, customerId);
			queryWrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.INVALIDING.getCode());
			signReceiptCountVo.setPurchaseInvaliding(
					repository.selectCount(queryWrapper));

			// 统计销售待确认
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.and(x -> x
					.eq(SignReceipt::getStatus,
							SignReceiptDef.Status.CONFIRMING.getCode())
					.eq(SignReceipt::getInitiator,
							CommonDef.AccountSource.CUSTOM.getCode()));
			queryWrapper.eq(SignReceipt::getSellerId, customerId);
			signReceiptCountVo
					.setSaleToBeConfirmed(repository.selectCount(queryWrapper));

			// 统计销售作废中
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(SignReceipt::getSellerId, customerId);
			queryWrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.INVALIDING.getCode());
			signReceiptCountVo
					.setSaleInvaliding(repository.selectCount(queryWrapper));

		}

		if (isSeal) {
			// 统计采购待签署
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.SIGNING.getCode());
			queryWrapper.and(x -> x
					.eq(SignReceipt::getSignStatus,
							BusinessContractDef.CommonSignState.UNSIGNED
									.getCode())
					.or().eq(SignReceipt::getSignStatus,
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode()));
			queryWrapper.eq(SignReceipt::getPurchaserId, customerId);
			signReceiptCountVo.setPurchaseToBeSigned(
					repository.selectCount(queryWrapper));

			// 统计销售待签署
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(SignReceipt::getStatus,
					SignReceiptDef.Status.SIGNING.getCode());
			queryWrapper.and(x -> x
					.eq(SignReceipt::getSignStatus,
							BusinessContractDef.CommonSignState.UNSIGNED
									.getCode())
					.or().eq(SignReceipt::getSignStatus,
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode()));
			queryWrapper.eq(SignReceipt::getSellerId, customerId);
			signReceiptCountVo
					.setSaleRejected(repository.selectCount(queryWrapper));
		}

		return Optional.of(signReceiptCountVo);
	}

	@Override
	public void downloadPdf(HttpServletResponse response, String id)
			throws IOException {
		setExportResponseFields(response, id);
		GeneratPdfDto pdf = generatPdfDto(id);
		PdfUtils.getPdf(TransactionDef.PdfType.SIGN_IN,
				response.getOutputStream(), pdf);
	}

	/**
	 * 设置导出响应头
	 *
	 * @param response
	 */
	private void setExportResponseFields(HttpServletResponse response,
			String id) {
		response.setContentType("application/pdf");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode(
				String.format("签收单_%s_%s",
						DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
								.format(LocalDateTime.now()),
						id),
				StandardCharsets.UTF_8).replaceAll("\\+", "%20");
		response.setHeader("Content-disposition",
				"attachment;filename*=utf-8''" + fileName + ".pdf");
	}

	/**
	 * 生成pdf转化类方法
	 *
	 * @param id
	 * @return
	 */
	private GeneratPdfDto generatPdfDto(String id) {
		SignReceipt signReceipt = this.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30097001));
		Contract contract = contractService.findOne(signReceipt.getContractId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30151001));
		List<DeliverGoods> byOrderIds = deliverGoodsService
				.findByIds(signReceipt.getRelatedDeliverGoodsIds());

		List<String> deliverGoodsIds = byOrderIds.stream()
				.map(DeliverGoods::getId).toList();
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		List<Order> orderList = orderService.findByIds(
				byOrderIds.stream().map(DeliverGoods::getOrderId).toList());
		Map<String, Order> collect = orderList.stream()
				.collect(Collectors.toMap(Order::getId, e -> e));
		Map<String, List<TransportOrderShip>> shipMap = transportOrderShips
				.stream()
				.collect(Collectors.groupingBy(TransportOrderShip::getGoodsId));
		Map<String, List<TransportOrderVehicle>> vehiclesMap = transportOrderVehicles
				.stream().collect(Collectors
						.groupingBy(TransportOrderVehicle::getDeliverGoodsId));
		Map<String, List<TransportOrderRailway>> railwayMap = transportOrderRailways
				.stream().collect(Collectors
						.groupingBy(TransportOrderRailway::getDeliverGoodsId));
		List<TransportOrderShip> ships = new ArrayList<>();
		List<TransportOrderVehicle> vehicles = new ArrayList<>();
		List<TransportOrderRailway> railways = new ArrayList<>();
		List<String> list = byOrderIds.stream().map(DeliverGoods::getOrderId)
				.distinct().toList();
		Map<String, Order> orderMap = new HashMap<>();
		String orderIds = String.join("、", list);
		String goodsInfoList = null;
		for (DeliverGoods byOrderId : byOrderIds) {
			DeliverGoodsDef.DeliverWay from = DeliverGoodsDef.DeliverWay
					.from(byOrderId.getDelivery());
			orderMap.put(byOrderId.getId(),
					collect.get(byOrderId.getOrderId()));
			switch (from) {
				case SELF_PICKUP -> {
					List<GoodsInfo> goodsInfos = this
							.convertGoodsInfo(byOrderId.getGoodsInfo());
					for (GoodsInfo goodsInfo : goodsInfos) {
						goodsInfo.setOrderId(byOrderId.getOrderId());
						goodsInfo.setDeliverGoodsId(byOrderId.getId());
					}
					goodsInfoList = JSON.toJSONString(goodsInfos);
				}
				case SHIPPING -> ships.addAll(shipMap.get(byOrderId.getId()));
				case CAR -> vehicles.addAll(vehiclesMap.get(byOrderId.getId()));
				case TRAIN ->
					railways.addAll(railwayMap.get(byOrderId.getId()));
			}

		}
		GeneratPdfDto pdf = new GeneratPdfDto();
		pdf.setDeliverGoodsList(byOrderIds);
		pdf.setOrderMap(orderMap);
		pdf.setBillId(id);
		pdf.setShipList(ships);
		pdf.setVehicleList(vehicles);
		pdf.setRailwayList(railways);
		pdf.setOrderId(orderIds);
		if (ContractDef.ContractType.PURCHASE
				.match(contract.getContractType())) {
			pdf.setBuyerName(contract.getSupplierChainEnterprise().getName());
			pdf.setSellerName(
					contract.getUpstreamSuppliersEnterprise().getName());
		} else {
			pdf.setBuyerName(
					contract.getDownstreamPurchasersEnterprise().getName());
			pdf.setSellerName(contract.getSupplierChainEnterprise().getName());
		}
		pdf.setContractName(contract.getName());
		DateTimeFormatter formatter = DateTimeFormatter
				.ofPattern("【yyyy】年【MM】月【dd】日");
		pdf.setContractSignDate(
				formatter.format(signReceipt.getSignConfirmDate()));
		pdf.setContractId(contract.getId());
		pdf.setGoodsInfoList(goodsInfoList);
		String orderId = byOrderIds.get(0).getOrderId();
		Order order = orderService.findOne(orderId).orElse(new Order());
		if (Objects.nonNull(pdf.getGoodsInfoList())) {
			for (Object o : JSON.parseArray(pdf.getGoodsInfoList())) {
				JSONObject object = (JSONObject) o;
				object.put("goodsName", order.getGoodsName());
				object.put("unit", order.getUnit());
			}
		}
		return pdf;
	}

	/**
	 * @description: 获取文件id，生成pdf文件并上传文件服务器，返回文件id
	 * @author: 彭湃
	 * @date: 2025/1/23 15:26
	 * @param: [resource]
	 * @return: java.lang.Long
	 **/
	private Long getFileId(SignReceipt signReceipt) {
		// 生成pdf
		OutputStream outputStream = new ByteArrayOutputStream();
		GeneratPdfDto pdf = generatPdfDto(signReceipt.getId());
		PdfUtils.getPdf(TransactionDef.PdfType.SIGN_IN, outputStream, pdf);
		try {
			CustomMultipartFile convert = MultipartFileUtils.convert(
					outputStream, "签收单合同" + signReceipt.getId(),
					"签收单合同" + signReceipt.getId() + ".pdf", "text/plain");
			File file = fileService
					.upload(convert, "签收单合同" + signReceipt.getId() + ".pdf")
					.orElse(null);
			if (Objects.nonNull(file)) {
				return file.getId();
			}
		} catch (Exception e) {
			log.error("生成pdf文件失败", e);
		}
		return null;
	}

	// 更根据签收单，发货单信息，设置签收单里面的 发货信息，订单信息
	private void packSignReceiptVo(SignReceipt e, SignReceiptVo signReceiptVo,
			Contract contract, List<DeliverGoods> deliverGoodsList) {
		// 根据发货信息查询出所有的订单id
		List<String> orderIdList = deliverGoodsList.stream()
				.map(DeliverGoods::getOrderId).collect(Collectors
						.collectingAndThen(Collectors.toSet(), Set::stream))
				.toList();
		signReceiptVo.setDeliverGoodsList(deliverGoodsList);
		if (!CollectionUtils.isEmpty(orderIdList)) {
			signReceiptVo.setOrderList(orderService.findByIds(orderIdList));
		}
		// 采购里面的签收关联订单 设置订单信息
		if (ContractDef.Type.BUY.match(contract.getContractType())) {
			if (CollectionUtils.isNotEmpty(e.getRelatedOrderIds())) {
				signReceiptVo.setOrderList(
						orderService.findByIds(e.getRelatedOrderIds()));
			}
		}
	}

	private void handleStatus(LambdaQueryWrapper<SignReceipt> wrapper,
			List<Integer> status, boolean j, boolean k, Integer initiator,
			Integer initiator1) {
		if (CollectionUtils.isNotEmpty(status)) {
			wrapper.and(h -> h.in(SignReceipt::getStatus, status).or(i -> {
				if (!j && !k) {
					i.eq(SignReceipt::getStatus,
							SignReceiptDef.Status.SIGNING.getCode());
				} else if (j && k) {
					i.in(SignReceipt::getStatus,
							SignReceiptDef.Status.CONFIRMING.getCode(),
							SignReceiptDef.Status.SIGNING.getCode());
				} else if (j) {
					i.and(x -> x
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.SIGNING.getCode())
							.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator, initiator)));
				} else {
					i.and(x -> x
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.SIGNING.getCode())
							.or(y -> y
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.CONFIRMING
													.getCode())
									.eq(SignReceipt::getInitiator,
											initiator1)));
				}
			}));
		} else {
			if (!j && !k) {
				wrapper.eq(SignReceipt::getStatus,
						SignReceiptDef.Status.SIGNING.getCode());
			} else if (j && k) {
				wrapper.in(SignReceipt::getStatus,
						SignReceiptDef.Status.CONFIRMING.getCode(),
						SignReceiptDef.Status.SIGNING.getCode());
			} else if (j) {
				wrapper.and(x -> x
						.eq(SignReceipt::getStatus,
								SignReceiptDef.Status.SIGNING.getCode())
						.or(y -> y
								.eq(SignReceipt::getStatus,
										SignReceiptDef.Status.CONFIRMING
												.getCode())
								.eq(SignReceipt::getInitiator, initiator)));
			} else {
				wrapper.and(x -> x
						.eq(SignReceipt::getStatus,
								SignReceiptDef.Status.SIGNING.getCode())
						.or(y -> y
								.eq(SignReceipt::getStatus,
										SignReceiptDef.Status.CONFIRMING
												.getCode())
								.eq(SignReceipt::getInitiator, initiator1)));
			}
		}
	}

	private void handleStatus1(LambdaQueryWrapper<SignReceipt> wrapper,
			List<Integer> status, boolean j, boolean k, Integer initiator,
			Integer initiator1) {
		if (CollectionUtils.isNotEmpty(status)) {
			wrapper.and(x -> x.in(SignReceipt::getStatus, status).or(y -> {
				if (!j && !k) {
					y.in(SignReceipt::getStatus, status);
				} else if (j && k) {
					y.eq(SignReceipt::getStatus,
							SignReceiptDef.Status.CONFIRMING.getCode());
				} else if (j) {
					y.and(q -> q
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.CONFIRMING.getCode())
							.eq(SignReceipt::getInitiator, initiator));
				} else {
					y.and(q -> q
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.CONFIRMING.getCode())
							.eq(SignReceipt::getInitiator, initiator1));
				}
			}));
		} else {
			if (!j && !k) {
				wrapper.in(SignReceipt::getStatus, status);
			} else if (j && k) {
				wrapper.eq(SignReceipt::getStatus,
						SignReceiptDef.Status.CONFIRMING.getCode());
			} else if (j) {
				wrapper.and(x -> x
						.eq(SignReceipt::getStatus,
								SignReceiptDef.Status.CONFIRMING.getCode())
						.eq(SignReceipt::getInitiator, initiator));
			} else {
				wrapper.and(x -> x
						.eq(SignReceipt::getStatus,
								SignReceiptDef.Status.CONFIRMING.getCode())
						.eq(SignReceipt::getInitiator, initiator1));
			}
		}
	}

	private void handleToBeSignedStatus(LambdaQueryWrapper<SignReceipt> wrapper,
			List<Integer> status, boolean j, boolean k,
			List<Integer> toBeSignedList, Integer initiator,
			Integer initiator1) {
		if (!j && !k) {
			wrapper.and(x -> x
					.in(CollectionUtils.isNotEmpty(status),
							SignReceipt::getStatus, status)
					.or(y -> y.in(SignReceipt::getSignStatus, toBeSignedList)
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.SIGNING.getCode())));
		} else if (j && k) {
			status.add(SignReceiptDef.Status.CONFIRMING.getCode());
			wrapper.and(x -> x
					.in(CollectionUtils.isNotEmpty(status),
							SignReceipt::getStatus, status)
					.or(y -> y.in(SignReceipt::getSignStatus, toBeSignedList)
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.SIGNING.getCode())));
		} else if (j) {
			wrapper.and(x -> x
					.in(CollectionUtils.isNotEmpty(status),
							SignReceipt::getStatus, status)
					.or(y -> y.in(SignReceipt::getSignStatus, toBeSignedList)
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.SIGNING.getCode()))
					.or(y -> y
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.CONFIRMING.getCode())
							.eq(SignReceipt::getInitiator, initiator)));
		} else {
			wrapper.and(x -> x
					.in(CollectionUtils.isNotEmpty(status),
							SignReceipt::getStatus, status)
					.or(y -> y.in(SignReceipt::getSignStatus, toBeSignedList)
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.SIGNING.getCode()))
					.or(y -> y
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.CONFIRMING.getCode())
							.eq(SignReceipt::getInitiator, initiator1)));
		}
	}

	private void handleSigningStatus(LambdaQueryWrapper<SignReceipt> wrapper,
			List<Integer> status, boolean j, boolean k, Integer initiator,
			Integer initiator1) {
		if (!j && !k) {
			wrapper.and(x -> x.in(CollectionUtils.isNotEmpty(status),
					SignReceipt::getStatus, status).or(y -> {
						if (CommonDef.AccountSource.INNER.match(initiator1)) {
							y.eq(SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode())
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.SIGNING
													.getCode());
						} else {
							y.eq(SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode())
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.SIGNING
													.getCode());
						}
					}));
		} else if (j && k) {
			status.add(SignReceiptDef.Status.CONFIRMING.getCode());
			wrapper.and(x -> x.in(CollectionUtils.isNotEmpty(status),
					SignReceipt::getStatus, status).or(y -> {
						if (CommonDef.AccountSource.INNER.match(initiator1)) {
							y.eq(SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode())
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.SIGNING
													.getCode());
						} else {
							y.eq(SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode())
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.SIGNING
													.getCode());
						}
					}));
		} else if (j) {
			wrapper.and(x -> x.in(CollectionUtils.isNotEmpty(status),
					SignReceipt::getStatus, status).or(y -> {
						if (CommonDef.AccountSource.INNER.match(initiator1)) {
							y.eq(SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode())
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.SIGNING
													.getCode());
						} else {
							y.eq(SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode())
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.SIGNING
													.getCode());
						}
					})
					.or(y -> y
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.CONFIRMING.getCode())
							.eq(SignReceipt::getInitiator, initiator)));
		} else {
			wrapper.and(x -> x.in(CollectionUtils.isNotEmpty(status),
					SignReceipt::getStatus, status).or(y -> {
						if (CommonDef.AccountSource.INNER.match(initiator1)) {
							y.eq(SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode())
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.SIGNING
													.getCode());
						} else {
							y.eq(SignReceipt::getSignStatus,
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode())
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.SIGNING
													.getCode());
						}
					})
					.or(y -> y
							.eq(SignReceipt::getStatus,
									SignReceiptDef.Status.CONFIRMING.getCode())
							.eq(SignReceipt::getInitiator, initiator1)));
		}
	}

	private void addConditions(LambdaQueryWrapper<SignReceipt> wrapper,
			List<Integer> status, List<Integer> copiedList,
			List<Integer> toBeSignedList, Integer innerCode,
			Integer customCode) {
		wrapper.and(x -> x.eq(SignReceipt::getInitiator, innerCode)
				.in(SignReceipt::getStatus, status).or()
				.or(CollectionUtils.isNotEmpty(copiedList),
						y -> y.eq(SignReceipt::getInitiator, customCode)
								.in(CollectionUtils.isNotEmpty(copiedList),
										SignReceipt::getStatus, copiedList))
				.or()
				.or(CollectionUtils.isNotEmpty(toBeSignedList), y -> y
						.in(SignReceipt::getSignStatus, toBeSignedList)
						.eq(SignReceipt::getStatus,
								SignReceiptDef.Status.SIGNING.getCode())));
	}

	private void addCommonConditions(LambdaQueryWrapper<SignReceipt> wrapper,
			List<Integer> status, List<Integer> copiedList, Integer initiator,
			Integer initiator1) {
		copiedList.removeIf(SignReceiptDef.Status.SIGNING::match);
		wrapper.and(x -> x.eq(SignReceipt::getInitiator, initiator)
				.in(SignReceipt::getStatus, status).or()
				.or(CollectionUtils.isNotEmpty(copiedList),
						y -> y.eq(SignReceipt::getInitiator, initiator1).in(
								CollectionUtils.isNotEmpty(copiedList),
								SignReceipt::getStatus, copiedList))
				.or().or(y -> {
					if (CommonDef.AccountSource.INNER.match(initiator1)) {
						y.eq(SignReceipt::getSignStatus,
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode())
								.eq(SignReceipt::getStatus,
										SignReceiptDef.Status.SIGNING
												.getCode());
					} else {
						y.eq(SignReceipt::getSignStatus,
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode())
								.eq(SignReceipt::getStatus,
										SignReceiptDef.Status.SIGNING
												.getCode());
					}
				}));
	}

	private void addCommonConditions1(LambdaQueryWrapper<SignReceipt> wrapper,
			List<Integer> status, List<Integer> copiedList, Integer initiator,
			Integer initiator1) {
		wrapper.and(x -> x.eq(SignReceipt::getInitiator, initiator)
				.in(SignReceipt::getStatus, status).or()
				.or(CollectionUtils.isNotEmpty(copiedList),
						y -> y.eq(SignReceipt::getInitiator, initiator1).in(
								CollectionUtils.isNotEmpty(copiedList),
								SignReceipt::getStatus, copiedList)));
	}

	/*
	 * @description: 新增修改采购签收的时候关联发货单和订单处理
	 *
	 * @author: pp
	 *
	 * @date: 2025/4/22 15:41
	 *
	 * @param: [signReceipt, deliverGoodsList]
	 *
	 * @return: com.zhihaoscm.domain.bean.entity.SignReceipt
	 **/
	private SignReceipt relate(SignReceipt signReceipt,
			List<DeliverGoods> deliverGoodsList) {
		List<String> deliverGoodsIds = deliverGoodsList.stream()
				.map(DeliverGoods::getId).distinct().toList();
		Map<String, List<TransportOrderShip>> shipMap = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds).stream()
				.collect(Collectors.groupingBy(TransportOrderShip::getGoodsId));
		Map<String, List<TransportOrderVehicle>> vehicleMap = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds).stream()
				.collect(Collectors
						.groupingBy(TransportOrderVehicle::getDeliverGoodsId));
		Map<String, List<TransportOrderRailway>> railwayMap = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds).stream()
				.collect(Collectors
						.groupingBy(TransportOrderRailway::getDeliverGoodsId));
		Boolean aBoolean = contractService
				.validateIsRecorded(signReceipt.getContractId(),
						ContractDef.ContractType.SALES.getCode())
				.orElse(null);
		ArrayString goodsIds = new ArrayString();
		Map<String, BigDecimal> orderWeightMap = new HashMap<>();
		BigDecimal totalWeight;
		List<String> orderIds = deliverGoodsList.stream()
				.map(DeliverGoods::getOrderId).distinct().toList();
		for (DeliverGoods deliverGoods : deliverGoodsList) {
			totalWeight = BigDecimal.ZERO;
			if (!orderWeightMap.containsKey(deliverGoods.getOrderId())) {
				orderWeightMap.put(deliverGoods.getOrderId(), BigDecimal.ZERO);
			} else {
				totalWeight = orderWeightMap.get(deliverGoods.getOrderId());
			}
			goodsIds.add(deliverGoods.getId());
			if (DeliverGoodsDef.ReceiveWay.SEPARATE
					.match(deliverGoods.getReceiveWay())) {
				if (Boolean.TRUE.equals(aBoolean)) {
					// 录入企业
					List<GoodsInfo> list = this
							.convertGoodsInfo(deliverGoods.getGoodsInfo());
					for (GoodsInfo goodsInfo : list) {
						if (Objects.nonNull(goodsInfo.getReceiptQuantity())) {
							totalWeight = totalWeight
									.add(goodsInfo.getReceiptQuantity());
							deliverGoods.setReceiptWeight(Objects
									.nonNull(deliverGoods.getReceiptWeight())
											? deliverGoods.getReceiptWeight()
													.add(goodsInfo
															.getReceiptQuantity())
											: BigDecimal.ZERO.add(goodsInfo
													.getReceiptQuantity()));
						}
					}
				} else {
					// 注册企业
					switch (DeliverGoodsDef.DeliverWay
							.from(deliverGoods.getDelivery())) {
						case SELF_PICKUP -> {
							List<GoodsInfo> list = this.convertGoodsInfo(
									deliverGoods.getGoodsInfo());
							for (GoodsInfo goodsInfo : list) {
								if (Objects.nonNull(
										goodsInfo.getReceiptQuantity())) {
									totalWeight = totalWeight.add(
											goodsInfo.getReceiptQuantity());
									deliverGoods.setReceiptWeight(
											Objects.nonNull(deliverGoods
													.getReceiptWeight())
															? deliverGoods
																	.getReceiptWeight()
																	.add(goodsInfo
																			.getReceiptQuantity())
															: BigDecimal.ZERO
																	.add(goodsInfo
																			.getReceiptQuantity()));
								}
							}
						}
						case TRAIN -> {
							List<TransportOrderRailway> transportOrderRailways = railwayMap
									.get(deliverGoods.getId());
							if (CollectionUtils
									.isNotEmpty(transportOrderRailways)) {
								for (TransportOrderRailway goodsInfo : transportOrderRailways) {
									if (Objects.nonNull(
											goodsInfo.getReceiptQuantity())) {
										totalWeight = totalWeight.add(
												goodsInfo.getReceiptQuantity());
										deliverGoods.setReceiptWeight(
												Objects.nonNull(deliverGoods
														.getReceiptWeight())
																? deliverGoods
																		.getReceiptWeight()
																		.add(goodsInfo
																				.getReceiptQuantity())
																: BigDecimal.ZERO
																		.add(goodsInfo
																				.getReceiptQuantity()));
									}
								}
							}
						}
						case CAR -> {
							List<TransportOrderVehicle> transportOrderVehicles = vehicleMap
									.get(deliverGoods.getId());
							if (CollectionUtils
									.isNotEmpty(transportOrderVehicles)) {
								for (TransportOrderVehicle goodsInfo : transportOrderVehicles) {
									if (Objects.nonNull(
											goodsInfo.getReceiptQuantity())) {
										totalWeight = totalWeight.add(
												goodsInfo.getReceiptQuantity());
										deliverGoods.setReceiptWeight(
												Objects.nonNull(deliverGoods
														.getReceiptWeight())
																? deliverGoods
																		.getReceiptWeight()
																		.add(goodsInfo
																				.getReceiptQuantity())
																: BigDecimal.ZERO
																		.add(goodsInfo
																				.getReceiptQuantity()));
									}
								}
							}
						}
						case SHIPPING -> {
							List<TransportOrderShip> transportOrderShips = shipMap
									.get(deliverGoods.getId());
							if (CollectionUtils
									.isNotEmpty(transportOrderShips)) {
								for (TransportOrderShip goodsInfo : transportOrderShips) {
									if (Objects.nonNull(
											goodsInfo.getReceiptQuantity())) {
										totalWeight = totalWeight.add(
												goodsInfo.getReceiptQuantity());
										deliverGoods.setReceiptWeight(
												Objects.nonNull(deliverGoods
														.getReceiptWeight())
																? deliverGoods
																		.getReceiptWeight()
																		.add(goodsInfo
																				.getReceiptQuantity())
																: BigDecimal.ZERO
																		.add(goodsInfo
																				.getReceiptQuantity()));
									}
								}
							}
						}
					}
				}
			} else {
				// 签收方式为一次签收，直接取发货单重量
				totalWeight = totalWeight.add(deliverGoods.getReceiptWeight());
			}
			orderWeightMap.put(deliverGoods.getOrderId(), totalWeight);
			deliverGoods.setSignReceiptId(signReceipt.getId());
		}
		signReceipt.setRelatedOrderIds(new ArrayString(orderIds));
		signReceipt.setRelatedDeliverGoodsIds(goodsIds);
		// 更新发货单信息
		deliverGoodsService.batchUpdate(deliverGoodsList);
		// 更新订单信息
		List<Order> orderList = orderService.findByIds(orderIds);
		orderList.forEach(
				e -> e.setReceivedWeight(orderWeightMap.get(e.getId())));
		orderService.batchUpdate(orderList);
		return signReceipt;
	}

	/**
	 * 发送短信
	 *
	 * @param signReceipt
	 * @param templateCode
	 * @param title
	 */
	private void sendNotice(SignReceipt signReceipt, String templateCode,
			String title, Integer type) {
		Customer customer = null;
		if (ReconciliationDef.Type.SELL.match(type)) {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(signReceipt.getPurchaserInputId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		} else {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(signReceipt.getSellerInputId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		}

		if (Objects.nonNull(customer)) {
			if (StringUtils.isNotBlank(templateCode)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(templateCode)
						.params(Map.of("order_id", signReceipt.getId()))
						.mobile(customer.getMobile()).build());
			}

			if (StringUtils.isNotBlank(title)) {
				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.ORDER.getCode())
						.title(title)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.SIGN_RECEIPT_DETAIL_PAGE)
						.detailId(String.valueOf(signReceipt.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
		}
	}

}
