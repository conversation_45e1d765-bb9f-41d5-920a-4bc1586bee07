package com.zhihaoscm.service.resource.custom.order;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.ContractPageResponse;
import com.zhihaoscm.domain.bean.entity.Order;
import com.zhihaoscm.domain.bean.vo.OrderVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.CertificationDef;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.OrderService;
import com.zhihaoscm.service.resource.form.order.OrderCreateForm;
import com.zhihaoscm.service.resource.form.order.OrderUpdateForm;
import com.zhihaoscm.service.resource.validator.customer.enterprise.CustomerEnterpriseValidator;
import com.zhihaoscm.service.resource.validator.order.OrderValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: 订单管理
 * @author: 彭湃
 * @date: 2025/1/17 16:11
 **/
@Tag(name = "订单管理", description = "订单管理API")
@Slf4j
@RestController
@RequestMapping("/order")
public class OrderResource {

	@Autowired
	private OrderService orderService;

	@Autowired
	private OrderValidator orderValidator;

	@Autowired
	private CustomerEnterpriseValidator customerEnterpriseValidator;

	/**
	 * 分页查询，客户端端查询 projectType=2查询采购订单数据，projectType=1查销售订单数据
	 **/
	@Operation(summary = "分页查询订单列表")
	@GetMapping("/paging")
	public ApiResponse<Page<OrderVo>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "签收编号或者项目名称") @RequestParam(required = false) String key,
			@Parameter(description = "类型") @RequestParam(required = false) Integer type,
			@Parameter(description = "项目类型，采购或者销售") @RequestParam Integer projectType,
			@Parameter(description = "货物信息") @RequestParam(required = false) String goodsName,
			@Parameter(description = "发货状态") @RequestParam(required = false) Integer deliveryStatus,
			@Parameter(description = "签收状态") @RequestParam(required = false) List<Integer> receiveStatus,
			@Parameter(description = "对账状态") @RequestParam(required = false) List<Integer> reconciliationStatus,
			@Parameter(description = "发票状态") @RequestParam(required = false) List<Integer> invoiceStatus,
			@Parameter(description = "发货开始日期") @RequestParam(required = false) LocalDateTime startTime,
			@Parameter(description = "发货结束日期") @RequestParam(required = false) LocalDateTime endTime,
			@Parameter(description = "状态") @RequestParam(required = false) List<Integer> state,
			@Parameter(description = "项目id") @RequestParam(required = false) String projectId,
			@Parameter(description = "排序key") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序") @RequestParam(required = false) String sortOrder,
			@Parameter(description = "采购方名称") @RequestParam(required = false) String purchaserName) {
		return new ApiResponse<>(PageUtil.convert(orderService.customPaging(
				page, size, key, type, projectType, goodsName, deliveryStatus,
				receiveStatus, reconciliationStatus, invoiceStatus, startTime,
				endTime, state,
				CustomerContextHolder.getCustomerLoginVo().getProxyAccount()
						.getId(),
				sortKey, sortOrder, projectId, purchaserName)));
	}

	/**
	 * <AUTHOR>
	 */
	@Operation(summary = "付款管理-关联订单下拉列表")
	@GetMapping("/selector-associated")
	public ApiResponse<Page<Order>> selectorAssociated(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "项目id") @RequestParam(required = false) String projectId,
			@Parameter(description = "合同编号") @RequestParam String contractId,
			@Parameter(description = "订单编号") @RequestParam(required = false) String orderId) {
		return new ApiResponse<>(
				PageUtil.convert(orderService.selectorAssociated(page, size,
						projectId, contractId, orderId)));
	}

	@Operation(summary = "根据id查询详细信息")
	@GetMapping("/vo/{id}")
	public ApiResponse<OrderVo> findVoById(@PathVariable String id) {
		return new ApiResponse<>(orderService.findVoById(id));
	}

	/**
	 * @author: 许晶
	 * @param contractId
	 * @param reconciliationStatus
	 * @return
	 */
	@Operation(summary = "合同内全部已完成签收的订单")
	@GetMapping("/selector")
	public ApiResponse<List<OrderVo>> selector(
			@Parameter(description = "对账单id") @RequestParam(required = false) String reconciliationId,
			@Parameter(description = "合同id") @RequestParam String contractId,
			@Parameter(description = "签收状态") @RequestParam(required = false) List<Integer> receiveStatus,
			@Parameter(description = "对账状态") @RequestParam(required = false) List<Integer> reconciliationStatus,
			@Parameter(description = "订单id") @RequestParam(required = false) List<String> orderIds) {
		return new ApiResponse<>(orderService.findByContractIdAndStatus(
				reconciliationId, contractId, receiveStatus,
				reconciliationStatus, orderIds));
	}

	@Operation(summary = "查询合同内全部有未签收完的订单")
	@GetMapping("/find/UnReceipt/orders/{contractId}")
	public ApiResponse<List<OrderVo>> findUnReceiptOrders(
			@PathVariable(value = "contractId") String contractId) {
		return new ApiResponse<>(orderService.findUnReceiptOrders(contractId));
	}

	@Operation(summary = "查询合同内全部有未发货完的订单")
	@GetMapping("/find/unDeliver/orders/{contractId}")
	public ApiResponse<List<OrderVo>> findUnDeliverOrders(
			@PathVariable(value = "contractId") String contractId,
			@Parameter(description = "发货单Id") @RequestParam(required = false) String deliverGoodsId) {
		return new ApiResponse<>(
				orderService.findUnDeliverOrders(contractId, deliverGoodsId));
	}

	@Operation(summary = "修改签收单时查询合同内全部有未签收完的订单")
	@GetMapping("/find/{signReceiptId}")
	public ApiResponse<List<OrderVo>> find(
			@PathVariable(value = "signReceiptId") String signReceiptId,
			@Parameter(description = "发货单id") @RequestParam(required = false) List<String> deliverGoodsIds) {
		return new ApiResponse<>(
				orderService.find(signReceiptId, deliverGoodsIds));
	}

	@Operation(summary = "查询项目下的订单总重量")
	@GetMapping("/find/total-quantity/{projectId}")
	public ApiResponse<BigDecimal> findTotalQuantity(
			@PathVariable("projectId") String projectId,
			@Parameter(description = "状态") @RequestParam(required = false) Integer state,
			@Parameter(description = "开始日期") @RequestParam(required = false) LocalDateTime startTime,
			@Parameter(description = "结束日期") @RequestParam(required = false) LocalDateTime endTime,
			@Parameter(description = "合同类型（采购或者销售）") @RequestParam Integer type,
			@Parameter(description = "订单编号或合同名称") @RequestParam(required = false) String param,
			@Parameter(description = "采购方名称（销售类型）/销售方名称（采购类型)") @RequestParam(required = false) String purchaserName,
			@Parameter(description = "类型") @RequestParam(required = false) Integer orderType,
			@Parameter(description = "状态") @RequestParam(required = false) List<Integer> states) {
		return new ApiResponse<>(orderService
				.findTotalQuantity(projectId, state, startTime, endTime, type,
						param, purchaserName, orderType, states)
				.orElse(BigDecimal.ZERO));
	}

	@Operation(summary = "根据项目id获取下游预估可提货余额")
	@GetMapping("/estimated-amount/{projectId}")
	public ApiResponse<BigDecimal> findCustomerEstimatedAmount(
			@PathVariable String projectId) {
		return new ApiResponse<>(
				orderService.findCustomerEstimatedGoodsAmount(projectId)
						.orElse(BigDecimal.ZERO));
	}

	@Operation(summary = "根据项目id获取上游预估可提货余额")
	@GetMapping("/buy/estimated-amount/{projectId}")
	public ApiResponse<BigDecimal> findSupplierEstimatedGoodsAmount(
			@PathVariable String projectId) {
		return new ApiResponse<>(
				orderService.findSupplierEstimatedGoodsAmount(projectId)
						.orElse(BigDecimal.ZERO));
	}

	/**
	 * 客户端-新增采购订单
	 *
	 * @param form
	 *            表格
	 * @return {@link ApiResponse }<{@link Order }>
	 * <AUTHOR> create time: 2025/07/30
	 **/
	@Operation(summary = "新增订单")
	@PostMapping
	public ApiResponse<Order> create(
			@Validated @RequestBody OrderCreateForm form) {
		orderValidator.validateCreate(form);
		return new ApiResponse<>(orderService
				.customCreateBuy(form.convertEntity(), form.getSaveType(),
						CommonDef.AccountSource.CUSTOM.getCode())
				.orElseThrow());
	}

	@Operation(summary = "完成发货操作")
	@PutMapping("/complete/delivery/{id}")
	@Secured({ AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<Order> completeDelivery(@PathVariable("id") String id) {
		return new ApiResponse<>(orderService
				.completeDelivery(orderValidator.validateCompleteDelivery(id))
				.orElseThrow());
	}

	@Operation(summary = "修改订单")
	@PutMapping("/{id}")
	public ApiResponse<Order> update(
			@Validated @RequestBody OrderUpdateForm form,
			@PathVariable("id") String id) {
		Order order = orderValidator.validateUpdate(id, form);

		return new ApiResponse<>(orderService
				.customUpdateBuy(form.convertEntity(order), form.getSaveType())
				.orElseThrow());
	}

	@Operation(summary = "删除订单")
	@DeleteMapping("/{id}")
	public ApiResponse<Void> delete(@PathVariable("id") String id) {
		orderValidator.validateDelete(id);
		orderService.delete(id);
		return new ApiResponse<>();
	}

	@Operation(summary = "确认订单")
	@PutMapping("/confirm/{id}")
	public ApiResponse<Order> confirm(@PathVariable("id") String id) {
		return new ApiResponse<>(orderService
				.confirm(orderValidator.validateExist(id),
						CommonDef.AccountSource.CUSTOM.getCode())
				.orElseThrow());
	}

	@Operation(summary = "驳回订单")
	@PutMapping("/revert/{id}")
	public ApiResponse<Order> revert(@PathVariable("id") String id,
			@Parameter(description = "驳回原因") @RequestParam(required = false) String reason) {
		Order order = orderValidator.validateExist(id);
		order.setRevokeReason(reason);
		return new ApiResponse<>(orderService
				.revert(orderValidator.validateExist(id), Boolean.TRUE,
						CommonDef.AccountSource.CUSTOM.getCode())
				.orElseThrow());
	}

	@Operation(summary = "提交订单")
	@PutMapping("/submit/{id}")
	public ApiResponse<Order> submit(@PathVariable("id") String id) {
		return new ApiResponse<>(orderService
				.submit(orderValidator.validateExist(id)).orElseThrow());
	}

	@Operation(summary = "发起签署")
	@PutMapping("/initiate-sign/{id}")
	public ApiResponse<ContractPageResponse> initiateSign(
			@PathVariable(value = "id") String id) {
		customerEnterpriseValidator.validateSign(CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		Order order = orderValidator.validateInitiateSign(id,
				CommonDef.AccountSource.CUSTOM.getCode());
		return new ApiResponse<>(orderService
				.initiateSign(order, CertificationDef.Origin.PC.getCode())
				.orElse(null));
	}

    @Operation(summary = "订单是否展示完成发货按钮的查询接口")
    @GetMapping("/check-order-delivery-status/{orderId}")
    public ApiResponse<Boolean> checkOrderDeliveryStatus(
            @PathVariable(value = "orderId") String orderId) {
        return new ApiResponse<>(
                orderService.checkOrderDeliveryStatus(orderId).orElseThrow());
    }

	@Operation(summary = "签署")
	@PutMapping("/signing/{id}")
	public ApiResponse<ContractPageResponse> signing(
			@PathVariable(value = "id") String id) {
		customerEnterpriseValidator.validateSign(CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		Order order = orderValidator.validateExist(id);
		return new ApiResponse<>(orderService
				.signing(order, CertificationDef.Origin.PC.getCode())
				.orElse(null));
	}

	@Operation(summary = "判断是否有发起作废按钮")
	@GetMapping("/verification-invalid/{id}")
	public ApiResponse<Boolean> verificationInvalid(
			@PathVariable(value = "id") String id,
			@Parameter(description = "业务类型") @RequestParam Integer type) {
		return new ApiResponse<>(
				orderService.verificationInvalid(id, type).orElse(null));
	}

	@Operation(summary = "线上发起作废")
	@PutMapping("/invalid/{id}")
	public ApiResponse<ContractPageResponse> invalid(@PathVariable String id,
			@Parameter(description = "作废原因") @RequestParam String reasons) {
		log.info("订单作废原因:{}", reasons);
		Order order = orderValidator.validateInvalid(id,
				CommonDef.UserType.OUTER.getCode(), false);
		order.setInvalidReason(reasons);
		order.setInvalidInitiator(CommonDef.UserType.OUTER.getCode());
		orderService.update(order);
		return new ApiResponse<>(
				orderService.invalid(order, CommonDef.UserType.OUTER.getCode())
						.orElse(null));
	}

	@Operation(summary = "线下发起作废")
	@PutMapping("/invalid-off-line/{id}")
	public ApiResponse<Order> invalidOffLine(@PathVariable String id,
			@Parameter(description = "作废原因") @RequestParam String reasons,
			@Parameter(description = "作废文件") @RequestParam(required = false) Long fileId) {
		log.info("订单作废原因:{}", reasons);
		Order order = orderValidator.validateInvalid(id,
				CommonDef.UserType.OUTER.getCode(), false);
		order.setInvalidReason(reasons);
		order.setInvalidFileId(fileId);
		order.setInvalidInitiator(CommonDef.UserType.OUTER.getCode());
		orderService.update(order);
		return new ApiResponse<>(orderService
				.invalidOffLine(order, CommonDef.UserType.OUTER.getCode())
				.orElse(null));
	}

	@Operation(summary = "确认作废")
	@PutMapping("/confirm-invalid/{id}")
	public ApiResponse<Order> confirmInvalid(@PathVariable String id) {
		Order order = orderValidator.validateInvalidConfirm(id,
				CommonDef.UserType.OUTER.getCode());
		return new ApiResponse<>(
				orderService.confirmInvalid(order).orElse(null));
	}

	@Operation(summary = "驳回作废")
	@PutMapping("/revert-invalid/{id}")
	public ApiResponse<Order> revertInvalid(@PathVariable String id,
			@Parameter(description = "驳回原因") @RequestParam String reasons) {
		Order order = orderValidator.validateInvalidConfirm(id,
				CommonDef.UserType.OUTER.getCode());
		order.setInvalidRevokeReason(reasons);
		return new ApiResponse<>(
				orderService.revertInvalid(order).orElse(null));
	}

	/**
	 * <AUTHOR>
	 * @param response
	 * @param id
	 * @return
	 * @throws IOException
	 */
	@Operation(summary = "下载/预览订单")
	@GetMapping("/download/{id}")
	public void download(HttpServletResponse response,
			@PathVariable("id") String id) throws IOException {
		orderValidator.validateExist(id);
		orderService.downloadPdf(response, id);
	}

}
