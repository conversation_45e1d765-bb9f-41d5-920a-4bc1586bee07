package com.zhihaoscm.service.core.service.impl;

import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.dto.GeneratPdfDto;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.GoodsInfo;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.DeliverGoodsCountVo;
import com.zhihaoscm.domain.bean.vo.DeliverGoodsVo;
import com.zhihaoscm.domain.bean.vo.OrderVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.PdfUtils;
import com.zhihaoscm.service.config.LocalDateTimeAdapter;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.DeliverGoodsMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 *
 */
@Slf4j
@Service
public class DeliverGoodsServiceImpl
		extends MpStringIdBaseServiceImpl<DeliverGoods, DeliverGoodsMapper>
		implements DeliverGoodsService {

	@Autowired
	private OrderService orderService;
	@Autowired
	private ProjectService projectService;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private SignReceiptService signReceiptService;

	@Autowired
	private ContractService contractService;
	@Autowired
	private TransportOrderShipService transportOrderShipService;
	@Autowired
	private TransportOrderVehicleService transportOrderVehicleService;

	@Autowired
	private TransportOrderRailwayService transportOrderRailwayService;

	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	@Autowired
	private CustomerService customerService;

	public DeliverGoodsServiceImpl(DeliverGoodsMapper repository) {
		super(repository);
	}

	/**
	 * @description:
	 * @author: 彭湃
	 * @date: 2025/1/13 11:56
	 * @param: [page,size,
	 *             key, purchaserName, goodsName, deliverWay, deliverStatus,
	 *             startTime, endTime, sortKey, sortOrder]
	 * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.zhihaoscm.domain.bean.vo.DeliverGoodsVo>
	 **/
	@Override
	public Page<DeliverGoodsVo> paging(Integer page, Integer size, String key,
			String purchaserName, String goodsName, Integer projectType,
			List<Integer> deliverWay, List<Integer> deliverStatus,
			LocalDateTime startTime, LocalDateTime endTime, String sortKey,
			String sortOrder, String projectId, String orderId, Boolean hasAll,
			Long userId, Integer signState) {
		LambdaQueryWrapper<DeliverGoods> wrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		wrapper.eq(DeliverGoods::getDel, CommonDef.Symbol.NO.getCode());

		// 根据签收状态过滤
		if (Objects.nonNull(signState)) {
			wrapper.eq(DeliverGoods::getStatus,
					DeliverGoodsDef.Status.DELIVER_COMPLETE.getCode());
			OrderDef.BusinessStatus from = OrderDef.BusinessStatus
					.from(signState);
			List<SignReceipt> signReceipts = switch (from) {
				case NOT_STARTED, INVALID, PRE_RECONCILIATION -> null;
				case IN_PROGRESS -> signReceiptService.find(null,
						List.of(SignReceiptDef.Status.DRAFT.getCode(),
								SignReceiptDef.Status.REVERTED.getCode(),
								SignReceiptDef.Status.SIGNING.getCode(),
								SignReceiptDef.Status.CONFIRMING.getCode()));
				case COMPLETED -> signReceiptService.find(null,
						List.of(SignReceiptDef.Status.FINISHED.getCode()));
			};
			if (from == OrderDef.BusinessStatus.NOT_STARTED) {
				wrapper.isNull(DeliverGoods::getSignReceiptId);
			} else if (CollectionUtils.isNotEmpty(signReceipts)) {
				wrapper.in(DeliverGoods::getSignReceiptId,
						signReceipts.stream().map(SignReceipt::getId).toList());
			} else {
				return new Page<>();
			}
		}

		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (Objects.nonNull(userId)) {
			if (!hasAll) {
				// 处理人是自己在的
				List<String> projectIdList = projectService.findByUserId(userId,
						null);
				if (org.apache.commons.collections4.CollectionUtils
						.isNotEmpty(projectIdList)) {
					wrapper.in(DeliverGoods::getProjectId, projectIdList);
				} else {
					return Page.of(page, size, 0);
				}
			}
		}
		if (Objects.nonNull(key)) {
			wrapper.and(i -> i.like(DeliverGoods::getId, key).or()
					.like(DeliverGoods::getOrderId, key));
		}

		// 根据采购方名称及货物名称查询
		List<Contract> contracts;
		contracts = contractService.find(purchaserName, goodsName, projectType,
				null);
		if (CollectionUtils.isEmpty(contracts)) {
			return new Page<>();
		}
		if (CollectionUtils.isNotEmpty(contracts)) {
			wrapper.in(DeliverGoods::getContractId,
					contracts.stream().map(Contract::getId).toList());
		}
		wrapper.eq(Objects.nonNull(orderId), DeliverGoods::getOrderId, orderId);

		wrapper.eq(Objects.nonNull(projectId), DeliverGoods::getProjectId,
				projectId);

		wrapper.ge(Objects.nonNull(startTime), DeliverGoods::getDeliveryDate,
				startTime);
		wrapper.le(Objects.nonNull(endTime), DeliverGoods::getDeliveryDate,
				endTime);

		wrapper.in(CollectionUtils.isNotEmpty(deliverWay),
				DeliverGoods::getDelivery, deliverWay);
		wrapper.in(CollectionUtils.isNotEmpty(deliverStatus),
				DeliverGoods::getStatus, deliverStatus);

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + ", id desc");
		} else {
			// 默认按照创建时间降序排列
			wrapper.last("order by updated_time desc ");
		}

		Page<DeliverGoods> paging = repository
				.selectPage(new Page<>(page, size), wrapper);

		List<DeliverGoodsVo> vos = this.packVos(paging.getRecords(), contracts);
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<DeliverGoodsVo> pagingBuy(Integer page, Integer size,
			String key, String sellerName, String goodsName,
			Integer projectType, List<Integer> deliverWay,
			LocalDateTime startTime, LocalDateTime endTime, String sortKey,
			String sortOrder, String projectId, String orderId, Boolean hasAll,
			Long userId, String param, List<Integer> deliverStatusList,
			Integer signStatus) {
		LambdaQueryWrapper<DeliverGoods> wrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		wrapper.eq(DeliverGoods::getDel, CommonDef.Symbol.NO.getCode());

		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (Objects.nonNull(userId)) {
			if (!hasAll) {
				// 处理人是自己在的
				List<String> projectIdList = projectService.findByUserId(userId,
						null);
				if (org.apache.commons.collections4.CollectionUtils
						.isNotEmpty(projectIdList)) {
					wrapper.in(DeliverGoods::getProjectId, projectIdList);
				} else {
					return Page.of(page, size, 0);
				}
			}
		}
		if (Objects.nonNull(key)) {
			List<Project> projectList = projectService.findByNameLike(key);
			if (CollectionUtils.isNotEmpty(projectList)) {
				wrapper.and(i -> i.like(DeliverGoods::getId, key).or().in(
						DeliverGoods::getProjectId,
						projectList.stream().map(Project::getId).toList()));
			} else {
				wrapper.and(i -> i.like(DeliverGoods::getId, key));
			}
		}
		// 签收编号或者合同名称
		if (Objects.nonNull(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(x -> x.like(DeliverGoods::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					DeliverGoods::getContractId, contractIds));
		}

		// 根据采购方名称及货物名称查询
		List<Contract> contracts;
		contracts = contractService.find(sellerName, goodsName, projectType,
				null);
		if (CollectionUtils.isEmpty(contracts)) {
			return new Page<>();
		}
		if (CollectionUtils.isNotEmpty(contracts)) {
			wrapper.in(DeliverGoods::getContractId,
					contracts.stream().map(Contract::getId).toList());
		}
		wrapper.eq(Objects.nonNull(orderId), DeliverGoods::getOrderId, orderId);

		wrapper.eq(Objects.nonNull(projectId), DeliverGoods::getProjectId,
				projectId);

		wrapper.ge(Objects.nonNull(startTime), DeliverGoods::getDeliveryDate,
				startTime);
		wrapper.le(Objects.nonNull(endTime), DeliverGoods::getDeliveryDate,
				endTime);

		wrapper.in(CollectionUtils.isNotEmpty(deliverWay),
				DeliverGoods::getDelivery, deliverWay);

		if (CollectionUtils.isNotEmpty(deliverStatusList)) {
			wrapper.in(DeliverGoods::getStatus, deliverStatusList);
		}

		if (Objects.nonNull(signStatus)) {
			LambdaQueryWrapper<SignReceipt> wrapper1 = new LambdaQueryWrapper<>(
					SignReceipt.class);
			switch (signStatus) {
				case 1 -> wrapper.isNull(DeliverGoods::getSignReceiptId);

				case 2 -> {
					List<SignReceipt> list = signReceiptService.getRepository()
							.selectList(wrapper1
									.eq(SignReceipt::getDel,
											CommonDef.Symbol.NO.getCode())
									.ne(SignReceipt::getStatus,
											SignReceiptDef.Status.FINISHED
													.getCode()));
					List<String> strings = list.stream().map(SignReceipt::getId)
							.toList();
					if (CollectionUtils.isEmpty(strings)) {
						return new Page<>();
					} else {
						wrapper.in(DeliverGoods::getSignReceiptId, strings);
					}
				}
				case 3 -> {
					List<SignReceipt> list = signReceiptService.getRepository()
							.selectList(wrapper1
									.eq(SignReceipt::getDel,
											CommonDef.Symbol.NO.getCode())
									.eq(SignReceipt::getStatus,
											SignReceiptDef.Status.FINISHED
													.getCode()));
					List<String> strings = list.stream().map(SignReceipt::getId)
							.toList();
					if (CollectionUtils.isEmpty(strings)) {
						return new Page<>();
					} else {
						wrapper.in(DeliverGoods::getSignReceiptId, strings);
					}
				}
			}
		}

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + ", id desc");
		} else {
			// 默认按照创建时间降序排列
			wrapper.last("order by created_time desc ");
		}

		Page<DeliverGoods> paging = repository
				.selectPage(new Page<>(page, size), wrapper);

		List<DeliverGoodsVo> vos = this.packVos(paging.getRecords(), contracts);
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	/**
	 * @description:pc-采购-分页查询提货通知
	 * @Author: 许晶
	 **/
	@Override
	public Page<DeliverGoodsVo> customPaging(Integer page, Integer size,
			String key, String sellerName, String goodsName,
			Integer projectType, List<Integer> deliverWay,
			List<Integer> deliverStatus, LocalDateTime startTime,
			LocalDateTime endTime, String sortKey, String sortOrder,
			Long customerId, String projectId, String orderId,
			Integer signState, String contractName) {
		LambdaQueryWrapper<DeliverGoods> wrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		wrapper.eq(DeliverGoods::getDel, CommonDef.Symbol.NO.getCode());
		if (Objects.nonNull(key)) {
			wrapper.and(i -> i.like(DeliverGoods::getId, key).or()
					.like(DeliverGoods::getOrderId, key));
		}
		if (Objects.nonNull(contractName)) {
			List<Contract> contracts = contractService.find(null, null,
					contractName, ContractDef.Type.SELL.getCode(), null);
			if (CollectionUtils.isEmpty(contracts)) {
				return new Page<>();
			} else {
				wrapper.in(DeliverGoods::getContractId,
						contracts.stream().map(Contract::getId).toList());
			}
		}
		// 根据签收状态过滤
		if (Objects.nonNull(signState)) {
			wrapper.eq(DeliverGoods::getStatus,
					DeliverGoodsDef.Status.DELIVER_COMPLETE.getCode());
			OrderDef.BusinessStatus from = OrderDef.BusinessStatus
					.from(signState);
			List<SignReceipt> signReceipts = switch (from) {
				case NOT_STARTED, INVALID, PRE_RECONCILIATION -> null;
				case IN_PROGRESS -> signReceiptService.find(null,
						List.of(SignReceiptDef.Status.DRAFT.getCode(),
								SignReceiptDef.Status.REVERTED.getCode(),
								SignReceiptDef.Status.SIGNING.getCode(),
								SignReceiptDef.Status.CONFIRMING.getCode()));
				case COMPLETED -> signReceiptService.find(null,
						List.of(SignReceiptDef.Status.FINISHED.getCode()));
			};
			if (from == OrderDef.BusinessStatus.NOT_STARTED) {
				wrapper.isNull(DeliverGoods::getSignReceiptId);
			} else if (CollectionUtils.isNotEmpty(signReceipts)) {
				wrapper.in(DeliverGoods::getSignReceiptId,
						signReceipts.stream().map(SignReceipt::getId).toList());
			} else {
				return new Page<>();
			}
		}

		// 根据销售方名称模糊搜索
		if (StringUtils.isNotBlank(sellerName)) {
			wrapper.and(i -> i.apply(StringUtils.isNoneBlank(sellerName),
					"(JSON_EXTRACT(seller_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					sellerName));
		}
		// 根据采购方名称及货物名称查询
		List<Contract> contracts;
		if (projectType == 1) {
			// 销售方是自己的数据
			wrapper.eq(DeliverGoods::getSellerId, customerId);
			contracts = contractService.findUp(null, goodsName, null,
					projectType, customerId);
		} else {
			// 采购方是自己的数据
			wrapper.eq(DeliverGoods::getPurchaserId, customerId);
			contracts = contractService.find(null, goodsName, null, projectType,
					customerId);
		}
		if (CollectionUtils.isEmpty(contracts)) {
			return new Page<>();
		}
		if (CollectionUtils.isNotEmpty(contracts)) {
			wrapper.in(DeliverGoods::getContractId,
					contracts.stream().map(Contract::getId).toList());
		}

		wrapper.eq(Objects.nonNull(projectId), DeliverGoods::getProjectId,
				projectId);
		wrapper.eq(Objects.nonNull(orderId), DeliverGoods::getOrderId, orderId);

		wrapper.ge(Objects.nonNull(startTime), DeliverGoods::getDeliveryDate,
				startTime);
		wrapper.le(Objects.nonNull(endTime), DeliverGoods::getDeliveryDate,
				endTime);
		wrapper.in(Objects.nonNull(deliverWay), DeliverGoods::getDelivery,
				deliverWay);

		if (Objects.nonNull(deliverStatus)) {
			wrapper.in(DeliverGoods::getStatus, deliverStatus);
		}

		if (projectType == 1) {
			wrapper.in(DeliverGoods::getStatus,
					DeliverGoodsDef.Status.WAIT_DELIVER.getCode(),
					DeliverGoodsDef.Status.DELIVERING.getCode(),
					DeliverGoodsDef.Status.DELIVER_INVALID.getCode(),
					DeliverGoodsDef.Status.DELIVER_CANCEL.getCode(),
					DeliverGoodsDef.Status.DELIVER_COMPLETE.getCode());
		}
		// 默认查询 运输中和 已完成
		else {
			wrapper.in(DeliverGoods::getStatus,
					DeliverGoodsDef.Status.DELIVERING.getCode(),
					DeliverGoodsDef.Status.DELIVER_INVALID.getCode(),
					DeliverGoodsDef.Status.DELIVER_CANCEL.getCode(),
					DeliverGoodsDef.Status.DELIVER_COMPLETE.getCode());
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + ", id desc ");
		} else {
			// 默认按照发货日期倒序
			wrapper.last("order by updated_time desc ");
		}

		Page<DeliverGoods> paging = repository
				.selectPage(new Page<>(page, size), wrapper);

		List<DeliverGoodsVo> vos = this.packVos(paging.getRecords(), contracts);
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	/**
	 * @description: 根据ID查询发货单VO
	 * @author: 彭湃
	 * @date: 2025/1/13 11:53
	 * @param: [id]
	 * @return: com.zhihaoscm.domain.bean.vo.DeliverGoodsVo
	 **/
	@Override
	public Optional<DeliverGoodsVo> findVoById(String id) {
		DeliverGoodsVo goodsVo = this.findOne(id).stream().map(e -> {
			DeliverGoodsVo deliverGoodsVo = new DeliverGoodsVo();
			projectService.findOne(e.getProjectId())
					.ifPresent(deliverGoodsVo::setProject);
			contractService.findOne(e.getContractId())
					.ifPresent(deliverGoodsVo::setContract);
			orderService.findOne(e.getOrderId())
					.ifPresent(deliverGoodsVo::setOrder);
			deliverGoodsVo.setTransportOrderShips(transportOrderShipService
					.findByDeliverGoodsIds(List.of(id)));
			deliverGoodsVo
					.setTransportOrderVehicles(transportOrderVehicleService
							.findByDeliverGoodsIds(List.of(id)));
			deliverGoodsVo
					.setTransportOrderRailways(transportOrderRailwayService
							.findByDeliverGoodsIds(List.of(id)));
			deliverGoodsVo.setDeliverGoods(e);
			return deliverGoodsVo;
		}).findFirst().orElseThrow(null);
		return Optional.of(goodsVo);
	}

	@Override
	public List<DeliverGoods> findUnfinished(String projectId) {
		LambdaQueryWrapper<DeliverGoods> queryWrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		queryWrapper.eq(DeliverGoods::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(DeliverGoods::getProjectId, projectId);
		queryWrapper.ne(DeliverGoods::getStatus,
				DeliverGoodsDef.Status.DELIVER_COMPLETE.getCode());
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<DeliverGoods> findByOrderIds(List<String> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Collections.emptyList();
		}
		LambdaQueryWrapper<DeliverGoods> wrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		this.filterDeleted(wrapper);
		wrapper.in(DeliverGoods::getOrderId, orderIds);
		return repository.selectList(wrapper);
	}

	@Override
	public List<DeliverGoods> findByOrderIdAndSignReceiptIds(String orderId,
			List<String> signReceiptIds) {
		LambdaQueryWrapper<DeliverGoods> wrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		this.filterDeleted(wrapper);
		if (StringUtils.isNotBlank(orderId)) {
			wrapper.eq(DeliverGoods::getOrderId, orderId);
		}
		if (CollectionUtils.isNotEmpty(signReceiptIds)) {
			wrapper.in(DeliverGoods::getSignReceiptId, signReceiptIds);
		}
		return repository.selectList(wrapper);
	}

	@Override
	public List<DeliverGoods> findByOrderIdsAndStates(List<String> orderIds,
			List<Integer> states) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Collections.emptyList();
		}
		LambdaQueryWrapper<DeliverGoods> wrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		this.filterDeleted(wrapper);
		wrapper.in(DeliverGoods::getOrderId, orderIds);
		wrapper.in(CollectionUtils.isNotEmpty(states), DeliverGoods::getStatus,
				states);
		return repository.selectList(wrapper);
	}

	// 销售里面的发货信息
	@Override
	public List<DeliverGoodsVo> findVosByOrderIds(List<String> orderIds) {
		if (CollectionUtils.isEmpty(orderIds)) {
			return Collections.emptyList();
		}
		LambdaQueryWrapper<DeliverGoods> wrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		this.filterDeleted(wrapper);
		wrapper.eq(DeliverGoods::getStatus,
				DeliverGoodsDef.Status.DELIVER_COMPLETE.getCode());
		wrapper.in(DeliverGoods::getOrderId, orderIds);
		return packDeliverGoodsVos(repository.selectList(wrapper));
	}

	// 销售里面的发货信息
	@Override
	public List<DeliverGoodsVo> findVosBySignReceiptIds(
			List<String> signReceiptIds) {
		if (CollectionUtils.isEmpty(signReceiptIds)) {
			return Collections.emptyList();
		}
		LambdaQueryWrapper<DeliverGoods> wrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		this.filterDeleted(wrapper);
		wrapper.eq(DeliverGoods::getStatus,
				DeliverGoodsDef.Status.DELIVER_COMPLETE.getCode());
		wrapper.in(DeliverGoods::getSignReceiptId, signReceiptIds);
		return packDeliverGoodsVos(repository.selectList(wrapper));
	}

	/**
	 * @description: 查询未关联签收单的发货单
	 * @author: 彭湃
	 * @date: 2025/1/18 16:50
	 * @param: []
	 * @return: java.util.List<com.zhihaoscm.domain.bean.entity.DeliverGoods>
	 **/
	@Override
	public List<DeliverGoods> findUnReceipt(String contractId) {
		// 查询合同下的所有订单
		List<Order> byContractId = orderService.findByContractId(contractId);
		if (CollectionUtils.isEmpty(byContractId)) {
			return List.of();
		}
		// 查询订单下所有未关联签收单的发货单
		List<String> orderIds = byContractId.stream().map(Order::getId)
				.toList();
		LambdaQueryWrapper<DeliverGoods> wrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		this.filterDeleted(wrapper);
		wrapper.eq(DeliverGoods::getStatus,
				DeliverGoodsDef.Status.DELIVER_COMPLETE.getCode());
		wrapper.isNull(DeliverGoods::getSignReceiptId);
		wrapper.in(CollectionUtils.isNotEmpty(orderIds),
				DeliverGoods::getOrderId, orderIds);
		List<DeliverGoods> deliverGoods = repository.selectList(wrapper);
		if (Objects.isNull(deliverGoods)) {
			deliverGoods = new ArrayList<>();
		}
		return deliverGoods;
	}

	@Override
	public List<DeliverGoods> findBySignReceiptIds(
			List<String> signReceiptIds) {
		if (CollectionUtils.isEmpty(signReceiptIds)) {
			return Collections.emptyList();
		}
		LambdaQueryWrapper<DeliverGoods> wrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		this.filterDeleted(wrapper);
		wrapper.in(CollectionUtils.isNotEmpty(signReceiptIds),
				DeliverGoods::getSignReceiptId, signReceiptIds);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<BigDecimal> findTotalQuantity(String projectId,
			Integer state, LocalDateTime startTime, LocalDateTime endTime,
			Integer type, String param, List<Integer> deliverWay,
			String purchaserName, String key) {
		BigDecimal goodsTotalQuantity = BigDecimal.ZERO;
		List<Contract> contracts;
		if (StringUtils.isBlank(purchaserName)) {
			contracts = contractService.find(null, null, null, type, null);
		} else {
			contracts = contractService.find(purchaserName, null, null, type,
					null);
		}
		if (CollectionUtils.isEmpty(contracts)) {
			return Optional.of(BigDecimal.ZERO);
		}
		LambdaQueryWrapper<DeliverGoods> wrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		// 发货编号或者关联订单号
		if (Objects.nonNull(key)) {
			wrapper.and(i -> i.like(DeliverGoods::getId, key).or()
					.like(DeliverGoods::getOrderId, key));
		}
		// 签收编号或者合同名称
		if (Objects.nonNull(param)) {
			List<String> contractIds = contractService.findByNameLike(param)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(x -> x.like(DeliverGoods::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					DeliverGoods::getContractId, contractIds));
		}
		wrapper.in(Objects.nonNull(deliverWay), DeliverGoods::getDelivery,
				deliverWay);
		wrapper.eq(DeliverGoods::getDel, CommonDef.Symbol.NO.getCode())
				.in(CollectionUtils.isNotEmpty(contracts),
						DeliverGoods::getContractId,
						contracts.stream().map(Contract::getId).toList())
				.ge(Objects.nonNull(startTime), DeliverGoods::getDeliveryDate,
						startTime)
				.le(Objects.nonNull(endTime), DeliverGoods::getDeliveryDate,
						endTime)
				.eq(DeliverGoods::getProjectId, projectId);
		List<DeliverGoods> deliverGoods = repository.selectList(wrapper);
		if (CollectionUtils.isNotEmpty(deliverGoods)) {
			if (ContractDef.Type.SELL.match(type)) {
				// 销售类型的要过滤出已完成状态的
				List<DeliverGoods> deliverGoods1 = deliverGoods.stream()
						.filter(i -> i.getStatus().equals(state)).toList();
				goodsTotalQuantity = deliverGoods1.stream()
						.map(DeliverGoods::getActualGoodsTotalQuantity)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			} else {
				goodsTotalQuantity = deliverGoods.stream()
						.map(DeliverGoods::getGoodsTotalQuantity)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);

			}
		}

		return Optional.of(goodsTotalQuantity);
	}

	@Override
	public Optional<BigDecimal> findOrderTotalQuantity(String orderId,
			List<Integer> states, LocalDateTime startTime,
			LocalDateTime endTime, Integer type) {
		LambdaQueryWrapper<DeliverGoods> wrapper;
		if (ContractDef.Type.SELL.match(type)) {
			wrapper = Wrappers.query(DeliverGoods.class).select(
					"SUM(actual_goods_total_quantity) goodsTotalQuantity")
					.lambda();
		} else {
			wrapper = Wrappers.query(DeliverGoods.class)
					.select("SUM(goods_total_quantity) goodsTotalQuantity")
					.lambda();
		}

		wrapper.eq(DeliverGoods::getDel, CommonDef.Symbol.NO.getCode())
				.ge(Objects.nonNull(startTime), DeliverGoods::getDeliveryDate,
						startTime)
				.le(Objects.nonNull(endTime), DeliverGoods::getDeliveryDate,
						endTime)
				.eq(DeliverGoods::getOrderId, orderId)
				.in(CollectionUtils.isNotEmpty(states), DeliverGoods::getStatus,
						states);
		DeliverGoods deliverGoods = repository.selectOne(wrapper);
		return Objects.isNull(deliverGoods) ? Optional.of(BigDecimal.ZERO)
				: Optional.of(deliverGoods.getGoodsTotalQuantity());
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Optional<DeliverGoods> createDeliver(DeliverGoods deliverGoods) {
		Project project = projectService.findOne(deliverGoods.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService
				.findOne(deliverGoods.getContractId()).orElse(new Contract());
		// 按规则设置签收单id
		deliverGoods.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(), RedisKeys.Cache.DELIVER_GOODS_CODE_GENERATOR,
				ContractDef.Type.from(contract.getContractType()).getStr()
						+ AutoCodeDef.BusinessRuleCode.DELIVER_SUFFIX.getCode(),
				4, AutoCodeDef.DATE_TYPE.yy));
		if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
				.match(deliverGoods.getDelivery())) {
			deliverGoods.setStatus(DeliverGoodsDef.Status.DELIVERING.getCode());
			deliverGoods.setActualGoodsTotalQuantity(
					deliverGoods.getGoodsTotalQuantity());
		} else {
			deliverGoods.setActualGoodsTotalQuantity(BigDecimal.ZERO);
			deliverGoods
					.setStatus(DeliverGoodsDef.Status.WAIT_DELIVER.getCode());
		}
		deliverGoods.setPurchaserId(contract.getDownstreamPurchasersId());
		deliverGoods.setPurchaserBusinessId(contract.getDownstreamId());
		deliverGoods.setPurchaserEnterprise(
				contract.getDownstreamPurchasersEnterprise());

		deliverGoods.setSellerId(contract.getSupplierChainId());
		deliverGoods.setSellerEnterprise(contract.getSupplierChainEnterprise());

		if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
				.match(deliverGoods.getDelivery())) {
			this.sendNotice(deliverGoods,
					wxSubscriptionProperties.getCreateGoodsCode(),
					MessageFormat.format(
							UserMessageConstants.DELIVER_GOODS_TRANSIT_TEMPLATE,
							deliverGoods.getId()),
					ContractDef.ContractType.SALES.getCode());
		}

		// 创建发货单时，增加订单已发货数量并修改发货状态
		Order order = orderService.findOne(deliverGoods.getOrderId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30009003));
		if (Objects.nonNull(order.getDeliveredWeight())) {
			order.setDeliveredWeight(order.getDeliveredWeight()
					.add(deliverGoods.getGoodsTotalQuantity()));
		} else {
			order.setDeliveredWeight(deliverGoods.getGoodsTotalQuantity());
		}
		if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
				.match(deliverGoods.getDelivery())) {
			order.setActualDeliveredWeight(order.getActualDeliveredWeight()
					.add(deliverGoods.getActualGoodsTotalQuantity()));
		}
		if (order.getActualDeliveredWeight()
				.compareTo(order.getGoodsTotalQuantity()) >= 0) {
			order.setDeliveryStatus(
					OrderDef.BusinessStatus.COMPLETED.getCode());
			this.sendNotice(deliverGoods,
					wxSubscriptionProperties.getFinishOrderGoodCode(),
					MessageFormat.format(
							UserMessageConstants.ORDER_FINISHED_GOODS_TEMPLATE,
							deliverGoods.getId()),
					ContractDef.ContractType.SALES.getCode());
		} else {
			order.setDeliveryStatus(
					OrderDef.BusinessStatus.IN_PROGRESS.getCode());
		}
		orderService.updateAllProperties(order);
		return Optional.of(super.create(deliverGoods));
	}

	@Override
	public Optional<DeliverGoods> customCreateDeliver(
			DeliverGoods deliverGoods) {
		Project project = projectService.findOne(deliverGoods.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService
				.findOne(deliverGoods.getContractId()).orElse(new Contract());
		// 按规则设置签收单id
		deliverGoods.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(), RedisKeys.Cache.DELIVER_GOODS_CODE_GENERATOR,
				ContractDef.Type.from(contract.getContractType()).getStr()
						+ AutoCodeDef.BusinessRuleCode.DELIVER_SUFFIX.getCode(),
				4, AutoCodeDef.DATE_TYPE.yy));
		if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
				.match(deliverGoods.getDelivery())) {
			deliverGoods.setStatus(DeliverGoodsDef.Status.DELIVERING.getCode());
			deliverGoods.setActualGoodsTotalQuantity(
					deliverGoods.getGoodsTotalQuantity());
			SpringUtil.getBean(DeliverGoodsService.class).notice(deliverGoods,
					1);
		} else {
			deliverGoods.setActualGoodsTotalQuantity(BigDecimal.ZERO);
			deliverGoods
					.setStatus(DeliverGoodsDef.Status.WAIT_DELIVER.getCode());
		}

		deliverGoods.setSellerId(contract.getUpstreamSuppliersId());
		deliverGoods.setSellerBusinessId(contract.getUpstreamId());
		deliverGoods
				.setSellerEnterprise(contract.getUpstreamSuppliersEnterprise());

		deliverGoods.setPurchaserId(contract.getSupplierChainId());
		deliverGoods
				.setPurchaserEnterprise(contract.getSupplierChainEnterprise());

		if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
				.match(deliverGoods.getDelivery())) {
			this.sendNotice(deliverGoods,
					wxSubscriptionProperties.getCreateGoodsCode(),
					MessageFormat.format(
							UserMessageConstants.DELIVER_GOODS_TRANSIT_TEMPLATE,
							deliverGoods.getId()),
					ContractDef.ContractType.PURCHASE.getCode());
		}

		// 创建发货单时，增加订单已发货数量并修改发货状态
		Order order = orderService.findOne(deliverGoods.getOrderId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30009003));
		if (Objects.nonNull(order.getDeliveredWeight())) {
			order.setDeliveredWeight(order.getDeliveredWeight()
					.add(deliverGoods.getGoodsTotalQuantity()));
		} else {
			order.setDeliveredWeight(deliverGoods.getGoodsTotalQuantity());
		}
		if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
				.match(deliverGoods.getDelivery())) {
			order.setActualDeliveredWeight(order.getActualDeliveredWeight()
					.add(deliverGoods.getActualGoodsTotalQuantity()));
		}
		if (order.getActualDeliveredWeight()
				.compareTo(order.getGoodsTotalQuantity()) >= 0) {
			order.setDeliveryStatus(
					OrderDef.BusinessStatus.COMPLETED.getCode());
			this.sendNotice(deliverGoods,
					wxSubscriptionProperties.getFinishOrderGoodCode(),
					MessageFormat.format(
							UserMessageConstants.ORDER_FINISHED_GOODS_TEMPLATE,
							deliverGoods.getId()),
					ContractDef.ContractType.PURCHASE.getCode());
		} else {
			order.setDeliveryStatus(
					OrderDef.BusinessStatus.IN_PROGRESS.getCode());
		}
		orderService.updateAllProperties(order);
		return Optional.of(super.create(deliverGoods));
	}

	@Override
	public Optional<DeliverGoods> createBuy(DeliverGoods deliverGoods) {
		Project project = projectService.findOne(deliverGoods.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService
				.findOne(deliverGoods.getContractId()).orElse(new Contract());
		// 按规则设置签收单id
		deliverGoods.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(), RedisKeys.Cache.DELIVER_GOODS_CODE_GENERATOR,
				ContractDef.Type.from(contract.getContractType()).getStr()
						+ AutoCodeDef.BusinessRuleCode.DELIVER_BUY_SUFFIX
								.getCode(),
				4, AutoCodeDef.DATE_TYPE.yy));
		// 创建发货单时，增加订单已发货数量并修改发货状态
		Order order = orderService.findOne(deliverGoods.getOrderId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30009003));
		if (Objects.nonNull(order.getDeliveredWeight())) {
			order.setDeliveredWeight(order.getDeliveredWeight()
					.add(deliverGoods.getGoodsTotalQuantity()));
		} else {
			order.setDeliveredWeight(deliverGoods.getGoodsTotalQuantity());
		}
		orderService.updateAllProperties(order);
		return Optional.of(super.create(deliverGoods));
	}

	/**
	 * @description: 修改发货单
	 * @author: 彭湃
	 * @date: 2025/1/13 11:54
	 * @param: [deliverGoods,
	 *             saveType]
	 * @return: com.zhihaoscm.domain.bean.entity.DeliverGoods
	 **/
	@Override
	public Optional<DeliverGoods> updateDeliver(DeliverGoods deliverGoods) {
		if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
				.match(deliverGoods.getDelivery())) {
			deliverGoods.setStatus(DeliverGoodsDef.Status.DELIVERING.getCode());
			deliverGoods.setActualGoodsTotalQuantity(
					deliverGoods.getGoodsTotalQuantity());
			this.sendNotice(deliverGoods,
					wxSubscriptionProperties.getCreateGoodsCode(),
					MessageFormat.format(
							UserMessageConstants.DELIVER_GOODS_TRANSIT_TEMPLATE,
							deliverGoods.getId()),
					ContractDef.ContractType.SALES.getCode());
		} else {
			deliverGoods
					.setStatus(DeliverGoodsDef.Status.WAIT_DELIVER.getCode());
		}
		DeliverGoods old = this.findOne(deliverGoods.getId()).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30001001));
		Order order = orderService.findOne(old.getOrderId())
				.orElse(new Order());
		Order order1 = orderService.findOne(deliverGoods.getOrderId())
				.orElse(new Order());
		if (!old.getOrderId().equals(deliverGoods.getOrderId())) {
			order.setDeliveredWeight(order.getDeliveredWeight()
					.subtract(old.getGoodsTotalQuantity()));
			if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
					.match(deliverGoods.getDelivery())) {
				order.setActualDeliveredWeight(order.getActualDeliveredWeight()
						.subtract(old.getActualGoodsTotalQuantity()));
			}
			if (order.getActualDeliveredWeight()
					.compareTo(order.getGoodsTotalQuantity()) < 0) {
				order.setDeliveryStatus(
						OrderDef.BusinessStatus.IN_PROGRESS.getCode());
			}
			if (order.getActualDeliveredWeight()
					.compareTo(BigDecimal.ZERO) <= 0) {
				order.setDeliveredWeight(BigDecimal.ZERO);
				order.setActualDeliveredWeight(BigDecimal.ZERO);
				order.setDeliveryStatus(
						OrderDef.BusinessStatus.NOT_STARTED.getCode());
			}
			orderService.updateAllProperties(order);
			if (Objects.nonNull(order1.getDeliveredWeight())) {
				order1.setDeliveredWeight(order1.getDeliveredWeight()
						.add(deliverGoods.getGoodsTotalQuantity()));
			} else {
				order1.setDeliveredWeight(deliverGoods.getGoodsTotalQuantity());
			}
			if (order1.getActualDeliveredWeight()
					.compareTo(order1.getGoodsTotalQuantity()) >= 0) {
				order1.setDeliveryStatus(
						OrderDef.BusinessStatus.COMPLETED.getCode());
			} else {
				order1.setDeliveryStatus(
						OrderDef.BusinessStatus.IN_PROGRESS.getCode());
			}
			orderService.updateAllProperties(order1);
		} else {
			order.setDeliveredWeight(order.getDeliveredWeight()
					.subtract(old.getGoodsTotalQuantity())
					.add(deliverGoods.getGoodsTotalQuantity()));
			if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
					.match(deliverGoods.getDelivery())) {
				order.setActualDeliveredWeight(order.getActualDeliveredWeight()
						.subtract(old.getActualGoodsTotalQuantity())
						.add(deliverGoods.getActualGoodsTotalQuantity()));
			}
			if (order.getActualDeliveredWeight()
					.compareTo(order.getGoodsTotalQuantity()) >= 0) {
				order.setDeliveryStatus(
						OrderDef.BusinessStatus.COMPLETED.getCode());
			} else {
				order.setDeliveryStatus(
						OrderDef.BusinessStatus.IN_PROGRESS.getCode());
			}
			orderService.updateAllProperties(order);
		}
		return Optional.of(this.updateAllProperties(deliverGoods));
	}

	@Override
	public Optional<DeliverGoods> customUpdateDeliver(
			DeliverGoods deliverGoods) {
		if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
				.match(deliverGoods.getDelivery())) {
			deliverGoods.setStatus(DeliverGoodsDef.Status.DELIVERING.getCode());
			deliverGoods.setActualGoodsTotalQuantity(
					deliverGoods.getGoodsTotalQuantity());
			this.sendNotice(deliverGoods,
					wxSubscriptionProperties.getCreateGoodsCode(),
					MessageFormat.format(
							UserMessageConstants.DELIVER_GOODS_TRANSIT_TEMPLATE,
							deliverGoods.getId()),
					ContractDef.ContractType.PURCHASE.getCode());
		} else {
			deliverGoods
					.setStatus(DeliverGoodsDef.Status.WAIT_DELIVER.getCode());
		}
		DeliverGoods old = this.findOne(deliverGoods.getId()).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30001001));
		Order order = orderService.findOne(old.getOrderId())
				.orElse(new Order());
		Order order1 = orderService.findOne(deliverGoods.getOrderId())
				.orElse(new Order());
		if (!old.getOrderId().equals(deliverGoods.getOrderId())) {
			order.setDeliveredWeight(order.getDeliveredWeight()
					.subtract(old.getGoodsTotalQuantity()));
			if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
					.match(deliverGoods.getDelivery())) {
				order.setActualDeliveredWeight(order.getActualDeliveredWeight()
						.subtract(old.getActualGoodsTotalQuantity()));
			}
			if (order.getActualDeliveredWeight()
					.compareTo(order.getGoodsTotalQuantity()) < 0) {
				order.setDeliveryStatus(
						OrderDef.BusinessStatus.IN_PROGRESS.getCode());
			}
			if (order.getActualDeliveredWeight()
					.compareTo(BigDecimal.ZERO) <= 0) {
				order.setDeliveredWeight(BigDecimal.ZERO);
				order.setActualDeliveredWeight(BigDecimal.ZERO);
				order.setDeliveryStatus(
						OrderDef.BusinessStatus.NOT_STARTED.getCode());
			}
			orderService.updateAllProperties(order);
			if (Objects.nonNull(order1.getDeliveredWeight())) {
				order1.setDeliveredWeight(order1.getDeliveredWeight()
						.add(deliverGoods.getGoodsTotalQuantity()));
			} else {
				order1.setDeliveredWeight(deliverGoods.getGoodsTotalQuantity());
			}
			if (order1.getActualDeliveredWeight()
					.compareTo(order1.getGoodsTotalQuantity()) >= 0) {
				order1.setDeliveryStatus(
						OrderDef.BusinessStatus.COMPLETED.getCode());
			} else {
				order1.setDeliveryStatus(
						OrderDef.BusinessStatus.IN_PROGRESS.getCode());
			}
			orderService.updateAllProperties(order1);
		} else {
			order.setDeliveredWeight(order.getDeliveredWeight()
					.subtract(old.getGoodsTotalQuantity())
					.add(deliverGoods.getGoodsTotalQuantity()));
			if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
					.match(deliverGoods.getDelivery())) {
				order.setActualDeliveredWeight(order.getActualDeliveredWeight()
						.subtract(old.getActualGoodsTotalQuantity())
						.add(deliverGoods.getActualGoodsTotalQuantity()));
			}
			if (order.getActualDeliveredWeight()
					.compareTo(order.getGoodsTotalQuantity()) >= 0) {
				order.setDeliveryStatus(
						OrderDef.BusinessStatus.COMPLETED.getCode());
			} else {
				order.setDeliveryStatus(
						OrderDef.BusinessStatus.IN_PROGRESS.getCode());
			}
			orderService.updateAllProperties(order);
		}
		return Optional.of(this.updateAllProperties(deliverGoods));
	}

	@Override
	public Optional<DeliverGoods> updateBuy(DeliverGoods deliverGoods) {
		DeliverGoods old = this.findOne(deliverGoods.getId()).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30001001));
		Order order = orderService.findOne(old.getOrderId())
				.orElse(new Order());
		Order order1 = orderService.findOne(deliverGoods.getOrderId())
				.orElse(new Order());
		if (!old.getOrderId().equals(deliverGoods.getOrderId())) {
			BigDecimal subtract = order.getDeliveredWeight()
					.subtract(old.getGoodsTotalQuantity());
			subtract = subtract.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO
					: subtract;
			order.setDeliveredWeight(subtract);
			orderService.updateAllProperties(order);
			if (Objects.nonNull(order1.getDeliveredWeight())) {
				order1.setDeliveredWeight(order1.getDeliveredWeight()
						.add(deliverGoods.getGoodsTotalQuantity()));
			} else {
				order1.setDeliveredWeight(deliverGoods.getGoodsTotalQuantity());
			}
			orderService.updateAllProperties(order1);
		} else {
			BigDecimal subtract = order.getDeliveredWeight()
					.subtract(old.getGoodsTotalQuantity());
			subtract = subtract.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO
					: subtract;
			order.setDeliveredWeight(
					subtract.add(deliverGoods.getGoodsTotalQuantity()));
			orderService.updateAllProperties(order);
		}
		return Optional.of(this.updateAllProperties(deliverGoods));
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void delete(String id) {
		DeliverGoods deliverGoods = this.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30001001));
		// 删除发货单时，减去订单已发货数量并修改发货状态
		Order order = orderService.findOne(deliverGoods.getOrderId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30009003));
		order.setDeliveredWeight(order.getDeliveredWeight()
				.subtract(deliverGoods.getGoodsTotalQuantity()));
		if (DeliverGoodsDef.DeliverWay.SELF_PICKUP
				.match(deliverGoods.getDelivery())) {
			order.setActualDeliveredWeight(order.getActualDeliveredWeight()
					.subtract(deliverGoods.getActualGoodsTotalQuantity()));
		}
		if (order.getActualDeliveredWeight()
				.compareTo(order.getGoodsTotalQuantity()) < 0) {
			order.setDeliveryStatus(
					OrderDef.BusinessStatus.IN_PROGRESS.getCode());
		}
		if (order.getActualDeliveredWeight().compareTo(BigDecimal.ZERO) <= 0) {
			order.setDeliveredWeight(BigDecimal.ZERO);
			order.setActualDeliveredWeight(BigDecimal.ZERO);
			order.setDeliveryStatus(
					OrderDef.BusinessStatus.NOT_STARTED.getCode());
		}
		orderService.updateAllProperties(order);
		List<TransportOrderShip> byDeliverGoodsIds = transportOrderShipService
				.findByDeliverGoodsIds(List.of(id));
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(List.of(id));
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(List.of(id));
		if (CollectionUtils.isNotEmpty(byDeliverGoodsIds)) {
			transportOrderShipService.batchDelete(byDeliverGoodsIds.stream()
					.map(TransportOrderShip::getId).toList());
		}
		if (CollectionUtils.isNotEmpty(transportOrderVehicles)) {
			transportOrderVehicleService.batchDelete(transportOrderVehicles
					.stream().map(TransportOrderVehicle::getId).toList());
		}
		if (CollectionUtils.isNotEmpty(transportOrderRailways)) {
			transportOrderRailwayService.batchDelete(transportOrderRailways
					.stream().map(TransportOrderRailway::getId).toList());
		}
		super.delete(id);
	}

	@Override
	public void deleteBuy(String id) {
		DeliverGoods deliverGoods = this.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30001001));
		// 删除发货单时，减去订单已发货数量并修改发货状态
		Order order = orderService.findOne(deliverGoods.getOrderId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30009003));
		order.setDeliveredWeight(order.getDeliveredWeight()
				.subtract(deliverGoods.getGoodsTotalQuantity()));
		orderService.update(order);
		super.delete(id);
	}

	@Override
	public Optional<DeliverGoods> invalidOffLine(DeliverGoods deliverGoods,
			Integer origin) {
		// 作废合同
		deliverGoods
				.setStatus(DeliverGoodsDef.Status.DELIVER_INVALID.getCode());
		deliverGoods.setInvalidTime(LocalDateTime.now());
		this.cancel(deliverGoods);
		if (CommonDef.AccountSource.INNER.match(origin)) {
			this.sendNotice(deliverGoods,
					wxSubscriptionProperties.getDeliverNullifyCode(),
					MessageFormat.format(
							UserMessageConstants.DELIVER_GOODS_INVALID_TEMPLATE,
							deliverGoods.getId()),
					ContractDef.ContractType.SALES.getCode());
		} else {
			SpringUtil.getBean(DeliverGoodsService.class).notice(deliverGoods,
					4);
		}
		return Optional.of(super.updateAllProperties(deliverGoods));
	}

	/**
	 * @description: 撤回操作
	 * @author: 彭湃
	 * @date: 2025/1/13 13:56
	 * @param: [deliverGoods]
	 * @return: com.zhihaoscm.domain.bean.entity.DeliverGoods
	 **/
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<DeliverGoods> revert(DeliverGoods deliverGoods,
			Boolean isRevoke, Integer origin) {
		deliverGoods.setStatus(DeliverGoodsDef.Status.DELIVER_CANCEL.getCode());
		// 撤回发货单时，将所有子单状态设为已取消
		this.cancel(deliverGoods);

		if (CommonDef.AccountSource.INNER.match(origin)) {
			this.sendNotice(deliverGoods,
					wxSubscriptionProperties.getWithdrawGoodsCode(),
					MessageFormat.format(
							UserMessageConstants.DELIVER_GOODS_WITHDRAW_TEMPLATE,
							deliverGoods.getId()),
					ContractDef.ContractType.SALES.getCode());
		} else {
			SpringUtil.getBean(DeliverGoodsService.class).notice(deliverGoods,
					3);
		}

		return Optional.of(this.updateAllProperties(deliverGoods));
	}

	/**
	 * 撤回发货单时，将所有子单状态设为已取消
	 * 
	 * @param deliverGoods
	 */
	private void cancel(DeliverGoods deliverGoods) {
		// 发货取消：发货单内全部运单状态改完已取消
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(List.of(deliverGoods.getId()));
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(List.of(deliverGoods.getId()));
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(List.of(deliverGoods.getId()));

		if (CollectionUtils.isNotEmpty(transportOrderShips)) {
			// 记录取消之前的状态
			transportOrderShips.forEach(
					ship -> ship.setBeforeCancelState(ship.getState()));
			// 记录取消之前的第三方状态
			transportOrderShips.forEach(ship -> ship
					.setBeforeCancelThirdState(ship.getThirdState()));

			transportOrderShips.forEach(ship -> ship
					.setState(TransportOrderShipDef.State.CANCELED.getCode()));
			transportOrderShipService.batchUpdate(transportOrderShips);
		}

		if (CollectionUtils.isNotEmpty(transportOrderVehicles)) {
			transportOrderVehicles.forEach(vehicle -> {
				vehicle.setBeforeCancelState(vehicle.getState());
				vehicle.setState(
						TransportOrderVehicleDef.State.CANCELED.getCode());
				vehicle.setCancelTime(LocalDateTime.now());
			});
			transportOrderVehicleService.batchUpdate(transportOrderVehicles);
		}

		if (CollectionUtils.isNotEmpty(transportOrderRailways)) {
			transportOrderRailways.forEach(railway -> railway.setState(
					TransportOrderRailwayDef.State.CANCELED.getCode()));
			transportOrderRailwayService.batchUpdate(transportOrderRailways);
		}

		orderService.findOne(deliverGoods.getOrderId()).ifPresent(e -> {
			if (Objects.nonNull(e.getActualDeliveredWeight()) && Objects
					.nonNull(deliverGoods.getActualGoodsTotalQuantity())) {
				e.setActualDeliveredWeight(e.getActualDeliveredWeight()
						.subtract(deliverGoods.getActualGoodsTotalQuantity()));
				if (e.getActualDeliveredWeight()
						.compareTo(e.getDeliveredWeight()) < 0) {
					e.setDeliveryStatus(
							OrderDef.BusinessStatus.IN_PROGRESS.getCode());
				}
				if (e.getActualDeliveredWeight()
						.compareTo(BigDecimal.ZERO) <= 0) {
					e.setActualDeliveredWeight(BigDecimal.ZERO);
					e.setDeliveryStatus(
							OrderDef.BusinessStatus.NOT_STARTED.getCode());
				}
				orderService.updateAllProperties(e);
			}
		});
	}

	/**
	 * @description: 确认送达
	 * @author: 彭湃
	 * @date: 2025/1/13 13:57
	 * @param: [deliverGoods]
	 * @return: com.zhihaoscm.domain.bean.entity.DeliverGoods
	 **/
	@Override
	public Optional<DeliverGoods> confirm(DeliverGoods deliverGoods,
			Integer origin) {
		deliverGoods
				.setStatus(DeliverGoodsDef.Status.DELIVER_COMPLETE.getCode());
		if (CommonDef.AccountSource.INNER.match(origin)) {
			this.sendNotice(deliverGoods,
					wxSubscriptionProperties.getConfirmGoodsCode(),
					MessageFormat.format(
							UserMessageConstants.DELIVER_GOODS_FINISHED_TEMPLATE,
							deliverGoods.getId()),
					ContractDef.ContractType.SALES.getCode());
		} else {
			SpringUtil.getBean(DeliverGoodsService.class).notice(deliverGoods,
					2);
		}
		return Optional.of(this.updateAllProperties(deliverGoods));
	}

	private List<DeliverGoodsVo> packVos(List<DeliverGoods> list,
			List<Contract> contracts) {
		if (CollectionUtils.isEmpty(contracts)) {
			contracts = contractService.findByIds(list.stream()
					.map(DeliverGoods::getContractId).distinct().toList());
		}
		List<String> receiptId = list.stream()
				.map(DeliverGoods::getSignReceiptId).toList();
		List<SignReceipt> signReceipts = signReceiptService
				.findByIds(receiptId);
		Map<String, SignReceipt> signReceiptMap = Map.of();
		if (CollectionUtils.isNotEmpty(signReceipts)) {
			signReceiptMap = signReceipts.stream()
					.collect(Collectors.toMap(SignReceipt::getId, e -> e));
		}

		Map<String, Contract> contractMap = contracts.stream()
				.collect(Collectors.toMap(Contract::getId, i -> i));
		List<String> orderIds = list.stream().map(DeliverGoods::getOrderId)
				.distinct().toList();
		List<String> projectIds = list.stream().map(DeliverGoods::getProjectId)
				.distinct().toList();
		List<Order> orderList = orderService.findByIds(orderIds);
		List<Project> projectList = projectService.findByIds(projectIds);
		Map<String, Order> collect = orderList.stream()
				.collect(Collectors.toMap(Order::getId, i -> i));
		Map<String, Project> projectMap = projectList.stream()
				.collect(Collectors.toMap(Project::getId, i -> i));

		List<String> deliverGoodsIds = list.stream().map(DeliverGoods::getId)
				.distinct().toList();

		// 船运单信息
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		Map<String, List<TransportOrderShip>> transportOrderShipMap = transportOrderShips
				.stream()
				.collect(Collectors.groupingBy(TransportOrderShip::getGoodsId));

		// 汽运单信息
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		Map<String, List<TransportOrderVehicle>> transportOrderVehicleMap = transportOrderVehicles
				.stream().collect(Collectors
						.groupingBy(TransportOrderVehicle::getDeliverGoodsId));

		// 铁路单信息
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		Map<String, List<TransportOrderRailway>> transportOrderRailwayMap = transportOrderRailways
				.stream().collect(Collectors
						.groupingBy(TransportOrderRailway::getDeliverGoodsId));

		Map<String, SignReceipt> finalSignReceiptMap = signReceiptMap;
		return list.stream().map(e -> {
			DeliverGoodsVo deliverGoodsVo = new DeliverGoodsVo();
			Order order = collect.get(e.getOrderId());
			deliverGoodsVo.setDeliverGoods(e);
			deliverGoodsVo.setContract(contractMap.get(e.getContractId()));
			if (Objects.nonNull(e.getSignReceiptId())) {
				SignReceipt signReceipt = finalSignReceiptMap
						.get(e.getSignReceiptId());
				deliverGoodsVo.setSignReceipt(signReceipt);
				if (Objects.nonNull(signReceipt)
						&& Objects.equals(signReceipt.getStatus(),
								SignReceiptDef.Status.FINISHED.getCode())) {
					deliverGoodsVo.setSignStatus(
							DeliverGoodsDef.SignStatus.SIGN_COMPLETE.getCode());
				} else {
					deliverGoodsVo.setSignStatus(
							DeliverGoodsDef.SignStatus.SIGNING.getCode());
				}
			} else {
				deliverGoodsVo.setSignStatus(
						DeliverGoodsDef.SignStatus.NOT_SIGN.getCode());
			}
			deliverGoodsVo.setProject(projectMap.get(e.getProjectId()));
			deliverGoodsVo.setOrder(order);
			// 发货方式为船运时设置船运单信息
			if (DeliverGoodsDef.DeliverWay.SHIPPING.match(e.getDelivery())) {
				deliverGoodsVo.setTransportOrderShips(
						transportOrderShipMap.get(e.getId()));
			}
			// 发货方式为汽运时设置汽运单信息
			if (DeliverGoodsDef.DeliverWay.CAR.match(e.getDelivery())) {
				deliverGoodsVo.setTransportOrderVehicles(
						transportOrderVehicleMap.get(e.getId()));
			}
			// 发货方式为铁路时设置铁路单信息
			if (DeliverGoodsDef.DeliverWay.TRAIN.match(e.getDelivery())) {
				deliverGoodsVo.setTransportOrderRailways(
						transportOrderRailwayMap.get(e.getId()));
			}

			return deliverGoodsVo;
		}).toList();
	}

	private List<DeliverGoodsVo> packDeliverGoodsVos(List<DeliverGoods> list) {
		// 签收信息
		List<String> signReceiptIds = list.stream()
				.map(DeliverGoods::getSignReceiptId).distinct().toList();
		List<SignReceipt> signReceipts = signReceiptService
				.findByIds(signReceiptIds);
		Map<String, SignReceipt> signReceiptMap = signReceipts.stream()
				.collect(Collectors.toMap(SignReceipt::getId, i -> i));
		// 订单信息
		List<String> orderIds = list.stream().map(DeliverGoods::getOrderId)
				.distinct().toList();
		List<Order> orders = orderService.findByIds(orderIds);
		Map<String, Order> orderMap = orders.stream()
				.collect(Collectors.toMap(Order::getId, i -> i));
		// 发货单id
		List<String> deliverGoodsIds = list.stream().map(DeliverGoods::getId)
				.distinct().toList();
		// 船运单信息
		List<TransportOrderShip> transportOrderShips = transportOrderShipService
				.findByDeliverGoodsIds(deliverGoodsIds);
		Map<String, List<TransportOrderShip>> transportOrderShipMap = transportOrderShips
				.stream()
				.collect(Collectors.groupingBy(TransportOrderShip::getGoodsId));
		// 汽运单信息
		List<TransportOrderVehicle> transportOrderVehicles = transportOrderVehicleService
				.findByDeliverGoodsIds(deliverGoodsIds);
		Map<String, List<TransportOrderVehicle>> transportOrderVehicleMap = transportOrderVehicles
				.stream().collect(Collectors
						.groupingBy(TransportOrderVehicle::getDeliverGoodsId));
		// 铁路单信息
		List<TransportOrderRailway> transportOrderRailways = transportOrderRailwayService
				.findByDeliverGoodsIds(deliverGoodsIds);
		Map<String, List<TransportOrderRailway>> transportOrderRailwayMap = transportOrderRailways
				.stream().collect(Collectors
						.groupingBy(TransportOrderRailway::getDeliverGoodsId));
		return list.stream().map(e -> {
			DeliverGoodsVo deliverGoodsVo = new DeliverGoodsVo();
			deliverGoodsVo.setDeliverGoods(e);
			deliverGoodsVo
					.setSignReceipt(signReceiptMap.get(e.getSignReceiptId()));
			// 设置订单信息
			deliverGoodsVo.setOrder(orderMap.get(e.getOrderId()));
			// 发货方式为船运时设置船运单信息
			if (DeliverGoodsDef.DeliverWay.SHIPPING.match(e.getDelivery())) {
				deliverGoodsVo.setTransportOrderShips(
						transportOrderShipMap.get(e.getId()));
			}
			// 发货方式为汽运时设置汽运单信息
			if (DeliverGoodsDef.DeliverWay.CAR.match(e.getDelivery())) {
				deliverGoodsVo.setTransportOrderVehicles(
						transportOrderVehicleMap.get(e.getId()));
			}
			// 发货方式为铁路时设置铁路单信息
			if (DeliverGoodsDef.DeliverWay.TRAIN.match(e.getDelivery())) {
				deliverGoodsVo.setTransportOrderRailways(
						transportOrderRailwayMap.get(e.getId()));
			}
			return deliverGoodsVo;
		}).toList();
	}

	/**
	 * @description: 将JSON字符串转换为Map列表，获取缺少的字段，然后将货物信息填充Map列表后转换回JSON字符串
	 * @author: 彭湃
	 * @date: 2025/1/13 14:59
	 * @param: [data,
	 *             arrayGoodsInfo]
	 * @return: java.lang.String
	 **/
	@Override
	public String convertJson(String data, List<GoodsInfo> arrayGoodsInfo) {
		try {
			// 将JSON字符串转换为Map列表，获取缺少的字段
			Type mapListType = new TypeToken<List<Map<String, Object>>>() {
			}.getType();
			Gson gson = new GsonBuilder().registerTypeAdapter(
					LocalDateTime.class, new LocalDateTimeAdapter()).create();
			List<Map<String, Object>> jsonMapList = gson.fromJson(data,
					mapListType);

			List<Map<String, String>> newMapList = new ArrayList<>();
			for (int i = 0; i < arrayGoodsInfo.size(); i++) {
				GoodsInfo myClass = arrayGoodsInfo.get(i);
				Map<String, String> newMap = BeanUtils.describe(myClass);

				Map<String, Object> jsonMap = jsonMapList.get(i);
				for (Map.Entry<String, Object> entry : jsonMap.entrySet()) {
					if (!newMap.containsKey(entry.getKey())) {
						newMap.put(entry.getKey(), (String) entry.getValue());
					}
				}
				newMapList.add(newMap);
			}
			// 将Map列表转换回JSON字符串
			return gson.toJson(newMapList);
		} catch (Exception e) {
			log.error("convertJson error", e);
		}
		return null;
	}

	@Override
	public void downloadPdf(HttpServletResponse response, String id)
			throws IOException {
		setExportResponseFields(response, id);
		GeneratPdfDto pdf = generatPdfDto(id);
		PdfUtils.getPdf(TransactionDef.PdfType.TRANSPORT,
				response.getOutputStream(), pdf);
	}

	@Override
	public Optional<DeliverGoodsCountVo> staticsAdminDeliverGoods(
			boolean isManage) {
		DeliverGoodsCountVo deliverGoodsCountVo = new DeliverGoodsCountVo();
		deliverGoodsCountVo.setToBeCompleted(0L);
		List<String> projectIds = projectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		LambdaQueryWrapper<DeliverGoods> queryWrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(DeliverGoods::getStatus,
						DeliverGoodsDef.Status.DELIVERING.getCode());
				queryWrapper.in(DeliverGoods::getProjectId, projectIds);
				deliverGoodsCountVo
						.setToBeCompleted(repository.selectCount(queryWrapper));
			}
		}

		return Optional.of(deliverGoodsCountVo);
	}

	@Override
	public Optional<DeliverGoodsCountVo> staticsCustomerDeliverGoods(
			boolean isPermission) {
		DeliverGoodsCountVo deliverGoodsCountVo = new DeliverGoodsCountVo();
		deliverGoodsCountVo.setToBeCompleted(0L);
		LambdaQueryWrapper<DeliverGoods> queryWrapper = Wrappers
				.lambdaQuery(DeliverGoods.class);
		if (isPermission) {
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(DeliverGoods::getStatus,
					DeliverGoodsDef.Status.DELIVERING.getCode());
			queryWrapper.eq(DeliverGoods::getPurchaserId, CustomerContextHolder
					.getCustomerLoginVo().getProxyAccount().getId());
			deliverGoodsCountVo
					.setToBeCompleted(repository.selectCount(queryWrapper));
		}

		return Optional.of(deliverGoodsCountVo);
	}

	@Override
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = "{{#success}}", type = LogDef.DELIVER_GOODS_INFO, bizNo = "{{#resource.getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#resource.getId()}}"),
			@LogRecord.KeyValuePair(key = "#projectId#", value = "{{#code}}") }, messageType = LogDef.MESSAGE_TYPE_ORDER, permission = LogDef.PROJECT_DEAL)
	public void notice(DeliverGoods resource, Integer type) {
		Project project = projectService.findOne(resource.getProjectId())
				.orElse(new Project());
		LogRecordContext.putVariable("code", project.getName());
		switch (type) {
			case 1 -> LogRecordContext.putVariable("success",
					LogDef.DELIVER_GOODS_CREATE);
			case 2 -> LogRecordContext.putVariable("success",
					LogDef.DELIVER_GOODS_CONFIRMED);
			case 3 -> LogRecordContext.putVariable("success",
					LogDef.DELIVER_GOODS_CANCEL);
			case 4 -> LogRecordContext.putVariable("success",
					LogDef.DELIVER_GOODS_INVALID);
			default -> {
			}
		}
		log.info("提货发送通知:{}", resource.getId());
	}

	/**
	 * 设置导出响应头
	 *
	 * @param response
	 */
	private void setExportResponseFields(HttpServletResponse response,
			String id) {
		response.setContentType("application/pdf");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode(
				String.format("发货单_%s_%s",
						DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
								.format(LocalDateTime.now()),
						id),
				StandardCharsets.UTF_8).replaceAll("\\+", "%20");
		response.setHeader("Content-disposition",
				"attachment;filename*=utf-8''" + fileName + ".pdf");
	}

	/**
	 * 生成pdf转化类方法
	 *
	 * @param id
	 * @return
	 */
	private GeneratPdfDto generatPdfDto(String id) {
		DeliverGoods deliverGoods = this.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30009003));

		OrderVo or = orderService.findVoById(deliverGoods.getOrderId());
		Order order = or.getOrder();
		Contract contract = or.getContract();
		GeneratPdfDto pdf = new GeneratPdfDto();
		pdf.setBillId(id);
		if (ContractDef.ContractType.PURCHASE
				.match(contract.getContractType())) {
			pdf.setBuyerName(contract.getSupplierChainEnterprise().getName());
			pdf.setSellerName(
					contract.getUpstreamSuppliersEnterprise().getName());
		} else {
			pdf.setBuyerName(
					contract.getDownstreamPurchasersEnterprise().getName());
			pdf.setSellerName(contract.getSupplierChainEnterprise().getName());
		}
		pdf.setContractName(contract.getName());
		pdf.setOrderId(deliverGoods.getOrderId());
		DateTimeFormatter formatter = DateTimeFormatter
				.ofPattern("【yyyy】年【MM】月【dd】日");
		pdf.setContractSignDate(formatter.format(contract.getSignDate()));
		pdf.setContractId(contract.getId());
		if (Objects.nonNull(deliverGoods.getDelivery())) {
			pdf.setDeliverWay(DeliverGoodsDef.DeliverWay
					.from(deliverGoods.getDelivery()));
		}
		pdf.setOrderRemark(order.getRemark());
		pdf.setGoodsInfoList(deliverGoods.getGoodsInfo());
		if (Objects.nonNull(pdf.getGoodsInfoList())) {
			for (Object o : JSON.parseArray(pdf.getGoodsInfoList())) {
				JSONObject object = (JSONObject) o;
				object.put("goodsName", order.getGoodsName());
				object.put("unit", order.getUnit());
			}
		}
		return pdf;
	}

	/**
	 * 发送短信
	 *
	 * @param deliverGoods
	 * @param templateCode
	 * @param title
	 */
	private void sendNotice(DeliverGoods deliverGoods, String templateCode,
			String title, Integer type) {
		Customer customer = null;
		if (ContractDef.ContractType.SALES.match(type)) {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(deliverGoods.getPurchaserBusinessId())
					.orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		} else {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(deliverGoods.getSellerBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		}

		if (Objects.nonNull(customer)) {
			if (StringUtils.isNotBlank(templateCode)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(templateCode)
						.params(Map.of("notice", deliverGoods.getId()))
						.mobile(customer.getMobile()).build());
			}

			if (StringUtils.isNotBlank(title)) {
				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.ORDER.getCode())
						.title(title)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.DELIVER_GOODS_DETAIL_PAGE)
						.detailId(String.valueOf(deliverGoods.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
		}
	}
}
