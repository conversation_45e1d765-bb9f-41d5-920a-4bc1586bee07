package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.QuotaChangeCountVo;
import com.zhihaoscm.domain.bean.vo.QuotaChangeVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.ThreadPoolUtil;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.QuotaChangeMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 额度变更 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@Service
public class QuotaChangeServiceImpl
		extends MpStringIdBaseServiceImpl<QuotaChange, QuotaChangeMapper>
		implements QuotaChangeService {

	@Autowired
	private ProjectService projectService;
	@Autowired
	private ContractService contractService;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	public QuotaChangeServiceImpl(QuotaChangeMapper repository) {
		super(repository);
	}

	@Override
	public Page<QuotaChangeVo> paging(Integer page, Integer size,
			String keyword, String purchaserName, String sellName,
			List<Integer> states, Integer changeType, Integer type,
			Integer costType, String sortKey, String sortOrder, Boolean hasAll,
			Long userId) {
		LambdaQueryWrapper<QuotaChange> wrapper = Wrappers
				.lambdaQuery(QuotaChange.class);
		this.filterDeleted(wrapper);
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (!hasAll) {
			// 处理人是自己在的
			List<String> projectIdList = projectService.findByUserId(userId,
					null);
			if (CollectionUtils.isNotEmpty(projectIdList)) {
				wrapper.in(QuotaChange::getProjectId, projectIdList);
			} else {
				return Page.of(page, size, 0);
			}
		}
		if (StringUtils.isNotBlank(keyword)) {
			List<String> projectIds = projectService.findByNameLike(keyword)
					.stream().map(Project::getId).distinct().toList();

			wrapper.and(StringUtils.isNotBlank(keyword),
					x -> x.like(QuotaChange::getId, keyword).or().in(
							CollectionUtils.isNotEmpty(projectIds),
							QuotaChange::getProjectId, projectIds));
		}

		if (StringUtils.isNotBlank(purchaserName)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
					purchaserName));
		}
		if (StringUtils.isNotBlank(sellName)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(seller_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
					sellName));
		}
		wrapper.eq(Objects.nonNull(changeType), QuotaChange::getChangeType,
				changeType);
		wrapper.eq(Objects.nonNull(type), QuotaChange::getType, type);
		wrapper.eq(Objects.nonNull(costType), QuotaChange::getCostType,
				costType);

		// 状态
		if (CollectionUtils.isEmpty(states)) {
			wrapper.and(
					x -> x.eq(QuotaChange::getInitiator,
							QuotaChangeDef.Initiator.ADMIN.getCode())
							.or(y -> y
									.eq(QuotaChange::getInitiator,
											QuotaChangeDef.Initiator.CUSTOM
													.getCode())
									.ne(QuotaChange::getState,
											QuotaChangeDef.State.REJECTED
													.getCode())));
		} else {
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {

					switch (QuotaChangeDef.QueryState.from(state)) {
						// 确认中
						case CONFIRMING -> x.or(y -> y
								.eq(QuotaChange::getInitiator,
										QuotaChangeDef.Initiator.ADMIN
												.getCode())
								.eq(QuotaChange::getState,
										QuotaChangeDef.State.CONFIRMING
												.getCode()));
						// 待确认
						case TO_BE_CONFIRMED -> x.or(y -> y
								.eq(QuotaChange::getInitiator,
										QuotaChangeDef.Initiator.CUSTOM
												.getCode())
								.eq(QuotaChange::getState,
										QuotaChangeDef.State.CONFIRMING
												.getCode()));
						// 已驳回
						case REJECTED -> x.or(y -> y
								.eq(QuotaChange::getInitiator,
										QuotaChangeDef.Initiator.ADMIN
												.getCode())
								.eq(QuotaChange::getState,
										QuotaChangeDef.State.REJECTED
												.getCode()));
						// 已完成
						case COMPLETED -> x.or(y -> y.eq(QuotaChange::getState,
								QuotaChangeDef.State.COMPLETED.getCode()));
					}
				}
			});
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + " ,id DESC");
		} else {
			String orderString = "CASE "
					+ "WHEN state = 1 AND initiator = 2 THEN 1 "
					+ "WHEN state = 2 AND initiator = 1 THEN 2 "
					+ "WHEN state = 1 AND initiator = 1 THEN 3 " + "ELSE 4 END";
			wrapper.last("ORDER BY " + orderString
					+ " ASC, updated_time DESC, id DESC");
		}
		Page<QuotaChange> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Page<QuotaChangeVo> customPaging(Integer page, Integer size,
			String keyword, String purchaserName, List<Integer> states,
			Integer changeType, Integer costType, String sortKey,
			String sortOrder, Long customerId, String sellerName,
			Integer type) {
		LambdaQueryWrapper<QuotaChange> wrapper = Wrappers
				.lambdaQuery(QuotaChange.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(type), QuotaChange::getType, type);

		wrapper.and(StringUtils.isNotBlank(keyword),
				x -> x.like(QuotaChange::getId, keyword).or()
						.like(QuotaChange::getContractName, keyword));

		if (StringUtils.isNotBlank(purchaserName)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
					purchaserName));
		}
		if (StringUtils.isNotBlank(sellerName)) {
			wrapper.and(i -> i.apply(
					"(JSON_EXTRACT(seller_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
					sellerName));
		}
		wrapper.eq(Objects.nonNull(changeType), QuotaChange::getChangeType,
				changeType);
		// 客户端只要查询销售变更的额度变更
		// wrapper.eq(QuotaChange::getType, QuotaChangeDef.Type.SELL.getCode());
		wrapper.eq(Objects.nonNull(costType), QuotaChange::getCostType,
				costType);
		// 当类型为采购变更时，判断采购方是当前用户，当类型为销售变更时，判断销售方是当前用户
		wrapper.and(
				x -> x.and(wrapper1 -> wrapper1
						.eq(QuotaChange::getType,
								QuotaChangeDef.Type.SELL.getCode())
						.eq(QuotaChange::getPurchaserId, customerId)).or(
								wrapper2 -> wrapper2
										.eq(QuotaChange::getType,
												QuotaChangeDef.Type.BUY
														.getCode())
										.eq(QuotaChange::getSellerId,
												customerId)));

		// 状态
		if (CollectionUtils.isEmpty(states)) {
			wrapper.and(
					x -> x.eq(QuotaChange::getInitiator,
							QuotaChangeDef.Initiator.CUSTOM.getCode())
							.or(y -> y
									.eq(QuotaChange::getInitiator,
											QuotaChangeDef.Initiator.ADMIN
													.getCode())
									.ne(QuotaChange::getState,
											QuotaChangeDef.State.REJECTED
													.getCode())));
		} else {
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					switch (QuotaChangeDef.QueryState.from(state)) {
						// 确认中
						case CONFIRMING -> x.or(y -> y
								.eq(QuotaChange::getInitiator,
										QuotaChangeDef.Initiator.CUSTOM
												.getCode())
								.eq(QuotaChange::getState,
										QuotaChangeDef.State.CONFIRMING
												.getCode()));
						// 待确认
						case TO_BE_CONFIRMED -> x.or(y -> y
								.eq(QuotaChange::getInitiator,
										QuotaChangeDef.Initiator.ADMIN
												.getCode())
								.eq(QuotaChange::getState,
										QuotaChangeDef.State.CONFIRMING
												.getCode()));
						// 已驳回
						case REJECTED -> x.or(y -> y
								.eq(QuotaChange::getInitiator,
										QuotaChangeDef.Initiator.CUSTOM
												.getCode())
								.eq(QuotaChange::getState,
										QuotaChangeDef.State.REJECTED
												.getCode()));
						// 已完成
						case COMPLETED -> x.or(y -> y.eq(QuotaChange::getState,
								QuotaChangeDef.State.COMPLETED.getCode()));
					}
				}
			});
		}

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + ", id DESC");
		} else {
			String orderString = "CASE "
					+ "WHEN state = 1 AND initiator = 1 THEN 1 "
					+ "WHEN state = 2 AND initiator = 2 THEN 2 "
					+ "WHEN state = 1 AND initiator = 2 THEN 3 " + "ELSE 4 END";
			wrapper.last("ORDER BY " + orderString
					+ " ASC, updated_time DESC, id DESC");
		}
		Page<QuotaChange> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Optional<QuotaChangeVo> findVoById(String id) {
		return super.findOne(id).map(this::packVo);
	}

	@Override
	public List<QuotaChange> findUnfinished(String projectId) {
		LambdaQueryWrapper<QuotaChange> queryWrapper = Wrappers
				.lambdaQuery(QuotaChange.class);
		queryWrapper.eq(QuotaChange::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(QuotaChange::getProjectId, projectId);
		queryWrapper.ne(QuotaChange::getState,
				QuotaChangeDef.State.COMPLETED.getCode());
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<QuotaChange> findByContractId(String contractId) {
		LambdaQueryWrapper<QuotaChange> wrapper = Wrappers
				.lambdaQuery(QuotaChange.class);
		this.filterDeleted(wrapper);
		wrapper.eq(QuotaChange::getContractId, contractId);
		return repository.selectList(wrapper);
	}

	@Override
	@FileId
	@Transactional(rollbackFor = Exception.class)
	public QuotaChange create(QuotaChange resource) {
		Project project = projectService.findOne(resource.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		// 按规则设置签收单id
		resource.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(), RedisKeys.Cache.QUOTA_CHANGE,
				1 + AutoCodeDef.BusinessRuleCode.QUOTA_CHANGE_SUFFIX.getCode(),
				4, AutoCodeDef.DATE_TYPE.yy));
		if (CommonDef.AccountSource.INNER.match(resource.getInitiator())) {
			if (QuotaChangeDef.Type.SELL.match(resource.getType())) {
				this.sendNotice(resource,
						wxSubscriptionProperties.getUnConfirmQuotaCode(),
						MessageFormat.format(
								UserMessageConstants.QUOTA_CHANGE_UNCONFIRMED_TEMPLATE,
								resource.getId()),
						QuotaChangeDef.Type.SELL.getCode());
			} else {
				this.sendNotice(resource,
						wxSubscriptionProperties.getUnConfirmQuotaCode(),
						MessageFormat.format(
								UserMessageConstants.QUOTA_CHANGE_UNCONFIRMED_TEMPLATE,
								resource.getId()),
						QuotaChangeDef.Type.BUY.getCode());
			}
		} else {
			ThreadPoolUtil.scheduleTask(
					() -> SpringUtil.getBean(QuotaChangeService.class)
							.notice(resource, 1),
					3, TimeUnit.SECONDS,
					ThreadPoolUtil.getUserScheduledExecutor());
		}
		// 采购额度变更需要更新合同的货款金额或履约保证金，此方法去调用，因为原来采购的类型，默认了就是完成状态
		// if (QuotaChangeDef.Type.BUY.match(resource.getType())) {
		// this.handleAmount(resource, Boolean.FALSE, Boolean.FALSE);
		// }
		// 保存额度变更
		super.create(resource);
		// 判断，如果是供应链新增且状态为 完成状态，调用确认接口
		if (Objects.equals(resource.getInitiator(),
				QuotaChangeDef.Initiator.ADMIN.getCode())
				&& Objects.equals(resource.getState(),
						QuotaChangeDef.State.COMPLETED.getCode())) {
			this.confirm(resource);
		}
		return resource;
	}

	@Override
	@FileId(type = 2)
	@Transactional(rollbackFor = Exception.class)
	public QuotaChange updateAllProperties(QuotaChange resource) {
		QuotaChange quotaChange = this.findOne(resource.getId()).orElse(null);
		if (Objects.nonNull(quotaChange)) {
			if (QuotaChangeDef.State.REJECTED.match(quotaChange.getState())) {
				if (QuotaChangeDef.State.CONFIRMING
						.match(resource.getState())) {
					if (QuotaChangeDef.Type.SELL.match(resource.getType())) {
						this.sendNotice(resource,
								wxSubscriptionProperties
										.getUnConfirmQuotaCode(),
								MessageFormat.format(
										UserMessageConstants.QUOTA_CHANGE_UNCONFIRMED_TEMPLATE,
										resource.getId()),
								QuotaChangeDef.Type.SELL.getCode());
					} else {
						this.sendNotice(resource,
								wxSubscriptionProperties
										.getUnConfirmQuotaCode(),
								MessageFormat.format(
										UserMessageConstants.QUOTA_CHANGE_UNCONFIRMED_TEMPLATE,
										resource.getId()),
								QuotaChangeDef.Type.BUY.getCode());
					}
				}
			}
			// 采购额度变更需要更新合同的货款金额或履约保证金
			if (QuotaChangeDef.Type.BUY.match(resource.getType())) {
				this.handleAmount(resource, Boolean.FALSE, Boolean.TRUE);
			}
		}
		return super.updateAllProperties(resource);
	}

	@Override
	@FileId(type = 3)
	@Transactional(rollbackFor = Exception.class)
	public void delete(String id) {
		QuotaChange quotaChange = super.findOne(id).orElse(null);
		if (Objects.nonNull(quotaChange)) {
			// 采购额度变更需要更新合同的货款金额或履约保证金
			if (QuotaChangeDef.Type.BUY.match(quotaChange.getType())) {
				this.handleAmount(quotaChange, Boolean.TRUE, Boolean.FALSE);
			}
		}
		super.delete(id);
	}

	@Override
	public void reject(QuotaChange quotaChange) {
		quotaChange.setState(QuotaChangeDef.State.REJECTED.getCode());
		if (CommonDef.AccountSource.CUSTOM.match(quotaChange.getInitiator())) {
			if (QuotaChangeDef.Type.SELL.match(quotaChange.getType())) {
				this.sendNotice(quotaChange,
						wxSubscriptionProperties.getDismissQuotaCode(),
						MessageFormat.format(
								UserMessageConstants.QUOTA_CHANGE_DISMISS_TEMPLATE,
								quotaChange.getId()),
						QuotaChangeDef.Type.SELL.getCode());
			} else {
				this.sendNotice(quotaChange,
						wxSubscriptionProperties.getDismissQuotaCode(),
						MessageFormat.format(
								UserMessageConstants.QUOTA_CHANGE_DISMISS_TEMPLATE,
								quotaChange.getId()),
						QuotaChangeDef.Type.BUY.getCode());
			}
		} else {
			SpringUtil.getBean(QuotaChangeService.class).notice(quotaChange, 3);
		}
		super.updateAllProperties(quotaChange);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void confirm(QuotaChange quotaChange) {
		quotaChange.setState(QuotaChangeDef.State.COMPLETED.getCode());
		super.updateAllProperties(quotaChange);
		// 处理额度变更影响的金额
		this.handleAmount(quotaChange, Boolean.FALSE, Boolean.FALSE);
		if (CommonDef.AccountSource.CUSTOM.match(quotaChange.getInitiator())) {
			if (QuotaChangeDef.Type.SELL.match(quotaChange.getType())) {
				this.sendNotice(quotaChange,
						wxSubscriptionProperties.getConfirmQuotaCode(),
						MessageFormat.format(
								UserMessageConstants.QUOTA_CHANGE_CONFIRMED_TEMPLATE,
								quotaChange.getId()),
						QuotaChangeDef.Type.SELL.getCode());
			} else {
				this.sendNotice(quotaChange,
						wxSubscriptionProperties.getConfirmQuotaCode(),
						MessageFormat.format(
								UserMessageConstants.QUOTA_CHANGE_CONFIRMED_TEMPLATE,
								quotaChange.getId()),
						QuotaChangeDef.Type.BUY.getCode());
			}
		} else {
			SpringUtil.getBean(QuotaChangeService.class).notice(quotaChange, 2);
		}

	}

	// 处理额度变更影响的金额变化
	private void handleAmount(QuotaChange quotaChange, Boolean isDelete,
			Boolean isUpdate) {
		Contract contract = contractService.findOne(quotaChange.getContractId())
				.orElse(null);
		// 合同中的履约保证金和贷款数据
		BigDecimal loanAmount = BigDecimal.ZERO;

		if (Objects.nonNull(contract)) {
			loanAmount = Objects.nonNull(contract.getLoanAmount())
					? contract.getLoanAmount()
					: BigDecimal.ZERO;

		}

		BigDecimal deposit = BigDecimal.ZERO;
		if (Objects.nonNull(contract)) {
			deposit = Objects.nonNull(contract.getDeposit())
					? contract.getDeposit()
					: BigDecimal.ZERO;
		}
		switch (QuotaChangeDef.CostType.from(quotaChange.getCostType())) {
			// 货款改项目的预估可提货余额。履约保证金改合同的履约保证金
			case GOODS_PAYMENT -> {
				// 数据库原来的额度变更
				QuotaChange oldQuotaChange = super.findOne(quotaChange.getId())
						.orElse(null);
				switch (QuotaChangeDef.ChangeType
						.from(quotaChange.getChangeType())) {
					// 新增
					case ADD -> {
						BigDecimal totalLoanAmount = BigDecimal.ZERO;
						BigDecimal totalDeposit = BigDecimal.ZERO;
						// 如果是删除操作 采购额度变更之前是新增的需要减去金额
						if ((Objects.nonNull(oldQuotaChange))
								&& (Boolean.TRUE.equals(isDelete))) {
							totalLoanAmount = loanAmount
									.subtract(oldQuotaChange.getApplyAmount());
						}
						// 如果是修改操作 采购额度变更需要变更金额
						else if ((Objects.nonNull(oldQuotaChange))
								&& (Boolean.TRUE.equals(isUpdate))) {
							if (QuotaChangeDef.Type.BUY
									.match(quotaChange.getType())) {
								// 之前的费用类型是货款
								if (QuotaChangeDef.CostType.GOODS_PAYMENT
										.match(oldQuotaChange.getCostType())) {
									// 先把上次的数据还原回去
									totalLoanAmount = this
											.handleTotalLoanAmount(
													oldQuotaChange,
													totalLoanAmount,
													loanAmount);
								} else {
									totalLoanAmount = loanAmount;
								}
								// 之前的费用类型是履约保证金
								if (QuotaChangeDef.CostType.BOND
										.match(oldQuotaChange.getCostType())) {
									// 先把上次的数据还原回去
									totalDeposit = this.handleTotalDeposit(
											oldQuotaChange, totalDeposit,
											deposit);
								} else {
									totalDeposit = deposit;
								}
								// 再处理本次申请的金额
								totalLoanAmount = totalLoanAmount
										.add(quotaChange.getApplyAmount());
							}
						} else {
							totalLoanAmount = loanAmount
									.add(quotaChange.getApplyAmount());
						}
						contractService.updateLoanAmount(
								quotaChange.getContractId(), totalLoanAmount);
						// contractService.updateDeposit(
						// quotaChange.getContractId(), totalDeposit);

					}
					case SUBTRACT -> {
						BigDecimal totalLoanAmount = BigDecimal.ZERO;
						BigDecimal totalDeposit = BigDecimal.ZERO;
						// 如果是删除操作 之前是减去的需要加上金额
						if ((Objects.nonNull(oldQuotaChange))
								&& (Boolean.TRUE.equals(isDelete))) {
							totalLoanAmount = loanAmount
									.add(oldQuotaChange.getApplyAmount());
						}
						// 如果是修改操作 采购额度变更需要变更金额
						else if ((Objects.nonNull(oldQuotaChange))
								&& (Boolean.TRUE.equals(isUpdate))) {
							if (QuotaChangeDef.Type.BUY
									.match(quotaChange.getType())) {
								// 之前的费用类型是货款
								if (QuotaChangeDef.CostType.GOODS_PAYMENT
										.match(oldQuotaChange.getCostType())) {
									// 先把上次的数据还原回去
									totalLoanAmount = this
											.handleTotalLoanAmount(
													oldQuotaChange,
													totalLoanAmount,
													loanAmount);
								} else {
									totalLoanAmount = loanAmount;
								}
								// 之前的费用类型是履约保证金
								if (QuotaChangeDef.CostType.BOND
										.match(oldQuotaChange.getCostType())) {
									// 先把上次的数据还原回去
									totalDeposit = this.handleTotalDeposit(
											oldQuotaChange, totalDeposit,
											deposit);
								} else {
									totalDeposit = deposit;
								}
								// 再处理本次申请的金额
								totalLoanAmount = totalLoanAmount
										.subtract(quotaChange.getApplyAmount());
							}
						} else {
							totalLoanAmount = loanAmount
									.subtract(quotaChange.getApplyAmount());
						}
						contractService.updateLoanAmount(
								quotaChange.getContractId(), totalLoanAmount);
						// contractService.updateDeposit(
						// quotaChange.getContractId(), totalDeposit);
					}
				}
			}
			case BOND -> {
				// 数据库原来的额度变更
				QuotaChange oldQuotaChange = super.findOne(quotaChange.getId())
						.orElse(null);
				switch (QuotaChangeDef.ChangeType
						.from(quotaChange.getChangeType())) {
					case ADD -> {
						BigDecimal totalDeposit = BigDecimal.ZERO;
						BigDecimal totalLoanAmount = BigDecimal.ZERO;
						// 如果是删除操作 之前是新增的需要减去金额
						if ((Objects.nonNull(oldQuotaChange))
								&& (Boolean.TRUE.equals(isDelete))) {
							totalDeposit = deposit
									.subtract(oldQuotaChange.getApplyAmount());
						} else if ((Objects.nonNull(oldQuotaChange))
								&& (Boolean.TRUE.equals(isUpdate))) {
							// 如果是修改操作 采购额度变更需要变更金额
							if (QuotaChangeDef.Type.BUY
									.match(quotaChange.getType())) {
								// 之前的费用类型是货款
								if (QuotaChangeDef.CostType.GOODS_PAYMENT
										.match(oldQuotaChange.getCostType())) {
									// 先把上次的数据还原回去
									totalLoanAmount = this
											.handleTotalLoanAmount(
													oldQuotaChange,
													totalLoanAmount,
													loanAmount);
								} else {
									totalLoanAmount = loanAmount;
								}
								// 之前的费用类型是履约保证金
								if (QuotaChangeDef.CostType.BOND
										.match(oldQuotaChange.getCostType())) {
									// 先把上次的数据还原回去
									totalDeposit = this.handleTotalDeposit(
											oldQuotaChange, totalDeposit,
											deposit);
								} else {
									totalDeposit = deposit;
								}
								totalDeposit = totalDeposit
										.add(quotaChange.getApplyAmount());
							}
						} else {
							totalDeposit = deposit
									.add(quotaChange.getApplyAmount());
						}
						// contractService.updateLoanAmount(
						// quotaChange.getContractId(), totalLoanAmount);
						contractService.updateDeposit(
								quotaChange.getContractId(), totalDeposit);

					}
					case SUBTRACT -> {
						BigDecimal totalDeposit = BigDecimal.ZERO;
						BigDecimal totalLoanAmount = BigDecimal.ZERO;
						// 如果是删除操作 之前是减去的需要加上金额
						if (Objects.nonNull(oldQuotaChange)
								&& Boolean.TRUE.equals(isDelete)) {
							totalDeposit = deposit
									.add(oldQuotaChange.getApplyAmount());
						}
						// 更新操作采购变更需要特殊处理
						else if (Objects.nonNull(oldQuotaChange)
								&& Boolean.TRUE.equals(isUpdate)) {
							if (QuotaChangeDef.Type.BUY
									.match(quotaChange.getType())) {
								// 之前的费用类型是货款
								if (QuotaChangeDef.CostType.GOODS_PAYMENT
										.match(oldQuotaChange.getCostType())) {
									// 先把上次的数据还原回去
									totalLoanAmount = this
											.handleTotalLoanAmount(
													oldQuotaChange,
													totalLoanAmount,
													loanAmount);
								} else {
									totalLoanAmount = loanAmount;
								}
								// 之前的费用类型是履约保证金
								if (QuotaChangeDef.CostType.BOND
										.match(oldQuotaChange.getCostType())) {
									// 先把上次的数据还原回去
									totalDeposit = this.handleTotalDeposit(
											oldQuotaChange, totalDeposit,
											deposit);
								} else {
									totalDeposit = deposit;
								}
								totalDeposit = totalDeposit
										.subtract(quotaChange.getApplyAmount());
							}
						} else {
							totalDeposit = deposit
									.subtract(quotaChange.getApplyAmount());
						}
						// contractService.updateLoanAmount(
						// quotaChange.getContractId(), totalLoanAmount);
						contractService.updateDeposit(
								quotaChange.getContractId(), totalDeposit);
					}
				}
			}
			case TRANSFER_PAYMENT -> {
				// 履约保证金转货款
				// 数据库原来的额度变更
				QuotaChange oldQuotaChange = super.findOne(quotaChange.getId())
						.orElse(null);
				BigDecimal totalLoanAmount = BigDecimal.ZERO;
				BigDecimal totalDeposit = BigDecimal.ZERO;
				if (isDelete) {
					// 如果是删除，则需要将原来的合同数据改回去，
					// 货款金额 = 合同中的贷款金额 - 额度变更的申请金额
					totalLoanAmount = loanAmount
							.subtract(oldQuotaChange.getApplyAmount());
					// 履约保证金 = 合同中的履约保证金 + 额度变更的申请金额
					totalDeposit = deposit.add(oldQuotaChange.getApplyAmount());
				} else if (isUpdate) {
					// 如果是修改，则需要将原来的合同数据改回去，还需要将新的合同数据加进去
					// 货款金额 = 合同中的贷款金额 - 老额度变更的申请金额 + 新的额度变更的申请金额
					totalLoanAmount = loanAmount
							.subtract(oldQuotaChange.getApplyAmount())
							.add(quotaChange.getApplyAmount());
					// 履约保证金 = 合同中的履约保证金 + 额度变更的申请金额 + 新额度变更的申请金额
					totalDeposit = deposit.add(oldQuotaChange.getApplyAmount())
							.subtract(quotaChange.getApplyAmount());
				} else {
					// 新增
					totalLoanAmount = loanAmount
							.add(quotaChange.getApplyAmount());
					totalDeposit = deposit
							.subtract(quotaChange.getApplyAmount());
				}
				contractService.updateLoanAmount(quotaChange.getContractId(),
						totalLoanAmount);
				contractService.updateDeposit(quotaChange.getContractId(),
						totalDeposit);
			}
			case TRANSFER_BOND -> {
				// 货款转履约保证金
				// 数据库原来的额度变更
				QuotaChange oldQuotaChange = super.findOne(quotaChange.getId())
						.orElse(null);
				BigDecimal totalLoanAmount = BigDecimal.ZERO;
				BigDecimal totalDeposit = BigDecimal.ZERO;
				if (isDelete) {
					// 如果是删除，则需要将原来的合同数据改回去，
					// 货款金额 = 合同中的贷款金额 + 额度变更的申请金额
					totalLoanAmount = loanAmount
							.add(oldQuotaChange.getApplyAmount());
					// 履约保证金 = 合同中的履约保证金 + 额度变更的申请金额
					totalDeposit = deposit
							.subtract(oldQuotaChange.getApplyAmount());
				} else if (isUpdate) {
					// 如果是修改，则需要将原来的合同数据改回去，还需要将新的合同数据加进去
					// 货款金额 = 合同中的贷款金额 + 老额度变更的申请金额 - 新的额度变更的申请金额
					totalLoanAmount = loanAmount
							.add(oldQuotaChange.getApplyAmount())
							.subtract(quotaChange.getApplyAmount());
					// 履约保证金 = 合同中的履约保证金 + 额度变更的申请金额 + 新额度变更的申请金额
					totalDeposit = deposit
							.subtract(oldQuotaChange.getApplyAmount())
							.add(quotaChange.getApplyAmount());
				} else {
					// 新增
					totalLoanAmount = loanAmount
							.subtract(quotaChange.getApplyAmount());
					totalDeposit = deposit.add(quotaChange.getApplyAmount());
				}
				contractService.updateLoanAmount(quotaChange.getContractId(),
						totalLoanAmount);
				contractService.updateDeposit(quotaChange.getContractId(),
						totalDeposit);
			}
		}
	}

	// 处理采购额度变更之前的履约保证金金额
	private BigDecimal handleTotalDeposit(QuotaChange oldQuotaChange,
			BigDecimal totalDeposit, BigDecimal deposit) {
		if (Objects.nonNull(oldQuotaChange)) {
			if (QuotaChangeDef.ChangeType.ADD
					.match(oldQuotaChange.getChangeType())) {
				// 之前是增加的先减去原来的金额
				totalDeposit = deposit
						.subtract(oldQuotaChange.getApplyAmount());
			} else {
				// 之前是减少的先加上原来的金额
				totalDeposit = deposit.add(oldQuotaChange.getApplyAmount());
			}
		}
		return totalDeposit;
	}

	// 处理采购额度变更之前的货款金额
	private BigDecimal handleTotalLoanAmount(QuotaChange oldQuotaChange,
			BigDecimal totalLoanAmount, BigDecimal loanAmount) {
		if (Objects.nonNull(oldQuotaChange)) {
			if (QuotaChangeDef.ChangeType.ADD
					.match(oldQuotaChange.getChangeType())) {
				// 之前是增加的先减去原来的金额
				totalLoanAmount = loanAmount
						.subtract(oldQuotaChange.getApplyAmount());
			} else {
				// 之前是减少的先加上原来的金额
				totalLoanAmount = loanAmount
						.add(oldQuotaChange.getApplyAmount());
			}
		}
		return totalLoanAmount;
	}

	@Override
	public Optional<QuotaChangeCountVo> staticsAdminQuotaChange(
			boolean isManage) {
		QuotaChangeCountVo quotaChangeCountVo = new QuotaChangeCountVo();
		quotaChangeCountVo.setWaitConfirm(0L);
		quotaChangeCountVo.setReject(0L);
		List<String> projectIds = projectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计待确认
				LambdaQueryWrapper<QuotaChange> wrapper = Wrappers
						.lambdaQuery(QuotaChange.class);
				this.filterDeleted(wrapper);
				wrapper.and(x -> x
						.ne(QuotaChange::getInitiator,
								CommonDef.AccountSource.INNER.getCode())
						.eq(QuotaChange::getState,
								QuotaChangeDef.State.CONFIRMING.getCode()));
				wrapper.in(QuotaChange::getProjectId, projectIds);
				quotaChangeCountVo
						.setWaitConfirm(repository.selectCount(wrapper));
				wrapper.clear();

				// 统计已驳回
				this.filterDeleted(wrapper);
				wrapper.and(x -> x
						.eq(QuotaChange::getInitiator,
								CommonDef.AccountSource.INNER.getCode())
						.eq(QuotaChange::getState,
								QuotaChangeDef.State.REJECTED.getCode()));
				wrapper.in(QuotaChange::getProjectId, projectIds);
				quotaChangeCountVo.setReject(repository.selectCount(wrapper));
			}
		}
		return Optional.of(quotaChangeCountVo);
	}

	@Override
	public Optional<QuotaChangeCountVo> staticsCustomerQuotaChange(
			boolean isPermission) {
		QuotaChangeCountVo quotaChangeCountVo = new QuotaChangeCountVo();
		quotaChangeCountVo.setWaitConfirm(0L);
		quotaChangeCountVo.setReject(0L);
		LambdaQueryWrapper<QuotaChange> wrapper = Wrappers
				.lambdaQuery(QuotaChange.class);
		Long customerId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();

		if (isPermission) {
			// 统计待确认
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.and(x -> x
					.ne(QuotaChange::getInitiator,
							CommonDef.AccountSource.CUSTOM.getCode())
					.eq(QuotaChange::getState,
							QuotaChangeDef.State.CONFIRMING.getCode()));
			wrapper.eq(QuotaChange::getPurchaserId, customerId);
			quotaChangeCountVo.setWaitConfirm(repository.selectCount(wrapper));

			// 统计已驳回
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.and(x -> x
					.eq(QuotaChange::getInitiator,
							CommonDef.AccountSource.CUSTOM.getCode())
					.eq(QuotaChange::getState,
							QuotaChangeDef.State.REJECTED.getCode()));
			wrapper.eq(QuotaChange::getPurchaserId, CustomerContextHolder
					.getCustomerLoginVo().getProxyAccount().getId());
			quotaChangeCountVo.setReject(repository.selectCount(wrapper));
		}

		return Optional.of(quotaChangeCountVo);
	}

	@Override
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = "{{#success}}", type = LogDef.CREDIT_LIMIT_CHANGE_INFO, bizNo = "{{#resource.getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#resource.getId()}}"),
			@LogRecord.KeyValuePair(key = "#projectId#", value = "{{#code}}") }, messageType = LogDef.MESSAGE_TYPE_FUNDING, permission = LogDef.PROJECT_DEAL)
	public void notice(QuotaChange resource, Integer type) {
		Project project = projectService.findOne(resource.getProjectId())
				.orElse(new Project());
		LogRecordContext.putVariable("code", project.getName());
		switch (type) {
			case 1 -> LogRecordContext.putVariable("success",
					LogDef.CREDIT_LIMIT_CHANGE_PENDING_CONFIRMATION);
			case 2 -> LogRecordContext.putVariable("success",
					LogDef.CREDIT_LIMIT_CHANGE_CONFIRMED);
			case 3 -> LogRecordContext.putVariable("success",
					LogDef.CREDIT_LIMIT_CHANGE_REJECTED);
			default -> {
			}
		}
		log.info("额度变更发送通知:{}", resource.getId());
	}

	/**
	 * 组装数据
	 *
	 * @param quotaChangeList
	 * @return
	 */
	private List<QuotaChangeVo> packVo(List<QuotaChange> quotaChangeList) {
		if (CollectionUtils.isEmpty(quotaChangeList)) {
			return List.of();
		}
		List<String> contractIds = quotaChangeList.stream()
				.map(QuotaChange::getContractId).toList();
		Map<String, Contract> contractMap = contractService
				.getIdMap(contractIds);

		List<String> projectIds = quotaChangeList.stream()
				.map(QuotaChange::getProjectId).toList();

		Map<String, Project> projectMap = projectService.getIdMap(projectIds);

		return quotaChangeList.stream().map(item -> {
			QuotaChangeVo quotaChangeVo = new QuotaChangeVo();
			quotaChangeVo.setQuotaChange(item);
			if (StringUtils.isNotBlank(item.getContractId())) {
				quotaChangeVo
						.setContract(contractMap.get(item.getContractId()));
			}
			if (Objects.nonNull(item.getProjectId())) {
				quotaChangeVo.setProject(projectMap.get(item.getProjectId()));
			}
			return quotaChangeVo;
		}).toList();
	}

	/**
	 * 组装数据
	 *
	 * @param quotaChange
	 * @return
	 */
	private QuotaChangeVo packVo(QuotaChange quotaChange) {
		QuotaChangeVo quotaChangeVo = new QuotaChangeVo();
		quotaChangeVo.setQuotaChange(quotaChange);
		if (StringUtils.isNotBlank(quotaChange.getContractId())) {
			quotaChangeVo.setContract(contractService
					.findOne(quotaChange.getContractId()).orElse(null));
		}
		if (Objects.nonNull(quotaChange.getProjectId())) {
			quotaChangeVo.setProject(projectService
					.findOne(quotaChange.getProjectId()).orElse(null));
		}
		return quotaChangeVo;
	}

	/**
	 * 发送短信
	 *
	 * @param quotaChange
	 * @param templateCode
	 * @param title
	 */
	private void sendNotice(QuotaChange quotaChange, String templateCode,
			String title, Integer type) {
		Customer customer = null;
		if (QuotaChangeDef.Type.SELL.match(type)) {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(quotaChange.getPurchaserBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		} else {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(quotaChange.getSellerBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		}

		if (Objects.nonNull(customer)) {
			if (StringUtils.isNotBlank(templateCode)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(templateCode)
						.params(Map.of("order_id", quotaChange.getId()))
						.mobile(customer.getMobile()).build());
			}

			if (StringUtils.isNotBlank(title)) {
				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.FUNDING.getCode())
						.title(title)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.QUOTA_CHANGE_APPLY_DETAIL_PAGE)
						.detailId(String.valueOf(quotaChange.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
		}
	}
}
