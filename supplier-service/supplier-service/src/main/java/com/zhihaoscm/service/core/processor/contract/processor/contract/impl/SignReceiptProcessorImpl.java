package com.zhihaoscm.service.core.processor.contract.processor.contract.impl;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.core.processor.contract.processor.contract.ContractProcessor;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

@Service
public class SignReceiptProcessorImpl implements ContractProcessor {

	@Autowired
	private SignReceiptService signReceiptService;

	@Autowired
	private ContractRecordService contractRecordService;

	@Autowired
	private FileService fileService;

	@Autowired
	private DeliverGoodsService deliverGoodsService;

	@Autowired
	private OrderService orderService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private SMSProperties wxSubscriptionProperties;

	@Autowired
	private ContractService contractService;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	@Override
	public Boolean support(Integer type) {
		return PurchaseContractDef.CorrelationTable.GOODS_RECEIPT.match(type);
	}

	@Override
	public void signComplete(String tableId) {
		signReceiptService.findOne(tableId).ifPresent(signReceipt -> {

			signReceipt.setSignStatus(
					BusinessContractDef.CommonSignState.COMPLETED.getCode());

			signReceipt.setStatus(SignReceiptDef.Status.FINISHED.getCode());
			signReceipt.setReconciliationStatus(
					OrderDef.BusinessStatus.NOT_STARTED.getCode());
			// 修改状态
			// 重新获取新的签署文件
			Long newFileId = contractRecordService.download(tableId,
					PurchaseContractDef.CorrelationTable.GOODS_RECEIPT);
			signReceipt.setSignReceiptFileId(newFileId);
			signReceipt.setSignReceiptFileId(newFileId);
			signReceiptService.update(signReceipt);

			Contract contract = contractService
					.findOne(signReceipt.getContractId())
					.orElse(new Contract());

			// 更新订单中的签收单状态
			if (ContractDef.Type.SELL.match(contract.getContractType())) {
				List<DeliverGoods> deliverGoodsList = deliverGoodsService
						.findByIds(signReceipt.getRelatedDeliverGoodsIds());
				List<String> orderIds = deliverGoodsList.stream()
						.map(DeliverGoods::getOrderId).toList();
				Map<String, List<DeliverGoods>> collect = deliverGoodsList
						.stream().collect(Collectors
								.groupingBy(DeliverGoods::getOrderId));
				List<Order> orderList = orderService.findByIds(orderIds);
				// 完成签收单后变更订单中的签收重量
				signReceiptService.changeOrderReceiptWeight(orderList, collect);
				// 签收单完成后判断订单中的签收单状态是否可以改为已完成状态
				signReceiptService.completedChangeOrderStatus(orderIds);
			}

			Customer customer = null;
			if (ReconciliationDef.Type.SELL.match(signReceipt.getType())) {
				DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
						.findOne(signReceipt.getPurchaserInputId())
						.orElse(null);
				if (Objects.nonNull(dealingsEnterprise) && Objects
						.nonNull(dealingsEnterprise.getCustomerId())) {
					customer = customerService
							.findOne(dealingsEnterprise.getCustomerId())
							.orElse(null);
				}
			} else {
				DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
						.findOne(signReceipt.getSellerInputId()).orElse(null);
				if (Objects.nonNull(dealingsEnterprise) && Objects
						.nonNull(dealingsEnterprise.getCustomerId())) {
					customer = customerService
							.findOne(dealingsEnterprise.getCustomerId())
							.orElse(null);
				}
			}

			if (SignReceiptDef.Type.SELL.match(signReceipt.getType())) {
				signReceiptService.notice(signReceipt, 4);

				if (Objects.nonNull(customer)) {
					messageService.sendNotice(AliMessage.builder()
							.receiptors(
									List.of(String.valueOf(customer.getId())))
							.templateCode(wxSubscriptionProperties
									.getFinishReceiptCode())
							.params(Map.of("order_id", signReceipt.getId()))
							.mobile(customer.getMobile()).build());

					messageService.sendNotice(UserMessage.builder()
							.type(UserMessageDef.MessageType.ORDER.getCode())
							.title(MessageFormat.format(
									UserMessageConstants.SIGN_RECEIPT_FINISHED_TEMPLATE,
									signReceipt.getId()))
							.receiptors(
									List.of(String.valueOf(customer.getId())))
							.url(UserMessageConstants.SIGN_RECEIPT_DETAIL_PAGE)
							.detailId(String.valueOf(signReceipt.getId()))
							.initiator(UserMessageDef.BusinessInitiator.receipt
									.getCode())
							.build());
				}
			} else {
				signReceiptService.notice(signReceipt, 4);
			}
		});
	}

	@Override
	public void signReject(String tableId, String contact) {
		signReceiptService.findOne(tableId)
				.ifPresent(signReceipt -> signReceiptService.revert(signReceipt,
						Boolean.FALSE));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void signing(String correlationId, String name,
			String callbackType) {
		signReceiptService.findOne(correlationId).ifPresent(signReceipt -> {

			Long newFileId = contractRecordService.download(correlationId,
					PurchaseContractDef.CorrelationTable.GOODS_RECEIPT);
			fileService
					.batchUnActive(List.of(signReceipt.getSignReceiptFileId()));
			signReceipt.setSignReceiptFileId(newFileId);

			// 防止契约锁回调顺序有问题的处理
			if (Objects.equals(signReceipt.getStatus(),
					SignReceiptDef.Status.FINISHED.getCode())) {
				return;
			}

			boolean isBuyer = false;
			if (PurchaseContractDef.CallbackType.SEAL.name()
					.equals(callbackType)) {
				isBuyer = name
						.equals(signReceipt.getPurchaserEnterprise().getName());
			} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
					.equals(callbackType)) {
				isBuyer = name.equals(
						signReceipt.getPurchaserEnterprise().getRealName());
			}
			boolean isSupplier = false;
			if (PurchaseContractDef.CallbackType.SEAL.name()
					.equals(callbackType)) {
				isSupplier = name
						.equals(signReceipt.getSellerEnterprise().getName());
			} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
					.equals(callbackType)) {
				isSupplier = name.equals(
						signReceipt.getSellerEnterprise().getRealName());
			}
			if (isBuyer) {
				if (BusinessContractDef.CommonSignState.SUPPLY_SIGNED
						.match(signReceipt.getSignStatus())) {
					signReceipt.setSignStatus(
							BusinessContractDef.CommonSignState.COMPLETED
									.getCode());
				} else {
					signReceipt.setSignStatus(
							BusinessContractDef.CommonSignState.BUYER_SIGNED
									.getCode());
				}
			} else if (isSupplier) {
				if (BusinessContractDef.CommonSignState.BUYER_SIGNED
						.match(signReceipt.getSignStatus())) {
					signReceipt.setSignStatus(
							BusinessContractDef.CommonSignState.COMPLETED
									.getCode());
				} else {
					signReceipt.setSignStatus(
							BusinessContractDef.CommonSignState.SUPPLY_SIGNED
									.getCode());
				}
			}
			signReceiptService.update(signReceipt);
		});

	}

	@Override
	public void sendInvalid(String tableId, String name) {
		signReceiptService.findOne(tableId).ifPresent(signReceipt -> {
			if (SignReceiptDef.Status.INVALIDING.match(signReceipt.getStatus())
					&& Objects.nonNull(signReceipt.getInvalidFileId())) {
				return;
			}

			// 作废后获取作废合同id
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.GOODS_RECEIPT);
			SignReceipt signReceipt1 = new SignReceipt();
			signReceipt1.setInvalidFileId(fileId);
			signReceipt1.setStatus(SignReceiptDef.Status.INVALIDING.getCode());
			signReceipt1.setInvalidSignState(
					PurchaseContractDef.CommonSignState.UNSIGNED.getCode());
			signReceiptService.updateNotNull(signReceipt1);
		});
	}

	@Override
	public void invaliding(String tableId, String name) {
		signReceiptService.findOne(tableId).ifPresent(signReceipt -> {

			// 防止契约锁回调顺序有问题的处理
			if (Objects.equals(SignReceiptDef.Status.INVALID.getCode(),
					signReceipt.getStatus())) {
				return;
			}
			boolean isBuyer;
			boolean isSupplier;
			isBuyer = name
					.equals(signReceipt.getPurchaserEnterprise().getName())
					|| name.equals(
							signReceipt.getPurchaserEnterprise().getRealName());

			isSupplier = name
					.equals(signReceipt.getSellerEnterprise().getName())
					|| name.equals(
							signReceipt.getSellerEnterprise().getRealName());

			if (isBuyer) {
				signReceipt.setInvalidSignState(
						PurchaseContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				signReceipt.setPurchaseInvalidTime(LocalDateTime.now());
			} else if (isSupplier) {
				signReceipt.setInvalidSignState(
						PurchaseContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
				signReceipt.setSellerInvalidTime(LocalDateTime.now());
			}

			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.GOODS_RECEIPT);
			signReceipt.setInvalidFileId(fileId);
			signReceiptService.update(signReceipt);
		});
	}

	@Override
	public void invalided(String tableId, String name) {
		signReceiptService.findOne(tableId).ifPresent(signReceipt -> {
			signReceipt.setStatus(SignReceiptDef.Status.INVALID.getCode());
			signReceipt.setInvalidSignState(
					PurchaseContractDef.CommonSignState.COMPLETED.getCode());
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.GOODS_RECEIPT);
			signReceipt.setInvalidFileId(fileId);
			signReceiptService.update(signReceipt);
			signReceiptService.revertDeliverAndOrder(signReceipt);
		});
	}

	@Override
	public void rejectInvalid(String tableId, String name) {
		signReceiptService.findOne(tableId).ifPresent(signReceipt -> {
			signReceipt.setStatus(SignReceiptDef.Status.FINISHED.getCode());
			signReceipt.setInvalidSignState(null);
			signReceipt.setInvalidRevokeTime(LocalDateTime.now());
			signReceiptService.updateAllProperties(signReceipt);
			List<DeliverGoods> deliverGoodsList = deliverGoodsService
					.findByIds(signReceipt.getRelatedDeliverGoodsIds());
			List<String> orderIds = deliverGoodsList.stream()
					.map(DeliverGoods::getOrderId).toList();
			signReceiptService.completedChangeOrderStatus(orderIds);
			signReceiptService.changeOrderReceiptWeight(signReceipt, false);
		});

	}
}
