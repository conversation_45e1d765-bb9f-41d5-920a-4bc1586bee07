# TenantContextUtil 使用指南

## 概述

`TenantContextUtil` 是一个通用的租户上下文管理工具类，用于在执行特定方法时临时修改或清空 `UserInfoContext` 中的 `tenantId`，执行完成后自动恢复原始值。

## 主要功能

1. **临时清空租户ID**：在无租户上下文中执行操作
2. **临时设置租户ID**：在指定租户上下文中执行操作
3. **自动恢复**：无论是否发生异常，都能正确恢复原始租户ID
4. **支持多种调用方式**：Lambda表达式、方法引用、Runnable等

## 使用场景

您提到的场景：
```java
// 原代码
UserVo userVo = userService.findByMobile(form.getMobile()).orElse(null);
```

需要在调用 `findByMobile` 方法前将 `tenantId` 设置为 null，调用后恢复。

## 解决方案

### 方案1：Lambda表达式（推荐用于复杂逻辑）

```java
// 直接替换原代码
UserVo userVo = TenantContextUtil.executeWithoutTenant(() -> 
    userService.findByMobile(form.getMobile()).orElse(null)
);
```

### 方案2：方法引用（推荐用于简单调用）

```java
// 更简洁的方法引用方式
UserVo userVo = TenantContextUtil.executeWithoutTenant(
    userService::findByMobile, form.getMobile()
).orElse(null);
```

### 方案3：保持Optional类型

```java
// 如果您想保持Optional类型
Optional<UserVo> userVoOptional = TenantContextUtil.executeWithoutTenant(
    userService::findByMobile, form.getMobile()
);
UserVo userVo = userVoOptional.orElse(null);
```

## 更多使用示例

### 1. 在指定租户上下文中执行

```java
// 在租户ID为123的上下文中执行
UserVo userVo = TenantContextUtil.executeWithTenant(123L, () -> 
    userService.findByMobile(mobile).orElse(null)
);
```

### 2. 无返回值的操作

```java
// 更新操作
TenantContextUtil.executeWithoutTenant(() -> {
    userService.updateUserStatus(userId, status);
    // 可以执行多个操作
    logService.log("用户状态已更新");
});
```

### 3. 复杂业务逻辑

```java
UserVo result = TenantContextUtil.executeWithoutTenant(() -> {
    Optional<UserVo> userOptional = userService.findByMobile(mobile);
    if (userOptional.isPresent()) {
        UserVo userVo = userOptional.get();
        // 执行其他业务逻辑
        enrichUserData(userVo);
        return userVo;
    }
    return createDefaultUser(mobile);
});
```

### 4. 多参数方法调用

```java
UserVo userVo = TenantContextUtil.executeWithoutTenant(() -> 
    userService.findByMobileAndOrigin(mobile, origin).orElse(null)
);
```

### 5. 链式调用

```java
String userName = TenantContextUtil.executeWithoutTenant(() -> 
    userService.findByMobile(mobile)
        .map(userVo -> userVo.getUser().getName())
        .orElse("未找到用户")
);
```

## API 说明

### 主要方法

1. `executeWithoutTenant(Supplier<T> supplier)` - 在无租户上下文中执行有返回值的操作
2. `executeWithTenant(Long tenantId, Supplier<T> supplier)` - 在指定租户上下文中执行有返回值的操作
3. `executeWithoutTenant(Runnable runnable)` - 在无租户上下文中执行无返回值的操作
4. `executeWithTenant(Long tenantId, Runnable runnable)` - 在指定租户上下文中执行无返回值的操作
5. `executeWithoutTenant(Function<T, R> function, T parameter)` - 在无租户上下文中执行带参数的操作
6. `executeWithTenant(Long tenantId, Function<T, R> function, T parameter)` - 在指定租户上下文中执行带参数的操作

### 异常处理

工具类使用 `try-finally` 确保无论是否发生异常，都能正确恢复原始的租户ID。

## 注意事项

1. **线程安全**：基于 `ThreadLocal` 实现，每个线程独立管理上下文
2. **嵌套调用**：支持嵌套调用，内层调用不会影响外层的上下文恢复
3. **性能影响**：工具类开销很小，主要是上下文的保存和恢复操作
4. **异常安全**：即使业务代码抛出异常，也能正确恢复上下文

## 迁移建议

对于您现有的代码：
```java
UserVo userVo = userService.findByMobile(form.getMobile()).orElse(null);
```

推荐的迁移方式：
```java
// 方式1：最小改动
UserVo userVo = TenantContextUtil.executeWithoutTenant(() -> 
    userService.findByMobile(form.getMobile()).orElse(null)
);

// 方式2：更简洁（推荐）
UserVo userVo = TenantContextUtil.executeWithoutTenant(
    userService::findByMobile, form.getMobile()
).orElse(null);
```

这样就能实现您要求的功能：在调用方法前将 `tenantId` 设置为 null，调用后自动恢复。
