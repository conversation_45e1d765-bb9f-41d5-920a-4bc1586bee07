package com.zhihaoscm.service.resource.validator.transport.order.vehicle;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.zhihaoscm.domain.exception.DynamicsBadRequestException;
import com.zhihaoscm.domain.meta.biz.TransportOrderRailwayDef;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.DeliverGoods;
import com.zhihaoscm.domain.bean.entity.TransportOrderVehicle;
import com.zhihaoscm.domain.bean.json.AddressJsonInfo;
import com.zhihaoscm.domain.bean.json.ArrayAddressJsonInfo;
import com.zhihaoscm.domain.bean.json.ArrayTransportOrderVehicleJsonInfo;
import com.zhihaoscm.domain.bean.json.TransportOrderVehicleJsonInfo;
import com.zhihaoscm.domain.meta.biz.DeliverGoodsDef;
import com.zhihaoscm.domain.meta.biz.TransportOrderVehicleDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.TransportOrderVehicleService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.resource.form.transport.order.vehicle.*;
import com.zhihaoscm.service.resource.validator.delivergoods.DeliverGoodsValidator;
import com.zhihaoscm.service.resource.validator.project.ProjectValidator;

@Component
public class TransportOrderVehicleValidator {

	@Autowired
	private TransportOrderVehicleService transportOrderVehicleService;

	@Autowired
	private DeliverGoodsValidator deliverGoodsValidator;

	@Autowired
	private ProjectValidator projectValidator;

	@Autowired
	private CustomerService customerService;

	/**
	 * 校验存在
	 */
	public TransportOrderVehicle validatorExist(String id) {
		return transportOrderVehicleService.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30158001));
	}

	/**
	 * 校验新增
	 * 
	 * @param form
	 */
	public void validatorCreate(TransportOrderVehicleCreateForm form) {
		this.validatorDeliverGoodsCancel(form.getDeliverGoodsId());
		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(form.getDeliverGoodsId());
		projectValidator.validateProjectPeople(deliverGoods.getProjectId());
		if (DeliverGoodsDef.Status.DELIVER_COMPLETE
				.match(deliverGoods.getStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30158047);
		}
		this.validatorParam(form.getFreightType(), form.getFreightUnitPrice(),
				form.getFreightUnitPriceType(), form.getTransportVolume(),
				form.getTransportItems(), form.getEstimateFreightRevenue());
		this.validatorAddress(form.getAddress());
	}

	/**
	 * 校验发车
	 * 
	 * @param id
	 * @param form
	 * @return
	 */
	public TransportOrderVehicle validatorDeparture(String id,
			TransportOrderVehicleDepartureForm form, Integer createType) {
		TransportOrderVehicle transportOrderVehicle = this.validatorExist(id);
        if(Objects.equals(createType, transportOrderVehicle.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderVehicle.getCreateType()).getName());
        }
        // 发货时间不能晚于当前时间
        LocalDateTime now = LocalDateTimeUtil.now();
        if(now.isBefore(form.getDepartureTime())) {
            throw new BadRequestException(ErrorCode.CODE_30158059);
        }

        projectValidator
				.validateProjectPeople(transportOrderVehicle.getProjectId());
		this.validatorDeliverGoodsCancel(
				transportOrderVehicle.getDeliverGoodsId());
		if (!TransportOrderVehicleDef.State.WAITING_TO_GO
				.match(transportOrderVehicle.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30158033);
		}

		transportOrderVehicle
				.setState(TransportOrderVehicleDef.State.DEPARTED.getCode());
		transportOrderVehicle.setEstimatedWeight(form.getEstimatedWeight());
		transportOrderVehicle
				.setDeparturePicFileId(form.getDeparturePicFileId());
		transportOrderVehicle.setOtherDeparturePicFileIds(
				form.getOtherDeparturePicFileIds());
		transportOrderVehicle.setDepartureTime(form.getDepartureTime());
		transportOrderVehicle.setDepartureAddress(form.getDepartureAddress());
		transportOrderVehicle.setDepartureCallTime(LocalDateTime.now());
		return transportOrderVehicle;
	}

	/**
	 * 校验异常上报
	 * 
	 * @param id
	 * @param form
	 * @return
	 */
	public TransportOrderVehicle validatorException(String id,
			TransportOrderVehicleExceptionForm form) {
		TransportOrderVehicle transportOrderVehicle = this.validatorExist(id);
		projectValidator
				.validateProjectPeople(transportOrderVehicle.getProjectId());
		this.validatorDeliverGoodsCancel(
				transportOrderVehicle.getDeliverGoodsId());
		if (TransportOrderVehicleDef.State.CANCELED
				.match(transportOrderVehicle.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30158041);
		}
		TransportOrderVehicleJsonInfo transportOrderVehicleJsonInfo = new TransportOrderVehicleJsonInfo();
		transportOrderVehicleJsonInfo
				.setExceptionReason(form.getExceptionReason());
		transportOrderVehicleJsonInfo.setReportAddress(form.getReportAddress());
		transportOrderVehicleJsonInfo.setReportTime(LocalDateTime.now());
		transportOrderVehicleJsonInfo
				.setExceptionPhotoIds(form.getExceptionPhotoIds());
		ArrayTransportOrderVehicleJsonInfo exceptionInfos = transportOrderVehicle
				.getExceptionInfos();
		if (CollectionUtils.isEmpty(exceptionInfos)) {
			ArrayTransportOrderVehicleJsonInfo exceptionInfo = new ArrayTransportOrderVehicleJsonInfo();
			exceptionInfo.add(transportOrderVehicleJsonInfo);
			transportOrderVehicle.setExceptionInfos(exceptionInfo);
		} else {
			exceptionInfos.add(transportOrderVehicleJsonInfo);
			transportOrderVehicle.setExceptionInfos(exceptionInfos);
		}
		return transportOrderVehicle;
	}

	/**
	 * 校验卸货
	 * 
	 * @param id
	 * @param form
	 * @return
	 */
	public TransportOrderVehicle validatorUnload(String id,
			TransportOrderVehicleUnloadForm form, Integer createType) {
		TransportOrderVehicle transportOrderVehicle = this.validatorExist(id);
        if(Objects.equals(createType, transportOrderVehicle.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderVehicle.getCreateType()).getName());
        }
        // 发货时间不能晚于当前时间
        LocalDateTime now = LocalDateTimeUtil.now();
        if(now.isBefore(form.getUnloadedTime())) {
            throw new BadRequestException(ErrorCode.CODE_30158059);
        }
		projectValidator
				.validateProjectPeople(transportOrderVehicle.getProjectId());
		this.validatorDeliverGoodsCancel(
				transportOrderVehicle.getDeliverGoodsId());
		if (!TransportOrderVehicleDef.State.DEPARTED
				.match(transportOrderVehicle.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30158040);
		}

		// 计算实际运费
		BigDecimal actualCosts = BigDecimal.ZERO;
		if (TransportOrderVehicleDef.FreightType.ONE_SET_PRICE
				.match(transportOrderVehicle.getFreightType())) {
			if (TransportOrderVehicleDef.FreightUnitPriceType.TRIP
					.match(transportOrderVehicle.getFreightUnitPriceType())) {
				// 若单位选择了"元/趟"，实际运费支出=预估单价
				actualCosts = transportOrderVehicle.getFreightUnitPrice();
			} else {
				actualCosts = transportOrderVehicle.getFreightUnitPrice()
						.multiply(form.getUnloadedWeight())
						.setScale(2, RoundingMode.HALF_UP);
			}
		}
		transportOrderVehicle.setActualCosts(actualCosts);
		transportOrderVehicle
				.setState(TransportOrderVehicleDef.State.UNLOADED.getCode());
		transportOrderVehicle.setUnloadedTime(form.getUnloadedTime());
		transportOrderVehicle.setUnloadAddress(form.getUnloadAddress());
		transportOrderVehicle.setUnloadedWeight(form.getUnloadedWeight());
		transportOrderVehicle.setSettlementDocumentsFileId(
				form.getSettlementDocumentsFileId());
		transportOrderVehicle
				.setUnloadedPicFileIds(form.getUnloadedPicFileIds());
		transportOrderVehicle.setUnloadCallTime(LocalDateTime.now());
		return transportOrderVehicle;
	}

	/**
	 * 校验修改
	 * 
	 * @param id
	 * @param form
	 */
	public TransportOrderVehicle validatorUpdate(String id,
			TransportOrderVehicleUpdateForm form, Integer createType) {
		TransportOrderVehicle transportOrderVehicle = this.validatorExist(id);
        if(Objects.equals(createType, transportOrderVehicle.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderVehicle.getCreateType()).getName());
        }

		projectValidator
				.validateProjectPeople(transportOrderVehicle.getProjectId());
		this.validatorDeliverGoodsCancel(
				transportOrderVehicle.getDeliverGoodsId());
		if (TransportOrderVehicleDef.State.CANCELED
				.match(transportOrderVehicle.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30158042);
		}

		if (CollectionUtils.isNotEmpty(form.getExceptionInfos())) {
			for (TransportOrderVehicleJsonInfo exceptionInfo : form
					.getExceptionInfos()) {
				if (Objects.isNull(exceptionInfo.getReportAddress())) {
					throw new BadRequestException(ErrorCode.CODE_30158034);
				}

				if (StringUtils
						.isNotBlank(exceptionInfo.getExceptionReason())) {
					if (exceptionInfo.getExceptionReason().length() > 200) {
						throw new BadRequestException(ErrorCode.CODE_30158035);
					}
				}
			}
		}

		this.validatorParam(form.getFreightType(), form.getFreightUnitPrice(),
				form.getFreightUnitPriceType(), form.getTransportVolume(),
				form.getTransportItems(), form.getEstimateFreightRevenue());

		this.validatorAddress(form.getAddress());

		if (TransportOrderVehicleDef.State.DEPARTED
				.match(transportOrderVehicle.getState())
				|| TransportOrderVehicleDef.State.UNLOADED
						.match(transportOrderVehicle.getState())) {
			TransportOrderVehicleDepartureItem transportOrderVehicleDepartureItem = form
					.getTransportOrderVehicleDepartureItem();
			if (Objects.isNull(transportOrderVehicleDepartureItem)) {
				throw new BadRequestException(ErrorCode.CODE_30158052);
			}

			if (Objects.isNull(
					transportOrderVehicleDepartureItem.getDepartureTime())) {
				throw new BadRequestException(ErrorCode.CODE_30158030);
			}

			if (CollectionUtils.isEmpty(
					transportOrderVehicleDepartureItem.getDepartureAddress())) {
				throw new BadRequestException(ErrorCode.CODE_30158031);
			}

			if (Objects.isNull(transportOrderVehicleDepartureItem
					.getDeparturePicFileId())) {
				throw new BadRequestException(ErrorCode.CODE_30158032);
			}
		}

		if (TransportOrderVehicleDef.State.UNLOADED
				.match(transportOrderVehicle.getState())) {
			TransportOrderVehicleUnloadItem transportOrderVehicleUnloadItem = form
					.getTransportOrderVehicleUnloadItem();
			if (Objects.isNull(transportOrderVehicleUnloadItem)) {
				throw new BadRequestException(ErrorCode.CODE_30158053);
			}

			if (Objects.isNull(
					transportOrderVehicleUnloadItem.getUnloadedTime())) {
				throw new BadRequestException(ErrorCode.CODE_30158036);
			}

			if (CollectionUtils.isEmpty(
					transportOrderVehicleUnloadItem.getUnloadAddress())) {
				throw new BadRequestException(ErrorCode.CODE_30158037);
			}

			if (Objects.isNull(transportOrderVehicleUnloadItem
					.getSettlementDocumentsFileId())) {
				throw new BadRequestException(ErrorCode.CODE_30158039);
			}
		}

		transportOrderVehicle = form.update(transportOrderVehicle);

		if (TransportOrderVehicleDef.State.UNLOADED
				.match(transportOrderVehicle.getState())) {
			transportOrderVehicle.setActualCosts(form.getFreightUnitPrice()
					.multiply(form.getTransportOrderVehicleUnloadItem()
							.getUnloadedWeight())
					.setScale(2, RoundingMode.HALF_UP));
		}

		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(transportOrderVehicle.getDeliverGoodsId());

		if (!DeliverGoodsDef.Status.DELIVER_COMPLETE
				.match(deliverGoods.getStatus())) {
			transportOrderVehicle.setTransportWeight(form.getTransportWeight());
		}

		return transportOrderVehicle;
	}

	/**
	 * 校验参数
	 * 
	 * @param freightType
	 * @param freightUnitPrice
	 * @param freightUnitPriceType
	 * @param transportVolume
	 * @param transportItems
	 */
	public void validatorParam(Integer freightType, BigDecimal freightUnitPrice,
			Integer freightUnitPriceType, BigDecimal transportVolume,
			Integer transportItems, BigDecimal estimateFreightRevenue) {
		if (TransportOrderVehicleDef.FreightType.ONE_SET_PRICE
				.match(freightType)) {
			// 运费类型为一口价时单价必填
			if (Objects.isNull(freightUnitPrice)) {
				throw new BadRequestException(ErrorCode.CODE_30158014);
			}

			// 运费类型为一口价时预估运费支出必填
			if (Objects.isNull(estimateFreightRevenue)) {
				throw new BadRequestException(ErrorCode.CODE_30158026);
			}

			// 运费类型为一口价时运费单价类型必填
			if (Objects.isNull(freightUnitPriceType)) {
				throw new BadRequestException(ErrorCode.CODE_30158015);
			}

			// 运费单价类型为元/方时，运输体积必填
			if (TransportOrderVehicleDef.FreightUnitPriceType.SQUARE
					.match(freightUnitPriceType)) {
				if (Objects.isNull(transportVolume)) {
					throw new BadRequestException(ErrorCode.CODE_30158028);
				}
			}

			// 运费单价类型为元/件时，运输件数必填
			if (TransportOrderVehicleDef.FreightUnitPriceType.PIECE
					.match(freightUnitPriceType)) {
				if (Objects.isNull(transportItems)) {
					throw new BadRequestException(ErrorCode.CODE_30158029);
				}
			}
		}
	}

	/**
	 * 校验地址
	 */
	public void validatorAddress(ArrayAddressJsonInfo address) {
		List<Integer> list = address.stream()
				.map(AddressJsonInfo::getAddressType).toList();

		// 装货地
		if (!list.contains(
				TransportOrderVehicleDef.AddressType.LOADING_PLACE.getCode())) {
			throw new BadRequestException(ErrorCode.CODE_30158003);
		}
		// 卸货地
		if (!list.contains(TransportOrderVehicleDef.AddressType.UNLOADED_PLACE
				.getCode())) {
			throw new BadRequestException(ErrorCode.CODE_30158043);
		}
	}

	/**
	 * 校验删除
	 * 
	 * @param id
	 */
	public void validatorDelete(String id, Integer createType) {
		TransportOrderVehicle transportOrderVehicle = this.validatorExist(id);
        if(Objects.equals(createType, transportOrderVehicle.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderVehicle.getCreateType()).getName());
        }
		projectValidator
				.validateProjectPeople(transportOrderVehicle.getProjectId());
		this.validatorDeliverGoodsCancel(
				transportOrderVehicle.getDeliverGoodsId());
		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(transportOrderVehicle.getDeliverGoodsId());
		if (DeliverGoodsDef.Status.DELIVER_COMPLETE
				.match(deliverGoods.getStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30158047);
		}
	}

	/**
	 * 校验发货单是否为取消发货
	 */
	private void validatorDeliverGoodsCancel(String id) {
		DeliverGoods deliverGoods = deliverGoodsValidator.validateExist(id);
		if (DeliverGoodsDef.Status.DELIVER_CANCEL
				.match(deliverGoods.getStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30158048);
		}
	}

	/**
	 * 校验线下新增
	 * 
	 * @param form
	 */
	public TransportOrderVehicle validatorOfflineCreate(
			TransportOrderVehicleOfflineCreateForm form) {
		this.validatorDeliverGoodsCancel(form.getDeliverGoodsId());
		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(form.getDeliverGoodsId());
		projectValidator.validateProjectPeople(deliverGoods.getProjectId());

		if (DeliverGoodsDef.Status.DELIVER_COMPLETE
				.match(deliverGoods.getStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30158047);
		}

		if (form.getExpectedUnloadedTime()
				.isBefore(form.getExpectedLoadingTime())) {
			throw new BadRequestException(ErrorCode.CODE_30158051);
		}

		this.validatorAddress(form.getAddress());

		TransportOrderVehicle transportOrderVehicle = form.convertToEntity();
        if(Objects.nonNull(deliverGoods.getPurchaserId())) {
            customerService.findOne(deliverGoods.getPurchaserId())
                    .ifPresent(customer -> {
                        transportOrderVehicle.setOwnerId(customer.getId());
                        transportOrderVehicle.setOwnerName(customer.getRealName());
                    });
        }
		return transportOrderVehicle;
	}

	/**
	 * 校验线下修改
	 * 
	 * @param id
	 * @param form
	 * @return
	 */
	public TransportOrderVehicle validatorOfflineUpdate(String id,
			TransportOrderVehicleOfflineUpdateForm form, Integer createType) {
		TransportOrderVehicle transportOrderVehicle = this.validatorExist(id);
        if(Objects.equals(createType, transportOrderVehicle.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderVehicle.getCreateType()).getName());
        }
		projectValidator
				.validateProjectPeople(transportOrderVehicle.getProjectId());
		this.validatorDeliverGoodsCancel(
				transportOrderVehicle.getDeliverGoodsId());

		if (TransportOrderVehicleDef.State.CANCELED
				.match(transportOrderVehicle.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30158042);
		}

		if (form.getExpectedUnloadedTime()
				.isBefore(form.getExpectedLoadingTime())) {
			throw new BadRequestException(ErrorCode.CODE_30158051);
		}

		this.validatorAddress(form.getAddress());

		transportOrderVehicle = form.update(transportOrderVehicle);

		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(transportOrderVehicle.getDeliverGoodsId());

		if (!DeliverGoodsDef.Status.DELIVER_COMPLETE
				.match(deliverGoods.getStatus())) {
			transportOrderVehicle.setTransportWeight(form.getTransportWeight());
		}

		return transportOrderVehicle;
	}
}
