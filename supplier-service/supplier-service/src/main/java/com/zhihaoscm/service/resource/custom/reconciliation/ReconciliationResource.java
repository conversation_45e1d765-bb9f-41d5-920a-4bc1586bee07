package com.zhihaoscm.service.resource.custom.reconciliation;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import com.zhihaoscm.domain.bean.ContractPageResponse;
import com.zhihaoscm.domain.meta.biz.CertificationDef;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.resource.form.reconciliation.*;
import com.zhihaoscm.service.resource.validator.customer.enterprise.CustomerEnterpriseValidator;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.Reconciliation;
import com.zhihaoscm.domain.bean.vo.ReconciliationVo;
import com.zhihaoscm.domain.meta.biz.ReconciliationDef;
import com.zhihaoscm.service.core.service.ReconciliationService;
import com.zhihaoscm.service.resource.validator.contract.ContractValidator;
import com.zhihaoscm.service.resource.validator.reconciliation.ReconciliationValidator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * <AUTHOR>
 * @create 2025/1/16
 */
@Tag(name = "对账管理", description = "对账管理API")
@RestController
@RequestMapping("/reconciliation/buy")
public class ReconciliationResource {

	@Autowired
	private ReconciliationService reconciliationService;

	@Autowired
	private ReconciliationValidator validator;

	@Autowired
	private ContractValidator contractValidator;

	@Autowired
	private CustomerEnterpriseValidator customerEnterpriseValidator;

	@Operation(summary = "分页查询客户端销售对账")
	@GetMapping(value = "/sale/paging")
	public ApiResponse<Page<ReconciliationVo>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "排序关键字") @RequestParam(value = "sortKey", required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(value = "sortOrder", required = false) String sortOrder,
			@Parameter(description = "合同名称或对账编号") @RequestParam(value = "param", required = false) String param,
			@Parameter(description = "货物名称") @RequestParam(value = "goodsName", required = false) String goodsName,
			@Parameter(description = "采购方名称") @RequestParam(value = "buyer", required = false) String buyer,
			@Parameter(description = "对账开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@Parameter(description = "对账结束时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
			@Parameter(description = "对账状态") @RequestParam(value = "states", required = false) List<Integer> states) {
		return new ApiResponse<>(
				PageUtil.convert(reconciliationService.customSalePaging(page,
						size, sortKey, sortOrder, param, goodsName, buyer,
						beginTime, endTime, states,
						CustomerContextHolder.getCustomerLoginVo()
								.getProxyAccount().getId(),
						CommonDef.AccountSource.CUSTOM.getCode())));
	}

	@Operation(summary = "分页查询客户端采购对账")
	@GetMapping(value = "/paging")
	public ApiResponse<Page<ReconciliationVo>> buyPaging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "排序关键字") @RequestParam(value = "sortKey", required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(value = "sortOrder", required = false) String sortOrder,
			@Parameter(description = "合同名称或对账编号") @RequestParam(value = "param", required = false) String param,
			@Parameter(description = "货物名称") @RequestParam(value = "goodsName", required = false) String goodsName,
			@Parameter(description = "销售方名称") @RequestParam(value = "seller", required = false) String seller,
			@Parameter(description = "对账开始时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@Parameter(description = "对账结束时间") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
			@Parameter(description = "对账状态") @RequestParam(value = "states", required = false) List<Integer> states) {
		return new ApiResponse<>(
				PageUtil.convert(reconciliationService.customBuyPaging(page,
						size, sortKey, sortOrder, param, goodsName, seller,
						beginTime, endTime, states,
						CustomerContextHolder.getCustomerLoginVo()
								.getProxyAccount().getId(),
						CommonDef.AccountSource.CUSTOM.getCode())));
	}

	@Operation(summary = "分页-根据订单id,类型查询对账列表")
	@GetMapping("/paging/order-id/type")
	public ApiResponse<Page<ReconciliationVo>> pagingFindByOrderIdAndType(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "排序关键字") @RequestParam(value = "sortKey", required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(value = "sortOrder", required = false) String sortOrder,
			@Parameter(description = "订单id") @RequestParam(value = "orderId") String orderId,
			@Parameter(description = "1 采购 2 销售") @RequestParam(value = "type") Integer type,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
		return new ApiResponse<>(PageUtil
				.convert(reconciliationService.pagingFindByOrderIdAndType(page,
						size, sortKey, sortOrder, orderId, type, beginTime,
						endTime, CustomerContextHolder.getCustomerLoginVo()
								.getProxyAccount().getId())));
	}

	@Operation(summary = "根据订单id和类型查询对账列表合计")
	@GetMapping("/total/order-id/type")
	public ApiResponse<BigDecimal> totalFindByOrderIdAndType(
			@Parameter(description = "订单id") @RequestParam(value = "orderId") String orderId,
			@Parameter(description = "1 采购 2 销售") @RequestParam(value = "type") Integer type,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beginTime,
			@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
		return new ApiResponse<>(
				reconciliationService
						.totalFindByOrderIdAndType(orderId, type, beginTime,
								endTime,
								CustomerContextHolder.getCustomerLoginVo()
										.getProxyAccount().getId())
						.orElse(null));
	}

	/**
	 * <AUTHOR>
	 */
	@Operation(summary = "付款管理-关联对账单下拉列表")
	@GetMapping("/selector-associated")
	public ApiResponse<Page<Reconciliation>> selectorAssociated(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "项目id") @RequestParam(required = false) String projectId,
			@Parameter(description = "合同编号") @RequestParam String contractId,
			@Parameter(description = "对账编号") @RequestParam(required = false) String reconciliationId) {
		return new ApiResponse<>(
				PageUtil.convert(reconciliationService.selectorAssociated(page,
						size, projectId, contractId, reconciliationId)));
	}

	@Operation(summary = "根据id查询对账单信息")
	@GetMapping("/vo/{id}")
	public ApiResponse<ReconciliationVo> findVoById(@PathVariable String id) {
		ReconciliationVo vo = reconciliationService.findVoById(id).orElse(null);
		return new ApiResponse<>(vo);
	}

	@Operation(summary = "是否显示保证金转货款那几个字段")
	@GetMapping("/validate/display")
	public ApiResponse<Integer> validateDisplay(
			@Parameter(description = "关联合同id") @RequestParam String contractId,
			@Parameter(description = "关联签收单ids") @RequestParam List<String> signReceiptIds) {
		return new ApiResponse<>(reconciliationService
				.validateDisplay(contractId, signReceiptIds).orElse(null));
	}

	@Operation(summary = "新增客户端销售对账")
	@PostMapping(value = "/sale")
	public ApiResponse<Reconciliation> create(
			@Validated @RequestBody ReconciliationBuyForm form) {
		return new ApiResponse<>(
				reconciliationService
						.createBuy(
								validator.validateCreateBuy(form,
										CommonDef.AccountSource.CUSTOM
												.getCode()),
								form.getDeliverGoodsList(),
								form.getDeliverGoodsVoList())
						.orElse(null));
	}

	@Operation(summary = "新增客户端采购对账")
	@PostMapping
	public ApiResponse<Reconciliation> create(
			@Validated @RequestBody ReconciliationForm form) {
		return new ApiResponse<>(reconciliationService.create(
				validator.validateCreate(form,
						CommonDef.AccountSource.CUSTOM.getCode()),
				form.getDeliverGoodsVoList()).orElse(null));
	}

	@Operation(summary = "修改客户端销售对账")
	@PutMapping("/sale/{id}")
	public ApiResponse<Reconciliation> update(@PathVariable("id") String id,
			@Validated @RequestBody ReconciliationUpdateBuyForm form) {
		Reconciliation reconciliation = validator.validateUpdateBuy(id, form,
				CommonDef.AccountSource.INNER.getCode());
		Reconciliation newReconciliation = reconciliationService
				.updateBuy(reconciliation, form.getDeliverGoodsList(),
						form.getDeliverGoodsVoList(), form.getSaveType(),
						CommonDef.AccountSource.CUSTOM.getCode())
				.orElse(null);
		return new ApiResponse<>(newReconciliation);
	}

	@Operation(summary = "修改客户端采购对账")
	@PutMapping("/{id}")
	public ApiResponse<Reconciliation> update(@PathVariable("id") String id,
			@Validated @RequestBody ReconciliationUpdateForm form) {
		Reconciliation reconciliation = validator.validateUpdate(id, form,
				CommonDef.AccountSource.CUSTOM.getCode());
		return new ApiResponse<>(
				reconciliationService
						.update(reconciliation, form.getDeliverGoodsVoList(),
								form.getSaveType(),
								CommonDef.AccountSource.CUSTOM.getCode())
						.orElse(null));
	}

	@Operation(summary = "客户端-采购对账-预对账完成之后新增修改采购对账")
	@PutMapping("/update/reconciliation/{id}")
	public ApiResponse<Reconciliation> updateReconciliation(
			@PathVariable("id") String id,
			@Validated @RequestBody ReconciliationPredForm form) {
		Reconciliation reconciliation = validator.validateUpdateReconciliation(
				id, form, CommonDef.AccountSource.CUSTOM.getCode());
		return new ApiResponse<>(reconciliationService.updateReconciliation(
				reconciliation, form.getDeliverGoodsVoList()).orElse(null));
	}

	@Operation(summary = "客户端-销售对账-预对账完成之后新增修改对账")
	@PutMapping("/update/sale/reconciliation/{id}")
	public ApiResponse<Reconciliation> updateBuyReconciliation(
			@PathVariable("id") String id,
			@Validated @RequestBody ReconciliationBuyPredForm form) {
		Reconciliation reconciliation = validator
				.validateUpdateBuyReconciliation(id, form,
						CommonDef.AccountSource.INNER.getCode());
		return new ApiResponse<>(reconciliationService.updateBuyReconciliation(
				reconciliation, form.getDeliverGoodsList(),
				form.getDeliverGoodsVoList()).orElse(null));
	}

	@Operation(summary = "删除客户端采购对账")
	@DeleteMapping("/{id}")
	public ApiResponse<Void> delete(@PathVariable String id) {
		reconciliationService.delete(validator.validateDelete(id));
		return new ApiResponse<>();
	}

	@Operation(summary = "删除客户端销售对账")
	@DeleteMapping("/sale/{id}")
	public ApiResponse<Void> deleteBuy(@PathVariable String id) {
		reconciliationService.deleteBuy(validator.validateDelete(id));
		return new ApiResponse<>();
	}

	@Operation(summary = "发起签署")
	@PutMapping("/initiate-signing/{id}")
	public ApiResponse<ContractPageResponse> initiateSigning(
			@PathVariable("id") String id) {
		customerEnterpriseValidator.validateSign(CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		Reconciliation reconciliation = validator.validateInitiateSigning(id,
				CommonDef.AccountSource.CUSTOM.getCode());
		return new ApiResponse<>(
				reconciliationService
						.initiateSign(reconciliation,
								CertificationDef.Origin.PC.getCode(),
								CommonDef.AccountSource.CUSTOM.getCode())
						.orElse(null));
	}

	@Operation(summary = "签署")
	@PutMapping("/signing/{id}")
	public ApiResponse<ContractPageResponse> signing(
			@PathVariable("id") String id) {
		customerEnterpriseValidator.validateSign(CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		Reconciliation reconciliation = validator.validateSigning(id,
				CommonDef.AccountSource.CUSTOM.getCode());
		return new ApiResponse<>(reconciliationService
				.signing(reconciliation, CertificationDef.Origin.PC.getCode())
				.orElse(null));
	}

	@Operation(summary = "驳回")
	@PostMapping("/reject/{id}")
	public ApiResponse<Void> reject(
			@Validated @RequestBody ReconciliationRejectForm form) {
		Reconciliation reconciliation = validator.validateReject(form,
				CommonDef.AccountSource.CUSTOM.getCode());
		reconciliationService.reject(reconciliation, Boolean.TRUE);
		return new ApiResponse<>();
	}

	@Operation(summary = "确认")
	@PutMapping("/confirm/{id}")
	public ApiResponse<Reconciliation> confirm(@PathVariable String id) {
		Reconciliation reconciliation = validator.validateConfirm(id);
		return new ApiResponse<>(
				reconciliationService.confirm(reconciliation).orElse(null));
	}

	@Operation(summary = "提交")
	@PutMapping("/submit/{id}")
	public ApiResponse<Reconciliation> submit(@PathVariable String id,
			@Parameter(description = "单据文件id") @RequestParam(required = false) Long fileId) {
		Reconciliation reconciliation = validator.validateSubmit(id,
				CommonDef.AccountSource.CUSTOM.getCode(), fileId);
		return new ApiResponse<>(
				reconciliationService.submit(reconciliation).orElse(null));
	}

	/**
	 * <AUTHOR>
	 */
	@Operation(summary = "关联对账单")
	@PostMapping("/contract/{id}")
	public ApiResponse<List<Reconciliation>> findByContractId(
			@PathVariable String id,
			@RequestBody(required = false) ReconciliationAssociaForm form) {
		contractValidator.validateExist(id);
		return new ApiResponse<>(reconciliationService.findByContractId(id,
				form, ReconciliationDef.Type.SELL.getCode()));
	}

	@Operation(summary = "线上发起作废")
	@PutMapping("/invalid/{id}")
	public ApiResponse<ContractPageResponse> invalid(@PathVariable String id,
			@Parameter(description = "作废原因") @RequestParam String reasons) {
		Reconciliation reconciliation = validator.validateInvalid(id,
				CommonDef.UserType.OUTER.getCode(), false, reasons, null);
		return new ApiResponse<>(reconciliationService
				.invalid(reconciliation, CommonDef.UserType.OUTER.getCode())
				.orElse(null));
	}

	@Operation(summary = "线下发起作废")
	@PutMapping("/invalid-off-line/{id}")
	public ApiResponse<Reconciliation> invalidOffLine(@PathVariable String id,
			@Parameter(description = "作废原因") @RequestParam String reasons,
			@Parameter(description = "作废文件") @RequestParam(required = false) Long fileId) {
		Reconciliation reconciliation = validator.validateInvalid(id,
				CommonDef.UserType.OUTER.getCode(), false, reasons, fileId);
		return new ApiResponse<>(
				reconciliationService
						.invalidOffLine(reconciliation,
								CommonDef.UserType.OUTER.getCode())
						.orElse(null));
	}

	@Operation(summary = "确认作废")
	@PutMapping("/confirm-invalid/{id}")
	public ApiResponse<Reconciliation> confirmInvalid(@PathVariable String id) {
		Reconciliation reconciliation = validator.validateInvalidConfirm(id,
				CommonDef.UserType.OUTER.getCode(), null);
		return new ApiResponse<>(reconciliationService
				.confirmInvalid(reconciliation).orElse(null));
	}

	@Operation(summary = "驳回作废")
	@PutMapping("/revert-invalid/{id}")
	public ApiResponse<Reconciliation> revertInvalid(@PathVariable String id,
			@Parameter(description = "驳回原因") @RequestParam String reasons) {
		Reconciliation reconciliation = validator.validateInvalidConfirm(id,
				CommonDef.UserType.OUTER.getCode(), reasons);
		return new ApiResponse<>(reconciliationService
				.revertInvalid(reconciliation).orElse(null));
	}

	@Operation(summary = "下载/预览单据")
	@GetMapping("/download/{id}")
	public void download(HttpServletResponse response,
			@PathVariable("id") String id) throws IOException {
		validator.validateExist(id);
		reconciliationService.downloadPdf(response, id);
	}

}
