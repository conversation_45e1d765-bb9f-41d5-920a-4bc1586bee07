package com.zhihaoscm.service.core.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.api.util.multipart.file.CustomMultipartFile;
import com.zhihaoscm.common.api.util.multipart.file.MultipartFileUtils;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.ContractPageResponse;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.StocktakingCountVo;
import com.zhihaoscm.domain.bean.vo.StocktakingVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.pdf.StockCheckPdfUtil;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.StocktakingMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.AdminSealService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 盘点 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@Slf4j
@Service
public class StocktakingServiceImpl
		extends MpStringIdBaseServiceImpl<Stocktaking, StocktakingMapper>
		implements StocktakingService {

	public StocktakingServiceImpl(StocktakingMapper repository) {
		super(repository);
	}

	@Autowired
	private ProjectService projectService;
	@Autowired
	private ContractService contractService;
	@Autowired
	private StocktakingDetailService stocktakingDetailService;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private ContractRecordService contractRecordService;
	@Autowired
	private FileService fileService;
	@Autowired
	private AdminSealService adminSealService;
	@Autowired
	private InboundService inboundService;
	@Autowired
	private OutboundService outboundService;
	@Autowired
	private WarehouseGoodsInfoService warehouseGoodsInfoService;
	@Autowired
	private GoodsService goodsService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private StorageInceptionInboundDetailService storageInceptionInboundDetailService;
	@Autowired
	private StorageInceptionOutboundDetailService storageInceptionOutboundDetailService;
	@Autowired
	private StockProjectService stockProjectService;
	@Autowired
	private StockContractService stockContractService;
	@Autowired
	private SupervisionStorageInceptionDetailService supervisionStorageInceptionDetailService;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	@Override
	public Page<StocktakingVo> paging(Integer page, Integer size,
			String sortKey, String sortOrder, String param,
			List<String> warehouseIds, List<Long> storageIds,
			List<Integer> type, LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, Boolean hasAll, Long userId, Integer module) {
		LambdaQueryWrapper<Stocktaking> wrapper = Wrappers
				.lambdaQuery(Stocktaking.class)
				.eq(Stocktaking::getDel, CommonDef.Symbol.NO.getCode());
		if (Objects.nonNull(module)) {
			wrapper.eq(Stocktaking::getModule, module);
		}
		// 盘点编号或项目名称
		if (Objects.nonNull(param)) {
			List<String> projectIds;
			if (StocktakingDef.Module.JXC.match(module)) {
				projectIds = projectService.findByNameLike(param).stream()
						.map(Project::getId).distinct().toList();
			} else {
				projectIds = stockProjectService.findByNameLike(param).stream()
						.map(StockProject::getId).distinct().toList();
			}
			wrapper.and(x -> x.like(Stocktaking::getId, param).or().in(
					CollectionUtils.isNotEmpty(projectIds),
					Stocktaking::getProjectId, projectIds));
		}
		// 所属仓库/库位
		if (CollectionUtils.isNotEmpty(warehouseIds)
				&& CollectionUtils.isNotEmpty(storageIds)) {
			wrapper.and(e -> e.in(Stocktaking::getWarehouseId, warehouseIds)
					.or().in(Stocktaking::getStorageId, storageIds));
		} else {
			if (CollectionUtils.isNotEmpty(warehouseIds)) {
				wrapper.in(Stocktaking::getWarehouseId, warehouseIds);
			}
			if (CollectionUtils.isNotEmpty(storageIds)) {
				wrapper.in(Stocktaking::getStorageId, storageIds);
			}
		}
		// 类型
		wrapper.in(CollectionUtils.isNotEmpty(type), Stocktaking::getType,
				type);
		// 开始时间
		wrapper.ge(Objects.nonNull(beginTime), Stocktaking::getStocktakingDate,
				beginTime);
		// 结束时间
		wrapper.le(Objects.nonNull(endTime), Stocktaking::getStocktakingDate,
				endTime);
		// 卖方未签署
		List<Integer> toBeSignedList = List.of(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
				BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode());
		// 没有传入状态时
		if (CollectionUtils.isEmpty(states)) {
			// 除了草稿和已驳回查询发起方是自己的 其他根据状态查询
			wrapper.and(x -> x.in(Stocktaking::getState,
					StocktakingDef.State.SIGNING.getCode(),
					StocktakingDef.State.CONFIRMING.getCode(),
					StocktakingDef.State.FINISHED.getCode(),
					StocktakingDef.State.INVALIDING.getCode(),
					StocktakingDef.State.INVALID.getCode(),
					StocktakingDef.State.TO_BE_INVENTORIED.getCode())
					.or(y -> y
							.eq(Stocktaking::getInitiator,
									CommonDef.AccountSource.INNER.getCode())
							.in(Stocktaking::getState,
									StocktakingDef.State.DRAFT.getCode(),
									StocktakingDef.State.REJECTED.getCode(),
									StocktakingDef.State.TO_BE_INITIATE
											.getCode())));
		} else {
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 草稿状态
					if (StocktakingDef.State.DRAFT.match(state)) {
						x.or(y -> y
								.eq(Stocktaking::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Stocktaking::getState,
										StocktakingDef.State.DRAFT.getCode()));
					}
					// 已驳回状态
					else if (StocktakingDef.State.REJECTED.match(state)) {
						x.or(y -> y
								.eq(Stocktaking::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Stocktaking::getState,
										StocktakingDef.State.REJECTED
												.getCode()));
					}
					// 待确认状态
					else if (StocktakingDef.State.WAIT_CONFIRM.match(state)) {
						x.or(y -> y
								.eq(Stocktaking::getInitiator,
										CommonDef.AccountSource.CUSTOM
												.getCode())
								.eq(Stocktaking::getState,
										StocktakingDef.State.CONFIRMING
												.getCode()));
					}
					// 确认中状态
					else if (StocktakingDef.State.CONFIRMING.match(state)) {
						x.or(y -> y
								.eq(Stocktaking::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Stocktaking::getState,
										StocktakingDef.State.CONFIRMING
												.getCode()));
					}
					// 签署中状态 签署中状态 并且销售方已签署
					else if (StocktakingDef.State.SIGNING.match(state)) {
						x.or(y -> y.eq(Stocktaking::getSignStatus,
								BusinessContractDef.CommonSignState.SUPPLY_SIGNED
										.getCode())
								.eq(Stocktaking::getState,
										StocktakingDef.State.SIGNING
												.getCode()));
					}
					// 待签署状态 签署中状态 并且买方已签署或者双方未签署
					else if (StocktakingDef.State.TO_BE_SIGNED.match(state)) {
						x.or(y -> y
								.in(Stocktaking::getSignStatus, toBeSignedList)
								.eq(Stocktaking::getState,
										StocktakingDef.State.SIGNING
												.getCode()));
					}
					// 已完成状态
					else if (StocktakingDef.State.FINISHED.match(state)) {
						x.or(y -> y.eq(Stocktaking::getState,
								StocktakingDef.State.FINISHED.getCode()));
					}
					// 待发起
					else if (StocktakingDef.State.TO_BE_INITIATE.match(state)) {
						x.or(y -> y.eq(Stocktaking::getState,
								StocktakingDef.State.TO_BE_INITIATE.getCode()));
					}
					// 作废中状态
					else if (StocktakingDef.State.INVALIDING.match(state)) {
						x.or(y -> y.eq(Stocktaking::getState,
								StocktakingDef.State.INVALIDING.getCode()));
					}
					// 已作废状态
					else if (StocktakingDef.State.INVALID.match(state)) {
						x.or(y -> y.eq(Stocktaking::getState,
								StocktakingDef.State.INVALID.getCode()));
					}
					// 待盘点状态
					else if (StocktakingDef.State.TO_BE_INVENTORIED
							.match(state)) {
						x.or(y -> y.eq(Stocktaking::getState,
								StocktakingDef.State.TO_BE_INVENTORIED
										.getCode()));
					}
				}
			});

		}
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (!hasAll) {
			List<String> projectIdList;
			if (StocktakingDef.Module.JXC.match(module)) {
				// 处理人是自己在的
				projectIdList = projectService.findByUserId(userId, null);
			} else {
				// 处理人是自己在的
				projectIdList = stockProjectService.findByUserId(userId, null);
			}
			if (CollectionUtils.isNotEmpty(projectIdList)) {
				wrapper.in(Stocktaking::getProjectId, projectIdList);
			} else {
				return Page.of(page, size, 0);
			}
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + ", id DESC");
		} else {
			// 默认按更新时间倒序排列
			wrapper.last("ORDER BY updated_time DESC");
		}
		Page<Stocktaking> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<StocktakingVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<StocktakingVo> customPaging(Integer page, Integer size,
			String sortKey, String sortOrder, String param,
			String warehouseName, List<Integer> type, LocalDateTime beginTime,
			LocalDateTime endTime, List<Integer> states, Long customerId,
			Integer origin, Integer module) {
		LambdaQueryWrapper<Stocktaking> wrapper = Wrappers
				.lambdaQuery(Stocktaking.class)
				.eq(Stocktaking::getDel, CommonDef.Symbol.NO.getCode())
				.eq(Stocktaking::getPurchaserId, customerId);
		if (Objects.nonNull(module)) {
			wrapper.eq(Stocktaking::getModule, module);
		}
		// 盘点编号或合同名称
		if (Objects.nonNull(param)) {
			List<String> contractIds;
			if (StocktakingDef.Module.JXC.match(module)) {
				contractIds = contractService.findByNameLike(param).stream()
						.map(Contract::getId).distinct().toList();
			} else {
				contractIds = stockContractService.findByNameLike(param)
						.stream().map(StockContract::getId).distinct().toList();
			}
			wrapper.and(x -> x.like(Stocktaking::getId, param).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					Stocktaking::getContractId, contractIds));
		}
		// 所属仓库
		if (StringUtils.isNotBlank(warehouseName)) {
			wrapper.and(x -> x
					.like(Stocktaking::getWarehouseName, warehouseName).or()
					.like(Stocktaking::getStorageName, warehouseName));
		}
		// 类型
		wrapper.in(CollectionUtils.isNotEmpty(type), Stocktaking::getType,
				type);
		// 开始时间
		wrapper.ge(Objects.nonNull(beginTime), Stocktaking::getStocktakingDate,
				beginTime);
		// 结束时间
		wrapper.le(Objects.nonNull(endTime), Stocktaking::getStocktakingDate,
				endTime);
		// 买方未签署
		List<Integer> toBeSignedList = List.of(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
				BusinessContractDef.CommonSignState.SUPPLY_SIGNED.getCode());
		// 只有销售项目才需要判断状态
		// 查询所有数据，则查询除了pc端草稿和pc端驳回状态的所有状态
		if (CollectionUtils.isEmpty(states)) {
			// 除了草稿和已驳回,待发起 查询发起方是自己的 其他根据状态查询
			wrapper.and(x -> x
					.in(Stocktaking::getState,
							StocktakingDef.State.SIGNING.getCode(),
							StocktakingDef.State.CONFIRMING.getCode(),
							StocktakingDef.State.FINISHED.getCode(),
							StocktakingDef.State.INVALIDING.getCode(),
							StocktakingDef.State.INVALID.getCode())
					.or(y -> y.eq(Stocktaking::getInitiator, origin).in(
							Stocktaking::getState,
							StocktakingDef.State.DRAFT.getCode(),
							StocktakingDef.State.REJECTED.getCode())));
		} else {
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 草稿状态
					if (StocktakingDef.State.DRAFT.match(state)) {
						x.or(y -> y.eq(Stocktaking::getInitiator, origin).eq(
								Stocktaking::getState,
								StocktakingDef.State.DRAFT.getCode()));
					}
					// 已驳回状态
					else if (StocktakingDef.State.REJECTED.match(state)) {
						x.or(y -> y.eq(Stocktaking::getInitiator, origin).eq(
								Stocktaking::getState,
								StocktakingDef.State.REJECTED.getCode()));
					}
					// 待确认状态 对方发起的确认中的状态
					else if (StocktakingDef.State.WAIT_CONFIRM.match(state)) {
						x.or(y -> y
								.eq(Stocktaking::getInitiator,
										CommonDef.AccountSource.INNER.getCode())
								.eq(Stocktaking::getState,
										StocktakingDef.State.CONFIRMING
												.getCode()));
					}
					// 确认中状态
					else if (StocktakingDef.State.CONFIRMING.match(state)) {
						x.or(y -> y.eq(Stocktaking::getInitiator, origin).eq(
								Stocktaking::getState,
								StocktakingDef.State.CONFIRMING.getCode()));
					}
					// 签署中状态 签署中状态 并且买方已签署
					else if (StocktakingDef.State.SIGNING.match(state)) {
						x.or(y -> y.eq(Stocktaking::getSignStatus,
								BusinessContractDef.CommonSignState.BUYER_SIGNED
										.getCode())
								.eq(Stocktaking::getState,
										StocktakingDef.State.SIGNING
												.getCode()));
					}
					// 待签署状态 签署中状态 并且卖方已签署或者双方未签署
					else if (StocktakingDef.State.TO_BE_SIGNED.match(state)) {
						x.or(y -> y
								.in(Stocktaking::getSignStatus, toBeSignedList)
								.eq(Stocktaking::getState,
										StocktakingDef.State.SIGNING
												.getCode()));
					}
					// 已完成状态
					else if (StocktakingDef.State.FINISHED.match(state)) {
						x.or(y -> y.eq(Stocktaking::getState,
								StocktakingDef.State.FINISHED.getCode()));
					}
					// 作废中状态
					else if (StocktakingDef.State.INVALIDING.match(state)) {
						x.or(y -> y.eq(Stocktaking::getState,
								StocktakingDef.State.INVALIDING.getCode()));
					}
					// 已作废状态
					else if (StocktakingDef.State.INVALID.match(state)) {
						x.or(y -> y.eq(Stocktaking::getState,
								StocktakingDef.State.INVALID.getCode()));
					}
				}
			});
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("order by " + sortKey + " " + sortOrder + ", id DESC");
		} else {
			// 默认按更新时间倒序排列
			wrapper.last("ORDER BY updated_time DESC");
		}
		Page<Stocktaking> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<StocktakingVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public List<Stocktaking> findByPurchaserIdAndState(Long customId,
			Integer state, Integer module) {
		LambdaQueryWrapper<Stocktaking> wrapper = Wrappers
				.lambdaQuery(Stocktaking.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(customId), Stocktaking::getPurchaserId,
				customId)
				.eq(Objects.nonNull(state), Stocktaking::getState, state)
				.eq(Objects.nonNull(module), Stocktaking::getModule, module);
		return repository.selectList(wrapper);
	}

	@Override
	public Optional<StocktakingVo> findVoById(String id) {
		return this.findOne(id).map(this::packVo);
	}

	@Override
	public List<Stocktaking> findByProjectId(String projectId,
			LocalDateTime startTime, LocalDateTime endTime) {
		LambdaQueryWrapper<Stocktaking> wrapper = Wrappers
				.lambdaQuery(Stocktaking.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(projectId), Stocktaking::getProjectId,
				projectId);
		// 开始时间
		wrapper.ge(Objects.nonNull(startTime), Stocktaking::getStocktakingDate,
				startTime);
		// 结束时间
		wrapper.le(Objects.nonNull(endTime), Stocktaking::getStocktakingDate,
				endTime);
		return repository.selectList(wrapper);
	}

	@Override
	public List<StocktakingDetail> findByProjectIdAndWarehouseId(
			String projectId, String warehouseId, Long storageId) {
		Project project = projectService.findOne(projectId).orElse(null);
		String goodsName = null;
		String unit = null;
		if (Objects.nonNull(project)) {
			goodsName = project.getGoodsName();
			Goods goods = goodsService.findOne(project.getGoodsId())
					.orElse(null);
			if (Objects.nonNull(goods)) {
				unit = goods.getUnit();
			}
		}
		// 最终的货物信息列表
		List<StocktakingDetail> stocktakingDetailList = new ArrayList<>();
		// 期初入库信息
		List<StorageInceptionInboundDetail> storageInceptionInboundDetailList = storageInceptionInboundDetailService
				.find(projectId, warehouseId, storageId);
		// 期初入库根据规格型号进行分组
		Map<String, List<StorageInceptionInboundDetail>> storageInWarehouseGoodsInfoMap = storageInceptionInboundDetailList
				.stream().collect(Collectors
						.groupingBy(StorageInceptionInboundDetail::getModel));
		// 期初出库信息
		List<StorageInceptionOutboundDetail> storageInceptionOutboundDetailList = storageInceptionOutboundDetailService
				.find(projectId, warehouseId, storageId);

		// 期初出库根据规格型号进行分组
		Map<String, List<StorageInceptionOutboundDetail>> storageOutWarehouseGoodsInfoMap = storageInceptionOutboundDetailList
				.stream().collect(Collectors
						.groupingBy(StorageInceptionOutboundDetail::getModel));
		// 入库信息
		List<Inbound> inboundList = inboundService
				.findByProjectIdAndWarehouseId(projectId, warehouseId,
						storageId,
						List.of(InboundDef.Status.INBOUNDED.getCode()),
						InboundDef.Type.JXC.getCode());
		List<String> inboundIds = inboundList.stream().map(Inbound::getId)
				.distinct().toList();
		// 出库信息
		List<Outbound> outboundList = outboundService
				.findByProjectIdAndWarehouseId(projectId, warehouseId,
						storageId,
						List.of(OutboundDef.Status.OUTBOUND.getCode()),
						InboundDef.Type.JXC.getCode());
		List<String> outboundIds = outboundList.stream().map(Outbound::getId)
				.distinct().toList();

		// 所有入库货物信息
		List<WarehouseGoodsInfo> inWarehouseGoodsInfoList = warehouseGoodsInfoService
				.findByRelateIds(inboundIds);
		// 将入库货物信息根据规格型号进行分组
		Map<String, List<WarehouseGoodsInfo>> inWarehouseGoodsInfoMap = inWarehouseGoodsInfoList
				.stream()
				.collect(Collectors.groupingBy(WarehouseGoodsInfo::getModel));
		// 所有出库货物信息
		List<WarehouseGoodsInfo> outWarehouseGoodsInfoList = warehouseGoodsInfoService
				.findByRelateIds(outboundIds);
		// 将出库货物信息根据规格型号进行分组
		Map<String, List<WarehouseGoodsInfo>> outWarehouseGoodsInfoMap = outWarehouseGoodsInfoList
				.stream()
				.collect(Collectors.groupingBy(WarehouseGoodsInfo::getModel));
		// 将storageInWarehouseGoodsInfoMap 和inWarehouseGoodsInfoMap
		// 的model合并成一个List
		List<String> modelList = Stream.concat(
				storageInWarehouseGoodsInfoMap
						.values().stream().flatMap(List::stream)
						.map(StorageInceptionInboundDetail::getModel),
				inWarehouseGoodsInfoMap.values().stream().flatMap(List::stream)
						.map(WarehouseGoodsInfo::getModel))
				.distinct().toList();
		if (CollectionUtils.isNotEmpty(modelList)) {

			// 根据规格型号循环入库货物信息
			for (String model : modelList) {

				StocktakingDetail stocktakingDetail = new StocktakingDetail();
				// 设置规格型号
				stocktakingDetail.setModel(model);
				// 设置货物名称
				stocktakingDetail.setGoodsName(goodsName);
				// 设置单位
				stocktakingDetail.setUnit(unit);
				BigDecimal bookQuantity = BigDecimal.ZERO;
				// 期初入库数量/重量
				BigDecimal inboundWeight1 = BigDecimal.ZERO;
				// 已完成入库数量/重量
				BigDecimal inboundWeight2 = BigDecimal.ZERO;
				// 期初出库数量/重量
				BigDecimal outboundWeight1 = BigDecimal.ZERO;
				// 已完成出库数量/重量
				BigDecimal outboundWeight2 = BigDecimal.ZERO;
				// 规格型号对应的期初入库信息
				List<StorageInceptionInboundDetail> storageInceptionInboundDetails = storageInWarehouseGoodsInfoMap
						.get(model);
				// 期初入库信息
				if (CollectionUtils
						.isNotEmpty(storageInceptionInboundDetails)) {
					inboundWeight1 = storageInceptionInboundDetails.stream()
							.map(StorageInceptionInboundDetail::getQuantity)
							.reduce(BigDecimal.ZERO, BigDecimal::add);
				}
				// 规格型号对应的期初出库信息
				List<StorageInceptionOutboundDetail> storageInceptionOutboundDetails = storageOutWarehouseGoodsInfoMap
						.get(model);
				// 期初出库信息
				if (CollectionUtils
						.isNotEmpty(storageInceptionOutboundDetails)) {
					outboundWeight1 = storageInceptionOutboundDetails.stream()
							.map(StorageInceptionOutboundDetail::getQuantity)
							.reduce(BigDecimal.ZERO, BigDecimal::add);
				}
				// 规格型号对应的入库信息
				List<WarehouseGoodsInfo> inWarehouseGoodsInfos = inWarehouseGoodsInfoMap
						.get(model);
				// 已完成入库信息
				if (CollectionUtils.isNotEmpty(inWarehouseGoodsInfos)) {
					inboundWeight2 = inWarehouseGoodsInfos.stream()
							.map(WarehouseGoodsInfo::getInboundWeight)
							.reduce(BigDecimal.ZERO, BigDecimal::add);

				}
				// 规格型号对应的出库信息
				List<WarehouseGoodsInfo> outWarehouseGoodsInfos = outWarehouseGoodsInfoMap
						.get(model);
				// 已完成出库数量/重量
				if (CollectionUtils.isNotEmpty(outWarehouseGoodsInfos)) {
					outboundWeight2 = outWarehouseGoodsInfos.stream()
							.map(WarehouseGoodsInfo::getInboundWeight)
							.reduce(BigDecimal.ZERO, BigDecimal::add);
				}
				// 账面数量=期初入库数量/重量+已完成入库数量/重量-期初出库数量/重量-已完成出库数量/重量
				bookQuantity = bookQuantity.add(inboundWeight1)
						.subtract(outboundWeight1).add(inboundWeight2)
						.subtract(outboundWeight2);
				// 设置账面数量
				stocktakingDetail.setBookQuantity(bookQuantity);
				stocktakingDetailList.add(stocktakingDetail);

			}
		}

		// 将账面数量大于0的数据返回出去
		return stocktakingDetailList.stream().filter(detail -> detail
				.getBookQuantity().compareTo(BigDecimal.ZERO) > 0).toList();
	}

	@Override
	public List<StocktakingDetail> findByStockProjectIdAndWarehouseId(
			String projectId, String warehouseId, Long storageId) {
		StockProject project = stockProjectService.findOne(projectId)
				.orElse(null);
		String goodsName = null;
		String unit = null;
		if (Objects.nonNull(project)) {
			goodsName = project.getGoodsName();
			Goods goods = goodsService.findOne(project.getGoodsId())
					.orElse(null);
			if (Objects.nonNull(goods)) {
				unit = goods.getUnit();
			}
		}
		// 最终的货物信息列表
		List<StocktakingDetail> stocktakingDetailList = new ArrayList<>();
		// 仓储监管-仓储期初明细信息
		List<SupervisionStorageInceptionDetail> supervisionStorageInceptionDetailList = supervisionStorageInceptionDetailService
				.find(projectId, warehouseId, storageId);
		// 仓储监管期初 根据规格型号进行分组
		Map<String, List<SupervisionStorageInceptionDetail>> supervisionStorageInceptionDetailMap = supervisionStorageInceptionDetailList
				.stream().collect(Collectors.groupingBy(
						SupervisionStorageInceptionDetail::getModel));

		// 仓储监管入库信息
		List<Inbound> inboundList = inboundService
				.findByProjectIdAndWarehouseId(projectId, warehouseId,
						storageId,
						List.of(InboundDef.Status.INBOUNDED.getCode()),
						InboundDef.Type.STORAGE.getCode());
		List<String> inboundIds = inboundList.stream().map(Inbound::getId)
				.distinct().toList();
		// 仓储监管出库信息
		List<Outbound> outboundList = outboundService
				.findByProjectIdAndWarehouseId(projectId, warehouseId,
						storageId,
						List.of(OutboundDef.Status.OUTBOUND.getCode()),
						InboundDef.Type.STORAGE.getCode());
		List<String> outboundIds = outboundList.stream().map(Outbound::getId)
				.distinct().toList();

		// 所有入库货物信息
		List<WarehouseGoodsInfo> inWarehouseGoodsInfoList = warehouseGoodsInfoService
				.findByRelateIds(inboundIds);
		// 将入库货物信息根据规格型号进行分组
		Map<String, List<WarehouseGoodsInfo>> inWarehouseGoodsInfoMap = inWarehouseGoodsInfoList
				.stream()
				.collect(Collectors.groupingBy(WarehouseGoodsInfo::getModel));
		// 所有出库货物信息
		List<WarehouseGoodsInfo> outWarehouseGoodsInfoList = warehouseGoodsInfoService
				.findByRelateIds(outboundIds);
		// 将出库货物信息根据规格型号进行分组
		Map<String, List<WarehouseGoodsInfo>> outWarehouseGoodsInfoMap = outWarehouseGoodsInfoList
				.stream()
				.collect(Collectors.groupingBy(WarehouseGoodsInfo::getModel));
		// 将storageInWarehouseGoodsInfoMap 和inWarehouseGoodsInfoMap
		// 的model合并成一个List
		List<String> modelList = Stream.concat(
				supervisionStorageInceptionDetailMap
						.values().stream().flatMap(List::stream)
						.map(SupervisionStorageInceptionDetail::getModel),
				inWarehouseGoodsInfoMap.values().stream().flatMap(List::stream)
						.map(WarehouseGoodsInfo::getModel))
				.distinct().toList();
		if (CollectionUtils.isNotEmpty(modelList)) {

			// 根据规格型号循环入库货物信息
			for (String model : modelList) {
				StocktakingDetail stocktakingDetail = new StocktakingDetail();
				// 设置规格型号
				stocktakingDetail.setModel(model);
				// 设置货物名称
				stocktakingDetail.setGoodsName(goodsName);
				// 设置单位
				stocktakingDetail.setUnit(unit);
				BigDecimal bookQuantity = BigDecimal.ZERO;
				// 仓储监管-期初库存数量
				BigDecimal stockWeight = BigDecimal.ZERO;
				// 已完成入库数量/重量
				BigDecimal inboundWeight2 = BigDecimal.ZERO;
				// 已完成出库数量/重量
				BigDecimal outboundWeight2 = BigDecimal.ZERO;
				// 规格型号对应的仓储监管期初信息
				List<SupervisionStorageInceptionDetail> supervisionStorageInceptionDetails = supervisionStorageInceptionDetailMap
						.get(model);
				// 仓储监管-仓储期初信息
				if (CollectionUtils
						.isNotEmpty(supervisionStorageInceptionDetails)) {
					stockWeight = supervisionStorageInceptionDetails.stream()
							.map(SupervisionStorageInceptionDetail::getInventoryQuantity)
							.filter(Objects::nonNull)
							.reduce(BigDecimal.ZERO, BigDecimal::add);
				}

				// 规格型号对应的入库信息
				List<WarehouseGoodsInfo> inWarehouseGoodsInfos = inWarehouseGoodsInfoMap
						.get(model);
				// 已完成入库信息
				if (CollectionUtils.isNotEmpty(inWarehouseGoodsInfos)) {
					inboundWeight2 = inWarehouseGoodsInfos.stream()
							.map(WarehouseGoodsInfo::getInboundWeight)
							.reduce(BigDecimal.ZERO, BigDecimal::add);

				}
				// 规格型号对应的出库信息
				List<WarehouseGoodsInfo> outWarehouseGoodsInfos = outWarehouseGoodsInfoMap
						.get(model);
				// 已完成出库数量/重量
				if (CollectionUtils.isNotEmpty(outWarehouseGoodsInfos)) {
					outboundWeight2 = outWarehouseGoodsInfos.stream()
							.map(WarehouseGoodsInfo::getInboundWeight)
							.reduce(BigDecimal.ZERO, BigDecimal::add);
				}
				// 账面数量=期初库存数量/重量+已完成入库数量/重量-已完成出库数量/重量
				bookQuantity = bookQuantity.add(stockWeight).add(inboundWeight2)
						.subtract(outboundWeight2);
				// 设置账面数量
				stocktakingDetail.setBookQuantity(bookQuantity);
				stocktakingDetailList.add(stocktakingDetail);

			}
		}

		// 将账面数量大于0的数据返回出去
		return stocktakingDetailList.stream().filter(detail -> detail
				.getBookQuantity().compareTo(BigDecimal.ZERO) > 0).toList();
	}

	@Override
	public List<Stocktaking> findUnfinished(String projectId, Integer module) {
		LambdaQueryWrapper<Stocktaking> queryWrapper = Wrappers
				.lambdaQuery(Stocktaking.class);
		queryWrapper.eq(Stocktaking::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Stocktaking::getProjectId, projectId);
		queryWrapper.eq(Objects.nonNull(module), Stocktaking::getModule,
				module);
		queryWrapper.ne(Stocktaking::getState,
				StocktakingDef.State.FINISHED.getCode());
		return repository.selectList(queryWrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Stocktaking> create(Stocktaking stocktaking,
			List<StocktakingDetail> stocktakingDetails) {
		String projectCode = "";
		if (StocktakingDef.Module.JXC.match(stocktaking.getModule())) {
			Project project = projectService.findOne(stocktaking.getProjectId())
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30152013));
			projectCode = project.getCode();
		}
		if (StocktakingDef.Module.STORAGE.match(stocktaking.getModule())) {
			StockProject stockProject = stockProjectService
					.findOne(stocktaking.getProjectId())
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30152013));
			projectCode = stockProject.getCode();
		}
		// 所属项目编号+6（固定）+5（固定）+自增数（4位）
		String id = AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				projectCode, RedisKeys.Cache.STOCKTAKING_CODE_GENERATOR,
				AutoCodeDef.BusinessRuleCode.STOCKTAKING.getCode(), 4,
				AutoCodeDef.DATE_TYPE.yy);
		stocktaking.setId(id);
		if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
				.isNotEmpty(stocktakingDetails)) {
			for (StocktakingDetail stocktakingDetail : stocktakingDetails) {
				// 设置盘点明细的盘点编号
				stocktakingDetail.setStocktakingId(id);
			}
			// 批量创建盘点明细记录
			stocktakingDetailService.batchCreate(stocktakingDetails);
		}
		Stocktaking result = super.create(stocktaking);
		if (StocktakingDef.State.CONFIRMING.match(result.getState())
				&& StocktakingDef.SignType.OFFLINE
						.match(result.getSignType())) {
			this.sendNotice(result,
					wxSubscriptionProperties.getUnConfirmStocktakingCode(),
					MessageFormat.format(
							UserMessageConstants.STOCKTAKING_UNCONFIRMED_TEMPLATE,
							result.getId()));
		}

		return Optional.of(result);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Stocktaking> update(Stocktaking stocktaking,
			List<StocktakingDetail> stocktakingDetailList) {
		// 之前关联的盘点明细
		List<StocktakingDetail> details = stocktakingDetailService
				.findByStocktakingIds(List.of(stocktaking.getId()));
		// 删除对应的盘点明细信息
		if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
				.isNotEmpty(details)) {
			for (StocktakingDetail stocktakingDetail : details) {
				// 物理删除
				stocktakingDetailService.deleteTure(stocktakingDetail.getId());
			}
		}
		// 批量创建盘点明细列表
		if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
				.isNotEmpty(stocktakingDetailList)) {
			for (StocktakingDetail stocktakingDetail : stocktakingDetailList) {
				// 设置盘点明细的盘点编号
				stocktakingDetail.setStocktakingId(stocktaking.getId());
			}
			// 批量创建盘点明细记录
			stocktakingDetailService.batchCreate(stocktakingDetailList);
		}
		return Optional.of(super.updateAllProperties(stocktaking));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(Stocktaking stocktaking,
			List<StocktakingDetail> stocktakingDetails) {
		// 删除盘点记录信息
		super.delete(stocktaking.getId());
		// 删除对应的盘点明细信息
		if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
				.isNotEmpty(stocktakingDetails)) {
			for (StocktakingDetail stocktakingDetail : stocktakingDetails) {
				stocktakingDetailService.delete(stocktakingDetail.getId());
			}
		}
	}

	@Override
	public Optional<ContractPageResponse> signing(Stocktaking stocktaking,
			Integer origin) {
		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				stocktaking.getPurchaserEnterprise().getSignMobile(),
				stocktaking.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}
		return contractRecordService.sign(stocktaking.getId(),
				PurchaseContractDef.CorrelationTable.STOCKTAKING, origin);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<ContractPageResponse> initiateSign(Stocktaking resource,
			Integer origin, Integer initiateType) {
		Long fileId = this.getFileId(resource);
		if (Objects.nonNull(fileId)) {
			// 设置盘点单据文件id
			resource.setStocktakingFileId(fileId);
			this.updateAllProperties(resource);
		} else {
			log.info("生成pdf文件失败");
			return Optional.empty();
		}
		// 设置供应链签署人
		String name = "";
		switch (CommonDef.AccountSource.from(initiateType)) {
			case CUSTOM -> {
				name = resource.getPurchaserEnterprise().getName() + "盘点单";
				User user1;
				if (StocktakingDef.Module.JXC.match(resource.getModule())) {
					user1 = adminSealService
							.findByType(SignerSettings.billType.stocktaking)
							.orElse(null);
				} else {
					user1 = adminSealService
							.findByType(
									SignerSettings.billType.stockStocktaking)
							.orElse(null);
				}
				if (Objects.nonNull(user1)) {
					resource.setSupplierSigner(user1.getName());
					resource.setSupplierSignerId(user1.getId());
				}
			}
			case INNER -> {
				name = resource.getSellerEnterprise().getName() + "盘点单";
				User user = UserContextHolder.getUser();
				if (Objects.nonNull(user)) {
					resource.setSupplierSigner(user.getName());
				}
			}
		}
		// 发起合同
		Map<Long, String> customerMap = contractRecordService.draft(name,
				List.of(resource.getPurchaserId(), resource.getSellerId()),
				List.of(resource.getStocktakingFileId()), resource.getId(),
				PurchaseContractDef.CorrelationTable.STOCKTAKING, null,
				resource.getModule());
		// 设置文件id
		resource.setStocktakingFileId(
				contractRecordService.download(resource.getId(),
						PurchaseContractDef.CorrelationTable.STOCKTAKING));

		resource.getPurchaserEnterprise()
				.setSignMobile(customerMap.get(resource.getPurchaserId()));
		resource.getSellerEnterprise()
				.setSignMobile(customerMap.get(resource.getSellerId()));
		// 状态设置为签署中
		resource.setState(StocktakingDef.State.SIGNING.getCode());
		resource.setSignStatus(
				BusinessContractDef.CommonSignState.UNSIGNED.getCode());
		this.updateAllProperties(resource);

		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				resource.getPurchaserEnterprise().getSignMobile(),
				resource.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}
		// 获取签署链接
		return contractRecordService.sign(resource.getId(),
				PurchaseContractDef.CorrelationTable.STOCKTAKING, origin);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void reject(Stocktaking stocktaking, Boolean isRevoke) {
		if (Objects.nonNull(stocktaking.getStocktakingFileId())) {
			fileService
					.batchUnActive(List.of(stocktaking.getStocktakingFileId()));
			if (StocktakingDef.SignType.ONLINE
					.match(stocktaking.getSignType())) {
				stocktaking.setStocktakingFileId(null);
			}
			if (isRevoke && StocktakingDef.SignType.ONLINE
					.match(stocktaking.getSignType())) {
				// 撤销合同
				contractRecordService.revoke(stocktaking.getId(),
						PurchaseContractDef.CorrelationTable.STOCKTAKING);
			}
		}
		if (StocktakingDef.Module.JXC.match(stocktaking.getModule())) {
			SpringUtil.getBean(StocktakingService.class).notice(stocktaking, 2);
		} else {
			SpringUtil.getBean(StocktakingService.class).notice(stocktaking, 7);
		}
		super.updateAllProperties(stocktaking);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Stocktaking> confirm(Stocktaking resource) {
		// 更新对账单状态 对账状态改为已完成
		resource.setState(SignReceiptDef.Status.FINISHED.getCode());
		Stocktaking stocktaking = this.updateAllProperties(resource);
		if (StocktakingDef.Module.JXC.match(stocktaking.getModule())) {
			SpringUtil.getBean(StocktakingService.class).notice(resource, 1);
		} else {
			SpringUtil.getBean(StocktakingService.class).notice(resource, 6);
		}
		return Optional.of(stocktaking);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<ContractPageResponse> invalid(Stocktaking stocktaking,
			Integer origin) {
		// 调用契约锁撤销合同接口
		contractRecordService.revoke(stocktaking.getId(),
				PurchaseContractDef.CorrelationTable.STOCKTAKING);
		// 作废后获取作废合同id
		Long fileId = contractRecordService.detail(stocktaking.getId(),
				PurchaseContractDef.CorrelationTable.STOCKTAKING);

		stocktaking.setInvalidFileId(fileId);
		stocktaking.setState(ReconciliationDef.State.INVALIDING.getCode());
		stocktaking.setInvalidSignState(
				PurchaseContractDef.CommonSignState.UNSIGNED.getCode());
		stocktaking.setInvalidRevokeReason(null);
		stocktaking.setInvalidRevokeTime(null);
		stocktaking.setPurchaseInvalidTime(null);
		stocktaking.setSellerInvalidTime(null);
		super.updateAllProperties(stocktaking);
		// 校验子账号是否有签章权限
		if (!contractRecordService.validateSign(
				stocktaking.getPurchaserEnterprise().getSignMobile(),
				stocktaking.getSellerEnterprise().getSignMobile())) {
			return Optional.empty();
		}

		if (CommonDef.UserType.OUTER.match(stocktaking.getInvalidInitiator())) {
			if (StocktakingDef.Module.JXC.match(stocktaking.getModule())) {
				SpringUtil.getBean(StocktakingService.class).notice(stocktaking,
						4);
			} else {
				SpringUtil.getBean(StocktakingService.class).notice(stocktaking,
						9);
			}
		} else {
			this.sendNotice(stocktaking,
					wxSubscriptionProperties.getStocktakingNullifyConfirmCode(),
					MessageFormat.format(
							UserMessageConstants.STOCKTAKING_INVALID_UNCONFIRMED_TEMPLATE,
							stocktaking.getId()));
		}

		return contractRecordService.sign(stocktaking.getId(),
				PurchaseContractDef.CorrelationTable.STOCKTAKING,
				CertificationDef.Origin.PC.getCode());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Stocktaking> invalidOffLine(Stocktaking stocktaking,
			Integer initiator) {
		// 作废合同
		stocktaking.setState(ReconciliationDef.State.INVALIDING.getCode());
		stocktaking.setInvalidInitiator(initiator);
		stocktaking.setInvalidRevokeReason(null);
		stocktaking.setInvalidRevokeTime(null);
		stocktaking.setPurchaseInvalidTime(null);
		stocktaking.setSellerInvalidTime(null);

		if (CommonDef.UserType.OUTER.match(stocktaking.getInvalidInitiator())) {
			stocktaking.setInvalidSignState(
					BusinessContractDef.CommonSignState.BUYER_SIGNED.getCode());
			stocktaking.setPurchaseInvalidTime(LocalDateTime.now());
			if (StocktakingDef.Module.JXC.match(stocktaking.getModule())) {
				SpringUtil.getBean(StocktakingService.class).notice(stocktaking,
						4);
			} else {
				SpringUtil.getBean(StocktakingService.class).notice(stocktaking,
						9);
			}
		} else {
			stocktaking.setInvalidSignState(
					BusinessContractDef.CommonSignState.SUPPLY_SIGNED
							.getCode());
			stocktaking.setSellerInvalidTime(LocalDateTime.now());

			// 如果销售合同中的采购方是录入企业，线下发起作废之后，直接为“已作废”状态，不会给采购方发短信和站内
			// 如果是进销存的
			if (StocktakingDef.Module.JXC.match(stocktaking.getModule())) {
				Project project = projectService
						.findOne(stocktaking.getProjectId())
						.orElseThrow(() -> new BadRequestException(
								ErrorCode.CODE_30152013));
				// 销售合同中的采购方 是 项目中的下游
				if (CommonDef.Symbol.YES
						.match(project.getCustomerIsRecorded())) {
					// 作废合同
					stocktaking
							.setState(StocktakingDef.State.INVALID.getCode());
					stocktaking.setInvalidSignState(
							BusinessContractDef.CommonSignState.COMPLETED
									.getCode());
					return Optional.of(super.updateAllProperties(stocktaking));
				}
			}

			this.sendNotice(stocktaking,
					wxSubscriptionProperties.getStocktakingNullifyConfirmCode(),
					MessageFormat.format(
							UserMessageConstants.STOCKTAKING_INVALID_UNCONFIRMED_TEMPLATE,
							stocktaking.getId()));
		}

		return Optional.of(super.updateAllProperties(stocktaking));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Stocktaking> confirmInvalid(Stocktaking stocktaking) {
		// 确认作废合同
		stocktaking.setState(ReconciliationDef.State.INVALID.getCode());
		stocktaking.setInvalidSignState(
				BusinessContractDef.CommonSignState.COMPLETED.getCode());
		if (CommonDef.UserType.INNER.match(stocktaking.getInvalidInitiator())) {
			stocktaking.setPurchaseInvalidTime(LocalDateTime.now());
		} else {
			stocktaking.setSellerInvalidTime(LocalDateTime.now());
		}
		return Optional.of(super.updateAllProperties(stocktaking));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Stocktaking> revertInvalid(Stocktaking stocktaking) {
		// 撤销作废合同
		stocktaking.setState(ReconciliationDef.State.FINISHED.getCode());
		stocktaking.setInvalidSignState(null);
		stocktaking.setInvalidRevokeTime(LocalDateTime.now());

		if (CommonDef.UserType.INNER.match(stocktaking.getInvalidInitiator())) {
			if (StocktakingDef.Module.JXC.match(stocktaking.getModule())) {
				SpringUtil.getBean(StocktakingService.class).notice(stocktaking,
						5);
			} else {
				SpringUtil.getBean(StocktakingService.class).notice(stocktaking,
						10);
			}
		} else {
			this.sendNotice(stocktaking,
					wxSubscriptionProperties.getStocktakingNullifyDismissCode(),
					MessageFormat.format(
							UserMessageConstants.STOCKTAKING_INVALID_DISMISS_TEMPLATE,
							stocktaking.getId()));
		}

		return Optional.of(super.updateAllProperties(stocktaking));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<Stocktaking> confirmStocktaking(Stocktaking stocktaking) {
		// 更新状态为盘点完成
		stocktaking.setState(StocktakingDef.State.FINISHED.getCode());
		return Optional.of(super.updateAllProperties(stocktaking));
	}

	@Override
	public void downloadPdf(HttpServletResponse response, String id,
			Boolean flag) throws IOException {
		setExportResponseFields(response, id);
		List<Stocktaking> stocktakingList = new ArrayList<>();
		// 盘点信息
		Stocktaking stocktaking = this.findOne(id).orElseThrow(null);
		stocktakingList.add(stocktaking);
		List<StocktakingDetail> stocktakingDetails = stocktakingDetailService
				.findByStocktakingIds(List.of(id));
		Map<String, List<StocktakingDetail>> idDetails = stocktakingDetails
				.stream().collect(Collectors
						.groupingBy(StocktakingDetail::getStocktakingId));
		StockCheckPdfUtil.generateStockCheckPdf(response.getOutputStream(),
				stocktakingList, idDetails, null, flag);
	}

	@Override
	public void printPdf(HttpServletResponse response, List<String> ids,
			String name) throws IOException {
		if (CollectionUtils.isNotEmpty(ids)) {
			setExportResponseFields(response, ids.get(0));
			// 盘点信息
			List<Stocktaking> stocktakingList = super.findByIds(ids);
			List<StocktakingDetail> stocktakingDetails = stocktakingDetailService
					.findByStocktakingIds(ids);
			Map<String, List<StocktakingDetail>> idDetails = stocktakingDetails
					.stream().collect(Collectors
							.groupingBy(StocktakingDetail::getStocktakingId));
			StockCheckPdfUtil.generateStockCheckPdf(response.getOutputStream(),
					stocktakingList, idDetails, name, true);
		}
	}

	@Override
	public Optional<Stocktaking> submit(Stocktaking stocktaking) {
		if (StocktakingDef.SignType.OFFLINE.match(stocktaking.getSignType())) {
			stocktaking.setState(StocktakingDef.State.CONFIRMING.getCode());
			// 如果是进销存的
			if (StocktakingDef.Module.JXC.match(stocktaking.getModule())) {
				// 如果销售合同中的采购方是录入企业，提交后，状态设置为“待盘点”
				if (Boolean.TRUE.equals(contractService
						.validateIsRecorded(stocktaking.getContractId(),
								ContractDef.ContractType.SALES.getCode())
						.orElse(null))) {
					stocktaking.setState(
							StocktakingDef.State.TO_BE_INVENTORIED.getCode());
				}
			}
		} else {
			if (CommonDef.AccountSource.INNER
					.match(stocktaking.getInitiator())) {
				stocktaking.setState(
						StocktakingDef.State.TO_BE_INITIATE.getCode());
			} else {
				stocktaking.setState(StocktakingDef.State.SIGNING.getCode());
			}
		}
		return Optional.of(super.updateAllProperties(stocktaking));
	}

	@Override
	public Optional<Stocktaking> retract(Stocktaking stocktaking) {
		stocktaking.setState(StocktakingDef.State.DRAFT.getCode());
		return Optional.of(super.updateAllProperties(stocktaking));
	}

	@Override
	public Optional<StocktakingCountVo> staticsAdminStocktaking(
			boolean isManage, boolean isSeal) {
		StocktakingCountVo stocktakingCountVo = new StocktakingCountVo();
		stocktakingCountVo.setWaitSigningCount(0L);
		stocktakingCountVo.setRejectedCount(0L);
		stocktakingCountVo.setInvalidingCount(0L);
		stocktakingCountVo.setWaitStocktakingCount(0L);
		stocktakingCountVo.setWaitConfirmingCount(0L);
		List<String> projectIds = projectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		LambdaQueryWrapper<Stocktaking> wrapper = Wrappers
				.lambdaQuery(Stocktaking.class);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计已驳回
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Stocktaking::getState,
						StocktakingDef.State.REJECTED.getCode());
				wrapper.in(Stocktaking::getProjectId, projectIds);
				wrapper.eq(Stocktaking::getModule,
						StocktakingDef.Module.JXC.getCode());
				stocktakingCountVo
						.setRejectedCount(repository.selectCount(wrapper));

				// 统计作废中
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Stocktaking::getState,
						StocktakingDef.State.INVALIDING.getCode());
				wrapper.in(Stocktaking::getProjectId, projectIds);
				wrapper.eq(Stocktaking::getModule,
						StocktakingDef.Module.JXC.getCode());
				stocktakingCountVo
						.setInvalidingCount(repository.selectCount(wrapper));

				// 统计待盘点
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.eq(Stocktaking::getState,
						StocktakingDef.State.TO_BE_INVENTORIED.getCode());
				wrapper.in(Stocktaking::getProjectId, projectIds);
				wrapper.eq(Stocktaking::getModule,
						StocktakingDef.Module.JXC.getCode());
				stocktakingCountVo.setWaitStocktakingCount(
						repository.selectCount(wrapper));
			}
		}

		if (isSeal) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				wrapper.clear();
				this.filterDeleted(wrapper);
				wrapper.in(Stocktaking::getSignStatus,
						BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
						BusinessContractDef.CommonSignState.BUYER_SIGNED
								.getCode())
						.eq(Stocktaking::getState,
								StocktakingDef.State.SIGNING.getCode());
				wrapper.in(Stocktaking::getProjectId, projectIds);
				wrapper.eq(Stocktaking::getModule,
						StocktakingDef.Module.JXC.getCode());
				stocktakingCountVo
						.setWaitSigningCount(repository.selectCount(wrapper));

			}
		}
		return Optional.of(stocktakingCountVo);
	}

	@Override
	public Optional<StocktakingCountVo> staticsCustomerStocktaking(
			boolean isSeal, boolean isPermission) {
		StocktakingCountVo stocktakingCountVo = new StocktakingCountVo();
		stocktakingCountVo.setWaitSigningCount(0L);
		stocktakingCountVo.setRejectedCount(0L);
		stocktakingCountVo.setInvalidingCount(0L);
		stocktakingCountVo.setWaitStocktakingCount(0L);
		stocktakingCountVo.setWaitConfirmingCount(0L);
		LambdaQueryWrapper<Stocktaking> wrapper = Wrappers
				.lambdaQuery(Stocktaking.class);
		Long customerId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();

		if (isPermission) {
			// 统计待确认
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Stocktaking::getPurchaserId, customerId);
			wrapper.eq(Stocktaking::getState,
					StocktakingDef.State.CONFIRMING.getCode())
					.eq(Stocktaking::getInitiator,
							CommonDef.AccountSource.INNER.getCode());
			stocktakingCountVo
					.setWaitConfirmingCount(repository.selectCount(wrapper));

			// 统计作废中
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Stocktaking::getPurchaserId, customerId);
			wrapper.eq(Stocktaking::getState,
					StocktakingDef.State.INVALIDING.getCode());
			stocktakingCountVo
					.setInvalidingCount(repository.selectCount(wrapper));
		}

		if (isSeal) {
			// 统计待签署
			wrapper.clear();
			this.filterDeleted(wrapper);
			wrapper.eq(Stocktaking::getPurchaserId, customerId);
			wrapper.in(Stocktaking::getSignStatus, List.of(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
					BusinessContractDef.CommonSignState.SUPPLY_SIGNED
							.getCode()))
					.eq(Stocktaking::getState,
							StocktakingDef.State.SIGNING.getCode());
			stocktakingCountVo
					.setWaitSigningCount(repository.selectCount(wrapper));
		}

		return Optional.of(stocktakingCountVo);
	}

	@Override
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = "{{#success}}", type = "{{#type}}", bizNo = "{{#resource.getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#resource.getId()}}"),
			@LogRecord.KeyValuePair(key = "#projectId#", value = "{{#code}}") }, messageType = LogDef.MESSAGE_TYPE_INVENTORY, permission = "{{#permission}}")
	public void notice(Stocktaking resource, Integer type) {
		// 提前定义 type -> success 的映射
		Map<Integer, String> successMap = Map.of(

				1, LogDef.STOCKTAKING_CUSTOMER_CONFIRMED,

				2, LogDef.STOCKTAKING_CUSTOMER_REJECTED,

				3, LogDef.STOCKTAKING_COMPLETED,

				4, LogDef.STOCKTAKING_INVALID_UNCONFIRMED,

				5, LogDef.STOCKTAKING_INVALID_REJECTED,

				6, LogDef.STOCKTAKING_CUSTOMER_CONFIRMED,

				7, LogDef.STOCKTAKING_CUSTOMER_REJECTED,

				8, LogDef.STOCKTAKING_COMPLETED,

				9, LogDef.STOCKTAKING_INVALID_UNCONFIRMED,

				10, LogDef.STOCKTAKING_INVALID_REJECTED

		);

		// 设置 success
		if (successMap.containsKey(type)) {
			LogRecordContext.putVariable("success", successMap.get(type));

			// 判断是进销存还是仓储监管
			String typeLog = type <= 5 ? LogDef.STOCKTAKING_INFO
					: LogDef.SUPERVISION_STOCKTAKING_INFO;
			String permissionLog = type <= 5 ? LogDef.PROJECT_DEAL
					: LogDef.S_PROJECT_DEAL;

			if (type <= 5) {
				Project project = projectService
						.findOne(resource.getProjectId())
						.orElseThrow(() -> new BadRequestException(
								ErrorCode.CODE_30152013));
				LogRecordContext.putVariable("code", project.getName());
			} else {
				StockProject stockProject = stockProjectService
						.findOne(resource.getProjectId())
						.orElseThrow(() -> new BadRequestException(
								ErrorCode.CODE_30152013));
				LogRecordContext.putVariable("code", stockProject.getName());

			}
			LogRecordContext.putVariable("type", typeLog);
			LogRecordContext.putVariable("permission", permissionLog);
		}
		log.info("盘点发送通知:{}", resource.getId());
	}

	@Override
	public Optional<StocktakingCountVo> staticsStockAdminStocktaking(
			boolean isManage) {
		StocktakingCountVo stocktakingCountVo = new StocktakingCountVo();
		stocktakingCountVo.setInvalidingCount(0L);
		stocktakingCountVo.setWaitConfirmingCount(0L);
		stocktakingCountVo.setWaitSigningCount(0L);
		stocktakingCountVo.setWaitStocktakingCount(0L);
		stocktakingCountVo.setRejectedCount(0L);
		List<String> projectIds = stockProjectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计已驳回
				LambdaQueryWrapper<Stocktaking> wrapper = Wrappers
						.lambdaQuery(Stocktaking.class);
				this.filterDeleted(wrapper);
				wrapper.eq(Stocktaking::getState,
						StocktakingDef.State.REJECTED.getCode());
				wrapper.eq(Stocktaking::getModule,
						StocktakingDef.Module.STORAGE.getCode());
				wrapper.in(Stocktaking::getProjectId, projectIds);
				stocktakingCountVo
						.setRejectedCount(repository.selectCount(wrapper));
				wrapper.clear();

				// 统计作废中
				this.filterDeleted(wrapper);
				wrapper.eq(Stocktaking::getState,
						StocktakingDef.State.INVALIDING.getCode());
				wrapper.eq(Stocktaking::getModule,
						StocktakingDef.Module.STORAGE.getCode());
				wrapper.in(Stocktaking::getProjectId, projectIds);
				stocktakingCountVo
						.setInvalidingCount(repository.selectCount(wrapper));
				wrapper.clear();
			}
		}
		return Optional.of(stocktakingCountVo);
	}

	@Override
	public Optional<StocktakingCountVo> staticsStockCustomerStocktaking() {
		StocktakingCountVo stocktakingCountVo = new StocktakingCountVo();
		stocktakingCountVo.setInvalidingCount(0L);
		stocktakingCountVo.setWaitConfirmingCount(0L);
		stocktakingCountVo.setWaitSigningCount(0L);
		stocktakingCountVo.setWaitStocktakingCount(0L);
		stocktakingCountVo.setRejectedCount(0L);
		// 统计待确认
		LambdaQueryWrapper<Stocktaking> wrapper = Wrappers
				.lambdaQuery(Stocktaking.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Stocktaking::getPurchaserId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		wrapper.eq(Stocktaking::getState,
				StocktakingDef.State.CONFIRMING.getCode())
				.eq(Stocktaking::getInitiator,
						CommonDef.AccountSource.INNER.getCode());
		wrapper.eq(Stocktaking::getModule,
				StocktakingDef.Module.STORAGE.getCode());
		stocktakingCountVo
				.setWaitConfirmingCount(repository.selectCount(wrapper));
		wrapper.clear();

		// 统计待签署
		this.filterDeleted(wrapper);
		wrapper.eq(Stocktaking::getPurchaserId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		wrapper.in(Stocktaking::getSignStatus,
				List.of(BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode()))
				.eq(Stocktaking::getState,
						StocktakingDef.State.SIGNING.getCode());
		wrapper.eq(Stocktaking::getModule,
				StocktakingDef.Module.STORAGE.getCode());
		stocktakingCountVo.setWaitSigningCount(repository.selectCount(wrapper));
		wrapper.clear();

		// 统计作废中
		this.filterDeleted(wrapper);
		wrapper.eq(Stocktaking::getPurchaserId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		wrapper.eq(Stocktaking::getState,
				StocktakingDef.State.INVALIDING.getCode());
		wrapper.eq(Stocktaking::getModule,
				StocktakingDef.Module.STORAGE.getCode());
		stocktakingCountVo.setInvalidingCount(repository.selectCount(wrapper));
		wrapper.clear();
		return Optional.of(stocktakingCountVo);
	}

	/**
	 * @description: 获取文件id，生成pdf文件并上传文件服务器，返回文件id
	 * @param: [resource]
	 * @return: java.lang.Long
	 **/
	private Long getFileId(Stocktaking stocktaking) {
		// 生成pdf
		OutputStream outputStream = new ByteArrayOutputStream();
		List<Stocktaking> stocktakingList = new ArrayList<>();
		// 抽检信息
		stocktakingList.add(stocktaking);
		List<StocktakingDetail> stocktakingDetails = stocktakingDetailService
				.findByStocktakingIds(List.of(stocktaking.getId()));
		Map<String, List<StocktakingDetail>> idDetails = stocktakingDetails
				.stream().collect(Collectors
						.groupingBy(StocktakingDetail::getStocktakingId));
		StockCheckPdfUtil.generateStockCheckPdf(outputStream, stocktakingList,
				idDetails, null, false);
		try {
			CustomMultipartFile convert = MultipartFileUtils.convert(
					outputStream, "盘点合同" + stocktaking.getId(),
					"盘点合同" + stocktaking.getId() + ".pdf", "text/plain");
			File file = fileService
					.upload(convert, "盘点合同" + stocktaking.getId() + ".pdf")
					.orElse(null);
			if (Objects.nonNull(file)) {
				return file.getId();
			}
		} catch (Exception e) {
			log.error("生成pdf文件失败", e);
		}
		return null;
	}

	/**
	 * 设置导出响应头
	 *
	 * @param response
	 */
	private void setExportResponseFields(HttpServletResponse response,
			String id) {
		response.setContentType("application/pdf");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode(
				String.format("盘点_%s_%s",
						DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
								.format(LocalDateTime.now()),
						id),
				StandardCharsets.UTF_8).replaceAll("\\+", "%20");
		response.setHeader("Content-disposition",
				"attachment;filename*=utf-8''" + fileName + ".pdf");
	}

	/**
	 * 封装盘点的vo信息
	 *
	 * @param
	 * @return
	 */
	private StocktakingVo packVo(Stocktaking stocktaking) {
		StocktakingVo vo = new StocktakingVo();
		// 盘点信息
		vo.setStocktaking(stocktaking);
		// 设置项目信息
		if (ObjectUtils.isNotEmpty(stocktaking.getProjectId())) {
			if (StocktakingDef.Module.JXC.match(stocktaking.getModule())) {
				projectService.findOne(stocktaking.getProjectId())
						.ifPresent(vo::setProject);
			} else if (StocktakingDef.Module.STORAGE
					.match(stocktaking.getModule())) {
				stockProjectService.findOne(stocktaking.getProjectId())
						.ifPresent(vo::setStockProject);
			}
		}
		// 设置合同信息
		if (ObjectUtils.isNotEmpty(stocktaking.getContractId())) {
			if (StocktakingDef.Module.JXC.match(stocktaking.getModule())) {
				contractService.findOne(stocktaking.getContractId())
						.ifPresent(vo::setContract);
			} else if (StocktakingDef.Module.STORAGE
					.match(stocktaking.getModule())) {
				stockContractService.findOne(stocktaking.getContractId())
						.ifPresent(vo::setStockContract);
			}
		}
		// 盘点明细信息根据盘点id查询
		List<StocktakingDetail> stocktakingDetails = stocktakingDetailService
				.findByStocktakingIds(List.of(stocktaking.getId()));
		// 设置盘点明细信息
		vo.setStocktakingDetailList(stocktakingDetails);
		return vo;
	}

	/**
	 * 封装分页查询的盘点vo信息
	 *
	 * @param
	 * @return
	 */
	private List<StocktakingVo> packPageVos(List<Stocktaking> stocktakingList) {
		List<StocktakingVo> vos = new ArrayList<>();
		// 合同信息
		List<String> contracts = stocktakingList.stream()
				.map(Stocktaking::getContractId).distinct().toList();
		Map<String, Contract> contractMap = contractService.findByIds(contracts)
				.stream().collect(Collectors.toMap(Contract::getId, e -> e));
		// 仓储合同信息
		Map<String, StockContract> stockContractMap = stockContractService
				.findByIds(contracts).stream()
				.collect(Collectors.toMap(StockContract::getId, e -> e));
		// 项目信息
		List<String> projectIds = stocktakingList.stream()
				.map(Stocktaking::getProjectId).distinct().toList();
		Map<String, Project> projectMap = projectService.findByIds(projectIds)
				.stream().collect(Collectors.toMap(Project::getId, e -> e));
		// 仓储项目信息
		Map<String, StockProject> stockProjectMap = stockProjectService
				.findByIds(projectIds).stream()
				.collect(Collectors.toMap(StockProject::getId, e -> e));
		// 盘点ids
		List<String> stocktakingIds = stocktakingList.stream()
				.map(Stocktaking::getId).distinct().toList();
		// 根据盘点id查询盘点明细
		List<StocktakingDetail> stocktakingDetails = stocktakingDetailService
				.findByStocktakingIds(stocktakingIds);
		// 将盘点明细根据盘点id进行分组
		Map<String, List<StocktakingDetail>> stocktakingDetailMap = stocktakingDetails
				.stream().collect(Collectors
						.groupingBy(StocktakingDetail::getStocktakingId));
		for (Stocktaking stocktaking : stocktakingList) {
			StocktakingVo vo = new StocktakingVo();
			// 设置盘点信息
			vo.setStocktaking(stocktaking);
			// 设置合同信息
			vo.setContract(contractMap.get(stocktaking.getContractId()));
			// 设置项目信息
			vo.setProject(projectMap.get(stocktaking.getProjectId()));
			// 设置仓储合同信息
			vo.setStockContract(
					stockContractMap.get(stocktaking.getContractId()));
			// 设置仓储项目信息
			vo.setStockProject(stockProjectMap.get(stocktaking.getProjectId()));
			// 设置盘点明细信息
			vo.setStocktakingDetailList(
					stocktakingDetailMap.get(stocktaking.getId()));
			vos.add(vo);
		}
		return vos;
	}

	/**
	 * 发送短信
	 *
	 * @param stocktaking
	 * @param templateCode
	 * @param title
	 */
	private void sendNotice(Stocktaking stocktaking, String templateCode,
			String title) {
		Customer customer = null;
		DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
				.findOne(stocktaking.getPurchaserBusinessId()).orElse(null);
		if (Objects.nonNull(dealingsEnterprise)
				&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
			customer = customerService
					.findOne(dealingsEnterprise.getCustomerId()).orElse(null);
		}

		if (Objects.nonNull(customer)) {
			if (StringUtils.isNotBlank(templateCode)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(templateCode)
						.params(Map.of("order_id", stocktaking.getId()))
						.mobile(customer.getMobile()).build());
			}

			if (StringUtils.isNotBlank(title)) {
				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.INVENTORY.getCode())
						.title(title)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(StocktakingDef.Module.JXC
								.match(stocktaking.getModule())
										? UserMessageConstants.STOCKTAKING_DETAIL_PAGE
										: UserMessageConstants.SUPERVISION_STOCKTAKING_DETAIL_PAGE)
						.detailId(String.valueOf(stocktaking.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
		}
	}
}
