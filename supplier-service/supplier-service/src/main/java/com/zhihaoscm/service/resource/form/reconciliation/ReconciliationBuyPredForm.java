package com.zhihaoscm.service.resource.form.reconciliation;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.zhihaoscm.domain.bean.entity.DeliverGoods;
import com.zhihaoscm.domain.bean.vo.DeliverGoodsVo;
import org.hibernate.validator.constraints.Length;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.Reconciliation;
import com.zhihaoscm.domain.meta.biz.ReconciliationDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name = "ReconciliationPredForm", title = "预对账完成之后 新增修改 对账")
public class ReconciliationBuyPredForm {

	@Schema(title = "签署方式 1线上 2.线下")
	private Integer signType;

	@Schema(description = "发货单列表")
	List<DeliverGoods> deliverGoodsList;

	@Schema(description = "发货单列表")
	List<DeliverGoodsVo> deliverGoodsVoList;

	@Schema(title = "实际对账金额")
	private BigDecimal reconciliationAmount;

	@NotNull(message = ErrorCode.CODE_30154018)
	@Schema(description = "对账数量/重量")
	private BigDecimal reconciliationWeight;

	@NotNull(message = ErrorCode.CODE_30154006)
	@Schema(title = "对账日期")
	private LocalDateTime reconciliationDate;

	@Schema(title = "对账单据文件id")
	private Long reconciliationFileId;

	@Schema(title = "备注")
	@Length(max = 200, message = ErrorCode.CODE_30154007)
	private String remark;

	public Reconciliation convertEntity(Reconciliation reconciliation,
			Integer initiator) {
		reconciliation.setSignType(this.signType);
		reconciliation.setReconciliationWeight(this.reconciliationWeight);
		reconciliation.setReconciliationDate(this.reconciliationDate);
		reconciliation.setReconciliationAmount(this.reconciliationAmount);
		reconciliation.setUnbilledAmount(this.reconciliationAmount);
		reconciliation.setReconciliationFileId(this.reconciliationFileId);
		reconciliation.setRemark(this.remark);
		// 设置发起方
		reconciliation.setInitiator(initiator);
		if (ReconciliationDef.SignType.OFFLINE
				.match(reconciliation.getSignType())) {
			// 线下签署 设置确认中状态
			reconciliation
					.setState(ReconciliationDef.State.CONFIRMING.getCode());
		} else {
			// 线上签署
			if (CommonDef.AccountSource.INNER
					.match(reconciliation.getInitiator())) {
				// 管理后台设置成待发起状态
				reconciliation.setState(
						ReconciliationDef.State.TO_BE_INITIATE.getCode());
			} else {
				// 客户端设置成签署中
				reconciliation
						.setState(ReconciliationDef.State.SIGNING.getCode());
			}
		}
		// 不是预对账时 设置已经进入对账阶段
		reconciliation
				.setIsConductReconciliation(CommonDef.Symbol.YES.getCode());
		// 清除对账作废信息
		reconciliation.setDeliveredInfo(null);
		reconciliation.setInvalidFileId(null);
		reconciliation.setInvalidInitiator(null);
		reconciliation.setInvalidReason(null);
		reconciliation.setInvalidRevokeReason(null);
		reconciliation.setInvalidRevokeTime(null);
		reconciliation.setInvalidRevokeTime(null);
		reconciliation.setPrePurchaseInvalidTime(null);
		reconciliation.setSellerInvalidTime(null);
		reconciliation.setInvalidSignState(null);
		return reconciliation;
	}
}
