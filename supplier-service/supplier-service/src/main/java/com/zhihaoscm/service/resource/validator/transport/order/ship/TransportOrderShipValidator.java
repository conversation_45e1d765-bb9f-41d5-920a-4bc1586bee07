package com.zhihaoscm.service.resource.validator.transport.order.ship;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import com.zhihaoscm.domain.exception.DynamicsBadRequestException;
import com.zhihaoscm.domain.meta.biz.TransportOrderRailwayDef;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.util.UpdateUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.dto.TransportOrderShipDto;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.*;
import com.zhihaoscm.domain.bean.vo.ShipVo;
import com.zhihaoscm.domain.bean.vo.ThirdTransportOrderDetailsShip;
import com.zhihaoscm.domain.bean.vo.ThirdTransportOrderShip;
import com.zhihaoscm.domain.bean.vo.ThirdTransportOrderShipVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.AutoCodeDef;
import com.zhihaoscm.domain.meta.biz.DeliverGoodsDef;
import com.zhihaoscm.domain.meta.biz.TransportOrderShipDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.domain.utils.MultipartFileUtil;
import com.zhihaoscm.domain.utils.ThreadPoolUtil;
import com.zhihaoscm.service.client.FileConnectClient;
import com.zhihaoscm.service.client.PortClient;
import com.zhihaoscm.service.client.ShipClient;
import com.zhihaoscm.service.client.ShippingConnectClient;
import com.zhihaoscm.service.core.service.FileService;
import com.zhihaoscm.service.core.service.TransportOrderDetailsShipService;
import com.zhihaoscm.service.core.service.TransportOrderShipService;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.resource.form.transport.order.ship.*;
import com.zhihaoscm.service.resource.validator.delivergoods.DeliverGoodsValidator;
import com.zhihaoscm.service.resource.validator.order.OrderValidator;
import com.zhihaoscm.service.resource.validator.project.ProjectValidator;

import lombok.extern.slf4j.Slf4j;

/**
 * 船运单校验器
 *
 */
@Slf4j
@Component
public class TransportOrderShipValidator {

	@Autowired
	private TransportOrderShipService service;

	@Autowired
	private PortClient portClient;

	@Autowired
	private StringRedisClient redisClient;

	@Autowired
	private DeliverGoodsValidator deliverGoodsValidator;

	@Autowired
	private ProjectValidator projectValidator;

	@Autowired
	private ShippingConnectClient shippingConnectClient;

	@Autowired
	private FileConnectClient fileConnectClient;

	@Autowired
	private FileService fileService;

	@Autowired
	private TransportOrderDetailsShipService transportOrderDetailsShipService;

	@Autowired
	private TransportOrderShipService transportOrderShipService;

	@Autowired
	private ShipClient shipClient;

	@Autowired
	private OrderValidator orderValidator;

	@Autowired
	private CustomerService customerService;

	/**
	 * 校验是否存在
	 *
	 * @param id
	 * @return
	 */
	public TransportOrderShip validateExist(String id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30137001));
	}

	/**
	 * 校验第三方船运单
	 *
	 * @param shippingOrderId
	 *            链云平台船运单ID
	 * @return
	 */
	public ThirdTransportOrderShip validateThirdTransportOrderShip(
			String shippingOrderId) {
		ThirdTransportOrderShipVo transportOrderShipVo = shippingConnectClient
				.findVoById(shippingOrderId).getData();
		if (Objects.isNull(transportOrderShipVo)) {
			throw new BadRequestException(ErrorCode.CODE_30137001);
		}
		return transportOrderShipVo.getTransportOrderShip();
	}

	/**
	 * 校验第三方船运单
	 *
	 * @param shippingOrderId
	 *            链云平台船运单ID
	 * @return
	 */
	private ThirdTransportOrderShipVo validateThirdTransportOrderShipVo(
			String shippingOrderId) {
		ThirdTransportOrderShipVo transportOrderShipVo = shippingConnectClient
				.findVoById(shippingOrderId).getData();
		if (Objects.isNull(transportOrderShipVo)) {
			throw new BadRequestException(ErrorCode.CODE_30137001);
		}
		return transportOrderShipVo;
	}

	/**
	 * 校验同意卸货
	 *
	 * @param id
	 */
	public TransportOrderShip validateAgreeUnload(String id, Integer createType) {
		TransportOrderShip transportOrderShip = this.validateExist(id);
		if (TransportOrderShipDef.State.CANCELED
				.match(transportOrderShip.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30138022);
		}
		projectValidator
				.validateProjectPeople(transportOrderShip.getProjectId());
		ThirdTransportOrderShip thirdTransportOrderShip = this
				.validateThirdTransportOrderShip(
						transportOrderShip.getShippingOrderId());
		if (!TransportOrderShipDef.ThirdState.TO_BE_UNLOADED
				.match(thirdTransportOrderShip.getState())) {
			// 待卸货才能进行同意卸货
			throw new BadRequestException(ErrorCode.CODE_30140004);
		}
		return transportOrderShip;
	}

	/**
	 * 校验修改 已经完成卸货状态不能进行修改信息
	 *
	 * @param id
	 * @return
	 */
	public TransportOrderShip validateUpdate(String id,
			TransportOrderShipForm form, Integer createType) {
		TransportOrderShip transportOrderShip = this.validateUpdate(id);
        if(!Objects.equals(createType, transportOrderShip.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderShip.getCreateType()).getName());
        }

		// 校验码头
		Port sourcePort = portClient.findById(form.getSourcePortId()).getData();
		Port destinationPort = portClient.findById(form.getDestinationPortId())
				.getData();
		transportOrderShip.setSourcePortName(sourcePort.getShortName());
		transportOrderShip
				.setDestinationPortName(destinationPort.getShortName());
		this.validateShip(form.getShipId(), transportOrderShip);
		return transportOrderShip;
	}

	/**
	 * 校验修改 已经完成卸货状态不能进行修改信息
	 *
	 * @param id
	 * @return
	 */
	public TransportOrderShip validateUpdate(String id) {
		TransportOrderShip transportOrderShip = this.validateExist(id);
		if (TransportOrderShipDef.State.CANCELED
				.match(transportOrderShip.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30138022);
		}
		projectValidator
				.validateProjectPeople(transportOrderShip.getProjectId());
		if (!TransportOrderShipDef.Source.SELF_CREATE
				.match(transportOrderShip.getSource())) {
			throw new BadRequestException(ErrorCode.CODE_30138021);
		}
		return transportOrderShip;
	}

	/**
	 * 校验删除
	 *
	 * @param id
	 */
	public void validateDelete(String id, Integer createType) {
		TransportOrderShip transportOrderShip = this.validateExist(id);

        if(!Objects.equals(createType, transportOrderShip.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderShip.getCreateType()).getName());
        }
		if (TransportOrderShipDef.State.CANCELED
				.match(transportOrderShip.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30138022);
		}
		projectValidator
				.validateProjectPeople(transportOrderShip.getProjectId());
		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(transportOrderShip.getGoodsId());
		// 发货完成不能新增删除船运单
		if (DeliverGoodsDef.Status.DELIVER_COMPLETE
				.match(deliverGoods.getStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30139030);
		}
	}

	/**
	 * 校验开始装货
	 *
	 * @param id
	 */
	public void validateFinishLoading(String id, Integer createType) {
		TransportOrderShip transportOrderShip = this.validateExist(id);
        if(!Objects.equals(createType, transportOrderShip.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderShip.getCreateType()).getName());
        }
		if (TransportOrderShipDef.State.CANCELED
				.match(transportOrderShip.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30138022);
		}
		projectValidator
				.validateProjectPeople(transportOrderShip.getProjectId());
		if (!TransportOrderShipDef.State.TO_BE_LOADED
				.match(transportOrderShip.getState())) {
			// 待装货才能进行开始装货
			throw new BadRequestException(ErrorCode.CODE_30137023);
		}
	}

	/**
	 * 校验支付
	 *
	 * @param form
	 * @return
	 */
	public TransportOrderShip validatePay(TransportOrderShipPayForm form, Integer createType) {
		// 自行支付才有支付定金按钮
		TransportOrderShip transportOrderShip = this
				.validateExist(form.getRelationCode());
        if(!Objects.equals(createType, transportOrderShip.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderShip.getCreateType()).getName());
        }

		if (TransportOrderShipDef.State.CANCELED
				.match(transportOrderShip.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30138022);
		}
		projectValidator
				.validateProjectPeople(transportOrderShip.getProjectId());

		// 校验链云的状态
		ThirdTransportOrderShip thirdTransportOrderShip = this
				.validateThirdTransportOrderShip(
						transportOrderShip.getShippingOrderId());

		if (TransportOrderShipDef.PayType.DEPOSIT.match(form.getType())) {
			if (!TransportOrderShipDef.ThirdState.DEPOSIT_TO_BE_PAID
					.match(thirdTransportOrderShip.getState())) {
				throw new BadRequestException(ErrorCode.CODE_30138012);
			}
		}

		if (TransportOrderShipDef.PayType.FREIGHT.match(form.getType())) {
			List<Integer> states = new ArrayList<>();
			states.add(
					TransportOrderShipDef.ThirdState.TO_BE_UNLOADED.getCode());
			states.add(TransportOrderShipDef.ThirdState.DURING_UNLOADING
					.getCode());
			states.add(TransportOrderShipDef.ThirdState.DISCHARGED.getCode());
			states.add(TransportOrderShipDef.ThirdState.COMPLETED.getCode());
			if (!states.contains(thirdTransportOrderShip.getState())) {
				throw new BadRequestException(ErrorCode.CODE_30138023);
			}
		}

		if (!TransportOrderShipDef.DepositPayType.PAY_ONESELF
				.match(transportOrderShip.getPayType())) {
			throw new BadRequestException(ErrorCode.CODE_30138008);
		}
		return transportOrderShip;
	}

	/**
	 * 校验发航确认
	 *
	 * @param id
	 */
	public void validateConfirmationDeparture(String id, Integer createType) {
		TransportOrderShip transportOrderShip = this.validateExist(id);
        if(!Objects.equals(createType, transportOrderShip.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderShip.getCreateType()).getName());
        }
		if (TransportOrderShipDef.State.CANCELED
				.match(transportOrderShip.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30138022);
		}
		projectValidator
				.validateProjectPeople(transportOrderShip.getProjectId());
		if (!TransportOrderShipDef.State.AWAITING_DEPARTURE
				.match(transportOrderShip.getState())) {
			// 待发航才能进行发航确认
			throw new BadRequestException(ErrorCode.CODE_30140002);

		}
	}

	/**
	 * 校验发航确认
	 *
	 * @param id
	 */
	public TransportOrderShip validateThirdConfirmationDeparture(String id, Integer createType) {
		TransportOrderShip transportOrderShip = this.validateExist(id);
        if(!Objects.equals(createType, transportOrderShip.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderShip.getCreateType()).getName());
        }
		if (TransportOrderShipDef.State.CANCELED
				.match(transportOrderShip.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30138022);
		}
		projectValidator
				.validateProjectPeople(transportOrderShip.getProjectId());
		ThirdTransportOrderShip thirdTransportOrderShip = this
				.validateThirdTransportOrderShip(
						transportOrderShip.getShippingOrderId());
		if (!TransportOrderShipDef.ThirdState.AWAITING_DEPARTURE
				.match(thirdTransportOrderShip.getState())) {
			// 待发航才能进行发航确认
			throw new BadRequestException(ErrorCode.CODE_30140002);
		}
		return transportOrderShip;
	}

	/**
	 * 校验新增-自建
	 *
	 * @param form
	 */
	public TransportOrderShip validateSelfCreate(TransportOrderShipForm form) {
		// 校验码头
		Port sourcePort = portClient.findById(form.getSourcePortId()).getData();
		Port destinationPort = portClient.findById(form.getDestinationPortId())
				.getData();
		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(form.getGoodsId());

		orderValidator.validateExist(form.getOrderId());

		// 发货完成不能新增删除船运单
		if (DeliverGoodsDef.Status.DELIVER_COMPLETE
				.match(deliverGoods.getStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30139030);
		}

		Project project = projectValidator
				.validateExistProject(deliverGoods.getProjectId());
		projectValidator.validateProjectPeople(deliverGoods.getProjectId());

		TransportOrderShip transportOrderShip = form.convertToEntity();
		this.validateShip(form.getShipId(), transportOrderShip);
		transportOrderShip.setProjectId(deliverGoods.getProjectId());
		transportOrderShip.setContractId(deliverGoods.getContractId());
		transportOrderShip.setSourcePortName(sourcePort.getShortName());
		transportOrderShip
				.setDestinationPortName(destinationPort.getShortName());
		transportOrderShip.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(
				redisClient, project.getCode(),
				RedisKeys.Cache.TRANSPORT_ORDER_SHIP,
				AutoCodeDef.BusinessRuleCode.TRANSPORT_ORDER_SHIP.getCode(), 4,
				AutoCodeDef.DATE_TYPE.yy));
		return transportOrderShip;
	}

	/**
	 * 校验第三方新增并填充信息
	 *
	 * @param form
	 * @return
	 */
	public TransportOrderShipDto validateThirdCreate(
			TransportOrderShipThirdForm form) {
		log.info("校验第三方新增并填充信息开始时间 ===> {}", LocalDateTime.now());
		TransportOrderShipDto dto = new TransportOrderShipDto();
		ThirdTransportOrderShipVo thirdTransportOrderShipVo = this
				.validateThirdTransportOrderShipVo(form.getShippingOrderId());
		List<ShippingDepositInfo> ownerShippingDeposits = thirdTransportOrderShipVo
				.getOwnerShippingDeposits();

		// 链云运单数据
		ThirdTransportOrderShip transportOrderShip = thirdTransportOrderShipVo
				.getTransportOrderShip();
		if (Objects.nonNull(transportOrderShip.getAppId())) {
			throw new BadRequestException(ErrorCode.CODE_30139029);
		}

		ThirdTransportOrderDetailsShip thirdTransportOrderDetailsShip = thirdTransportOrderShipVo
				.getTransportOrderDetailsShip();

		// 转实体
		TransportOrderShip newTransportOrderShip = form.convertToEntity();
		// 填充数据
		UpdateUtil.copyNonNullProperties(transportOrderShip,
				newTransportOrderShip);

		if (CollectionUtils.isNotEmpty(ownerShippingDeposits)) {
			for (ShippingDepositInfo shippingDepositInfo : ownerShippingDeposits) {
				shippingDepositInfo.setPaymentFileId(this
						.uploadFile(shippingDepositInfo.getPaymentFileId()));
				shippingDepositInfo.setReceiptFileId(this
						.uploadFile(shippingDepositInfo.getReceiptFileId()));
			}
			newTransportOrderShip.setAdvanceDeposit(
					new ArrayShippingDepositInfo(ownerShippingDeposits));
		}

		// 校验发货信息
		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(form.getGoodsId());
		Order order = orderValidator.validateExist(form.getOrderId());
		// 发货完成不能新增删除船运单
		if (DeliverGoodsDef.Status.DELIVER_COMPLETE
				.match(deliverGoods.getStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30139030);
		}

		// 设置项目ID
		newTransportOrderShip.setProjectId(deliverGoods.getProjectId());
		newTransportOrderShip.setContractId(deliverGoods.getContractId());

		newTransportOrderShip.setGoodsType(order.getGoodsName());

		projectValidator.validateProjectPeople(deliverGoods.getProjectId());

		// 货主信息取发货单采购商信息
		Enterprise purchaserEnterprise = deliverGoods.getPurchaserEnterprise();
		newTransportOrderShip.setOwnerId(deliverGoods.getPurchaserId());
		newTransportOrderShip.setOwnerEnterprise(purchaserEnterprise);
		newTransportOrderShip.setOwnerName(purchaserEnterprise.getRealName());
		newTransportOrderShip.setOwnerMobile(purchaserEnterprise.getMobile());

		// 校验项目信息
		Project project = projectValidator
				.validateExistProject(deliverGoods.getProjectId());

		// 设置ID
		newTransportOrderShip.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(
				redisClient, project.getCode(),
				RedisKeys.Cache.TRANSPORT_ORDER_SHIP,
				AutoCodeDef.BusinessRuleCode.TRANSPORT_ORDER_SHIP.getCode(), 4,
				AutoCodeDef.DATE_TYPE.yy));
		// 处理运单状态
		this.handleShippingOrderState(transportOrderShip,
				newTransportOrderShip);

		// 处理支付文件
		ThreadPoolUtil.submitTask(
				() -> SpringUtil.getBean(TransportOrderShipValidator.class)
						.handlePayFile(newTransportOrderShip.getId(),
								transportOrderShip,ownerShippingDeposits),
				ThreadPoolUtil.getShippingOrderExecutor());

		dto.setTransportOrderShip(newTransportOrderShip);

		// 设置明细数据
		TransportOrderDetailsShip newTransportOrderDetailsShip = new TransportOrderDetailsShip();
		newTransportOrderDetailsShip.setId(newTransportOrderShip.getId());
		// 发货信息
		newTransportOrderDetailsShip.setDeliveryInfo(
				thirdTransportOrderDetailsShip.getDeliveryInfo());

		// 是否排水
		newTransportOrderDetailsShip
				.setDrainage(thirdTransportOrderDetailsShip.getDrainage());
		// 排水确认时间
		newTransportOrderDetailsShip.setDrainageTime(
				thirdTransportOrderDetailsShip.getDrainageTime());

		// 货主确认发航时间
		newTransportOrderDetailsShip.setOwnerSetSailTime(
				thirdTransportOrderDetailsShip.getOwnerSetSailTime());
		// 船主确认到港时间
		newTransportOrderDetailsShip.setCaptainArrivalTime(
				thirdTransportOrderDetailsShip.getCaptainArrivalTime());

		// 接档信息
		ThreadPoolUtil.submitTask(
				() -> this.handleGearShift(newTransportOrderDetailsShip,
						thirdTransportOrderDetailsShip),
				ThreadPoolUtil.getShippingOrderExecutor());

		// 离岸准备信息
		ThreadPoolUtil.submitTask(
				() -> this.handleLgzbInfo(newTransportOrderDetailsShip,
						thirdTransportOrderDetailsShip),
				ThreadPoolUtil.getShippingOrderExecutor());

		// 发航信息
		ThreadPoolUtil.submitTask(
				() -> this.handleSetSailInfo(newTransportOrderDetailsShip,
						thirdTransportOrderDetailsShip),
				ThreadPoolUtil.getShippingOrderExecutor());

		// 处理卸货信息
		ThreadPoolUtil.submitTask(
				() -> this.handleUnloadingInfo(newTransportOrderDetailsShip,
						thirdTransportOrderDetailsShip),
				ThreadPoolUtil.getShippingOrderExecutor());

		// 排水视频ID
		ThreadPoolUtil.submitTask(
				() -> this.handelDrainageVideo(newTransportOrderDetailsShip,
						thirdTransportOrderDetailsShip),
				ThreadPoolUtil.getShippingOrderExecutor());

		// 到港视频ID
		ThreadPoolUtil.submitTask(
				() -> this.handleArrivalVideo(newTransportOrderDetailsShip,
						thirdTransportOrderDetailsShip),
				ThreadPoolUtil.getShippingOrderExecutor());

		// 货主确认卸货时间
		newTransportOrderDetailsShip.setOwnerUnloadingTime(
				thirdTransportOrderDetailsShip.getOwnerUnloadingTime());
		// 运费结算吨位
		dto.setTransportOrderDetailsShip(newTransportOrderDetailsShip);
		return dto;
	}

	/**
	 * 排水视频ID
	 *
	 * @param transportOrderDetailsShip
	 */
	private void handelDrainageVideo(
			TransportOrderDetailsShip newTransportOrderDetailsShip,
			ThirdTransportOrderDetailsShip transportOrderDetailsShip) {

		Long drainageVideoId = transportOrderDetailsShip.getDrainageVideoId();
		if (Objects.nonNull(drainageVideoId)) {
			newTransportOrderDetailsShip
					.setDrainageVideoId(this.uploadFile(drainageVideoId));
		}

		LambdaUpdateWrapper<TransportOrderDetailsShip> wrapper = new LambdaUpdateWrapper<>();
		wrapper.set(TransportOrderDetailsShip::getDrainageVideoId,
				newTransportOrderDetailsShip.getDrainageVideoId());
		transportOrderDetailsShipService.updateByIdAndWrapper(
				newTransportOrderDetailsShip.getId(), wrapper);
		log.info("排水视频ID");
	}

	/**
	 * 校验船舶
	 *
	 * @param shipId
	 * @param transportOrderShip
	 */
	private void validateShip(String shipId,
			TransportOrderShip transportOrderShip) {
		if (StringUtils.isNotBlank(shipId)) {
			ShipVo shipVo = shipClient.findVoById(shipId).getData();
			if (Objects.isNull(shipVo)) {
				throw new BadRequestException(ErrorCode.CODE_30139018);
			}
			Ship ship = shipVo.getShip();
			if (StringUtils.isNotBlank(ship.getCnname())) {
				transportOrderShip
						.setShipName(ship.getCnname() + "/" + ship.getName());
			} else {
				transportOrderShip.setShipName(ship.getName());
			}
			transportOrderShip.setShipType(ship.getType());
		}
	}

	/**
	 * 到港视频ID
	 *
	 * @param transportOrderDetailsShip
	 */
	private void handleArrivalVideo(
			TransportOrderDetailsShip newTransportOrderDetailsShip,
			ThirdTransportOrderDetailsShip transportOrderDetailsShip) {
		log.info("到港视频ID开始 ===> {}", LocalDateTime.now());
		// 到港视频ID
		Long arrivalVideoId = transportOrderDetailsShip.getArrivalVideoId();
		if (Objects.nonNull(arrivalVideoId)) {
			newTransportOrderDetailsShip
					.setArrivalVideoId(this.uploadFile(arrivalVideoId));

			LambdaUpdateWrapper<TransportOrderDetailsShip> wrapper = new LambdaUpdateWrapper<>();
			wrapper.set(TransportOrderDetailsShip::getArrivalVideoId,
					newTransportOrderDetailsShip.getArrivalVideoId());
			transportOrderDetailsShipService.updateByIdAndWrapper(
					newTransportOrderDetailsShip.getId(), wrapper);
			log.info("到港视频ID完成 ===> {}", LocalDateTime.now());
		}
	}

	/**
	 * 处理发航信息
	 *
	 * @param transportOrderDetailsShip
	 */
	private void handleSetSailInfo(
			TransportOrderDetailsShip newTransportOrderDetailsShip,
			ThirdTransportOrderDetailsShip transportOrderDetailsShip) {
		log.info("处理发航信息开始 ===> {}", LocalDateTime.now());
		SetSailInfo setSailInfo = transportOrderDetailsShip.getSetSailInfo();
		if (Objects.nonNull(setSailInfo)) {
			// 发航视频id
			Long navigationVideoId = setSailInfo.getNavigationVideoId();
			if (Objects.nonNull(navigationVideoId)) {
				setSailInfo.setNavigationVideoId(
						this.uploadFile(navigationVideoId));
			}
			newTransportOrderDetailsShip.setSetSailInfo(setSailInfo);

			LambdaUpdateWrapper<TransportOrderDetailsShip> wrapper = new LambdaUpdateWrapper<>();
			wrapper.set(TransportOrderDetailsShip::getSetSailInfo, setSailInfo);
			transportOrderDetailsShipService.updateByIdAndWrapper(
					newTransportOrderDetailsShip.getId(), wrapper);
			log.info("处理发航信息完成 ===> {}", LocalDateTime.now());
		}
	}

	/**
	 * 处理离港准备信息
	 *
	 * @param transportOrderDetailsShip
	 */
	private void handleLgzbInfo(
			TransportOrderDetailsShip newTransportOrderDetailsShip,
			ThirdTransportOrderDetailsShip transportOrderDetailsShip) {
		DeparturePreparationInfo lazbInfo = transportOrderDetailsShip
				.getLazbInfo();
		if (Objects.nonNull(lazbInfo)) {
			// 满载视频
			CompletableFuture<Void> loadVideoIdCompletableFuture = CompletableFuture
					.runAsync(() -> {
						Long loadVideoId = lazbInfo.getLoadVideoId();
						if (Objects.nonNull(loadVideoId)) {
							lazbInfo.setLoadVideoId(
									this.uploadFile(loadVideoId));
						}
					}, ThreadPoolUtil.getShippingOrderExecutor());

			// 吨位证明
			CompletableFuture<Void> tonCertificateIdCompletableFuture = CompletableFuture
					.runAsync(() -> {
						Long tonCertificateId = lazbInfo.getTonProof();
						if (Objects.nonNull(tonCertificateId)) {
							lazbInfo.setTonProof(
									this.uploadFile(tonCertificateId));
						}
					}, ThreadPoolUtil.getShippingOrderExecutor());

			// 六处水尺文件ID
			CompletableFuture<Void> sixWaterGaugesOneFileIdCompletableFuture = CompletableFuture
					.runAsync(() -> {
						Long sixWaterGaugesOneFileId = lazbInfo
								.getSixWaterGaugesOneFileId();
						if (Objects.nonNull(sixWaterGaugesOneFileId)) {
							lazbInfo.setSixWaterGaugesOneFileId(
									this.uploadFile(sixWaterGaugesOneFileId));
						}
					}, ThreadPoolUtil.getShippingOrderExecutor());

			CompletableFuture<Void> sixWaterGaugesTwoFileIdCompletableFuture = CompletableFuture
					.runAsync(() -> {
						Long sixWaterGaugesTwoFileId = lazbInfo
								.getSixWaterGaugesTwoFileId();
						if (Objects.nonNull(sixWaterGaugesTwoFileId)) {
							lazbInfo.setSixWaterGaugesTwoFileId(
									this.uploadFile(sixWaterGaugesTwoFileId));
						}
					}, ThreadPoolUtil.getShippingOrderExecutor());

			CompletableFuture<Void> sixWaterGaugesThreeFileIdCompletableFuture = CompletableFuture
					.runAsync(() -> {
						Long sixWaterGaugesThreeFileId = lazbInfo
								.getSixWaterGaugesThreeFileId();
						if (Objects.nonNull(sixWaterGaugesThreeFileId)) {
							lazbInfo.setSixWaterGaugesThreeFileId(
									this.uploadFile(sixWaterGaugesThreeFileId));
						}
					}, ThreadPoolUtil.getShippingOrderExecutor());

			CompletableFuture<Void> sixWaterGaugesFourFileIdCompletableFuture = CompletableFuture
					.runAsync(() -> {
						Long sixWaterGaugesFourFileId = lazbInfo
								.getSixWaterGaugesFourFileId();
						if (Objects.nonNull(sixWaterGaugesFourFileId)) {
							lazbInfo.setSixWaterGaugesFourFileId(
									this.uploadFile(sixWaterGaugesFourFileId));
						}
					}, ThreadPoolUtil.getShippingOrderExecutor());

			CompletableFuture<Void> sixWaterGaugesFiveFileIdCompletableFuture = CompletableFuture
					.runAsync(() -> {
						Long sixWaterGaugesFiveFileId = lazbInfo
								.getSixWaterGaugesFiveFileId();
						if (Objects.nonNull(sixWaterGaugesFiveFileId)) {
							lazbInfo.setSixWaterGaugesFiveFileId(
									this.uploadFile(sixWaterGaugesFiveFileId));
						}
					}, ThreadPoolUtil.getShippingOrderExecutor());

			CompletableFuture<Void> sixWaterGaugesSixFileIdCompletableFuture = CompletableFuture
					.runAsync(() -> {
						Long sixWaterGaugesSixFileId = lazbInfo
								.getSixWaterGaugesSixFileId();
						if (Objects.nonNull(sixWaterGaugesSixFileId)) {
							lazbInfo.setSixWaterGaugesSixFileId(
									this.uploadFile(sixWaterGaugesSixFileId));
						}
					}, ThreadPoolUtil.getShippingOrderExecutor());

			CompletableFuture.allOf(loadVideoIdCompletableFuture,
					sixWaterGaugesOneFileIdCompletableFuture,
					sixWaterGaugesTwoFileIdCompletableFuture,
					sixWaterGaugesThreeFileIdCompletableFuture,
					sixWaterGaugesFourFileIdCompletableFuture,
					sixWaterGaugesFiveFileIdCompletableFuture,
					sixWaterGaugesSixFileIdCompletableFuture,
					tonCertificateIdCompletableFuture).join();

			newTransportOrderDetailsShip.setLazbInfo(lazbInfo);
			LambdaUpdateWrapper<TransportOrderDetailsShip> wrapper = new LambdaUpdateWrapper<>();
			wrapper.set(TransportOrderDetailsShip::getLazbInfo, lazbInfo);
			transportOrderDetailsShipService.updateByIdAndWrapper(
					newTransportOrderDetailsShip.getId(), wrapper);
			log.info("处理离港准备信息");
		}
	}

	/**
	 * 卸货信息
	 *
	 * @param transportOrderDetailsShip
	 */
	private void handleUnloadingInfo(
			TransportOrderDetailsShip newTransportOrderDetailsShip,
			ThirdTransportOrderDetailsShip transportOrderDetailsShip) {
		log.info("开始卸货信息 ===> {}", LocalDateTime.now());
		// 卸货信息
		UnloadingInfo unloadingInfo = transportOrderDetailsShip
				.getUnloadingInfo();
		if (Objects.nonNull(unloadingInfo)) {
			CompletableFuture<Void> unloadingVideoIdCompletableFuture = CompletableFuture
					.runAsync(() -> {
						// 卸货视频ID
						Long unloadingVideoId = unloadingInfo
								.getUnloadingVideoId();
						if (Objects.nonNull(unloadingVideoId)) {
							unloadingInfo.setUnloadingVideoId(
									this.uploadFile(unloadingVideoId));
						}
					}, ThreadPoolUtil.getShippingOrderExecutor());

			CompletableFuture<Void> clearanceVideoIdCompletableFuture = CompletableFuture
					.runAsync(() -> {
						// 清仓视频ID
						Long clearanceVideoId = unloadingInfo
								.getClearanceVideoId();
						if (Objects.nonNull(clearanceVideoId)) {
							unloadingInfo.setClearanceVideoId(
									this.uploadFile(clearanceVideoId));
						}
					}, ThreadPoolUtil.getShippingOrderExecutor());

			CompletableFuture.allOf(unloadingVideoIdCompletableFuture,
					clearanceVideoIdCompletableFuture).join();
			newTransportOrderDetailsShip.setUnloadingInfo(unloadingInfo);

			LambdaUpdateWrapper<TransportOrderDetailsShip> wrapper = new LambdaUpdateWrapper<>();
			wrapper.set(TransportOrderDetailsShip::getUnloadingInfo,
					unloadingInfo);
			transportOrderDetailsShipService.updateByIdAndWrapper(
					newTransportOrderDetailsShip.getId(), wrapper);
			log.info("结束卸货信息 ===> {}", LocalDateTime.now());
		}
	}

	/**
	 * 处理接档信息
	 *
	 * @param transportOrderDetailsShip
	 */
	private void handleGearShift(
			TransportOrderDetailsShip newTransportOrderDetailsShip,
			ThirdTransportOrderDetailsShip transportOrderDetailsShip) {
		ArrayGearShift jdInfo = transportOrderDetailsShip.getJdInfo();
		ArrayGearShift newJdInfo = new ArrayGearShift();
		log.info("处理接档信息开始 ===> {}", LocalDateTime.now());
		if (CollectionUtils.isNotEmpty(jdInfo)) {
			List<CompletableFuture<Void>> futures = new ArrayList<>();

			for (GearShift gearShift : jdInfo) {

				Long makeFileVideoId = gearShift.getMakeFileVideoId();
				if (Objects.nonNull(makeFileVideoId)) {

					futures.add(CompletableFuture.runAsync(
							() -> gearShift.setMakeFileVideoId(
									this.uploadFile(makeFileVideoId)),
							ThreadPoolUtil.getShippingOrderExecutor()));

				}

				Long sandPileVideoId = gearShift.getSandPileVideoId();
				if (Objects.nonNull(sandPileVideoId)) {
					futures.add(CompletableFuture.runAsync(
							() -> gearShift.setSandPileVideoId(
									this.uploadFile(sandPileVideoId)),
							ThreadPoolUtil.getShippingOrderExecutor()));

				}

				Long rightWaterGaugePhotoId = gearShift
						.getRightWaterGaugePhotoId();
				if (Objects.nonNull(rightWaterGaugePhotoId)) {
					futures.add(CompletableFuture.runAsync(
							() -> gearShift.setRightWaterGaugePhotoId(
									this.uploadFile(rightWaterGaugePhotoId)),
							ThreadPoolUtil.getShippingOrderExecutor()));

				}

				Long dockingVideoId = gearShift.getDockingVideoId();
				if (Objects.nonNull(dockingVideoId)) {
					futures.add(CompletableFuture.runAsync(
							() -> gearShift.setDockingVideoId(
									this.uploadFile(dockingVideoId)),
							ThreadPoolUtil.getShippingOrderExecutor()));
				}

				Long emptyWaterGaugePhotoId = gearShift
						.getEmptyWaterGaugePhotoId();
				if (Objects.nonNull(emptyWaterGaugePhotoId)) {
					futures.add(CompletableFuture.runAsync(
							() -> gearShift.setEmptyWaterGaugePhotoId(
									this.uploadFile(emptyWaterGaugePhotoId)),
							ThreadPoolUtil.getShippingOrderExecutor()));

				}

				Long emptyWaterGaugeVideoId = gearShift
						.getEmptyWaterGaugeVideoId();
				if (Objects.nonNull(emptyWaterGaugeVideoId)) {
					futures.add(CompletableFuture.runAsync(
							() -> gearShift.setEmptyWaterGaugeVideoId(
									this.uploadFile(emptyWaterGaugeVideoId)),
							ThreadPoolUtil.getShippingOrderExecutor()));

				}

				Long emptyWarehousePhotoId = gearShift
						.getEmptyWarehousePhotoId();
				if (Objects.nonNull(emptyWarehousePhotoId)) {
					futures.add(CompletableFuture.runAsync(
							() -> gearShift.setEmptyWarehousePhotoId(
									this.uploadFile(emptyWarehousePhotoId)),
							ThreadPoolUtil.getShippingOrderExecutor()));
				}

				Long bottomPressureChamberPhotoId = gearShift
						.getBottomPressureChamberPhotoId();
				if (Objects.nonNull(bottomPressureChamberPhotoId)) {
					futures.add(CompletableFuture.runAsync(
							() -> gearShift.setBottomPressureChamberPhotoId(this
									.uploadFile(bottomPressureChamberPhotoId)),
							ThreadPoolUtil.getShippingOrderExecutor()));
				}

				Long loadingVideoId = gearShift.getLoadingVideoId();
				if (Objects.nonNull(loadingVideoId)) {
					futures.add(CompletableFuture.runAsync(
							() -> gearShift.setLoadingVideoId(
									this.uploadFile(loadingVideoId)),
							ThreadPoolUtil.getShippingOrderExecutor()));
				}

				newJdInfo.add(gearShift);
			}

			// 等待所有异步任务完成
			CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
					.join();

			newTransportOrderDetailsShip.setJdInfo(newJdInfo);
			LambdaUpdateWrapper<TransportOrderDetailsShip> wrapper = new LambdaUpdateWrapper<>();
			wrapper.set(TransportOrderDetailsShip::getJdInfo, newJdInfo);
			transportOrderDetailsShipService.updateByIdAndWrapper(
					newTransportOrderDetailsShip.getId(), wrapper);

			log.info("处理接档信息完成 ===> {}", LocalDateTime.now());
		}
	}

	/**
	 * 处理支付文件
	 *
	 * @param transportOrderShip
	 */
	private void handlePayFile(String id,
			ThirdTransportOrderShip transportOrderShip,List<ShippingDepositInfo> ownerShippingDeposits) {
		TransportOrderShip newTransportOrderShip = new TransportOrderShip();
		newTransportOrderShip.setId(id);
		// 货主支付凭证文件ID
		Long ownerPayFileId = transportOrderShip.getOwnerPayFileId();
		if (CollectionUtils.isNotEmpty(ownerShippingDeposits)) {
			log.info("处理支付文件");
			for (ShippingDepositInfo shippingDepositInfo : ownerShippingDeposits) {
				shippingDepositInfo.setPaymentFileId(this
						.uploadFile(shippingDepositInfo.getPaymentFileId()));
				shippingDepositInfo.setReceiptFileId(this
						.uploadFile(shippingDepositInfo.getReceiptFileId()));
			}
			newTransportOrderShip.setAdvanceDeposit(
					new ArrayShippingDepositInfo(ownerShippingDeposits));
		}
		if (Objects.nonNull(ownerPayFileId)) {
			newTransportOrderShip
					.setOwnerPayFileId(this.uploadFile(ownerPayFileId));
		}
		transportOrderShipService.update(newTransportOrderShip);
	}

	/**
	 * 设置状态
	 *
	 * @param transportOrderShip
	 * @param newTransportOrderShip
	 */
	private void handleShippingOrderState(
			ThirdTransportOrderShip transportOrderShip,
			TransportOrderShip newTransportOrderShip) {
		// 处理状态
		switch (TransportOrderShipDef.ThirdState
				.from(transportOrderShip.getState())) {
			case SHIP_INFO_FEE_TO_BE_PAID, DEPOSIT_TO_BE_PAID, TO_BE_LOADED,
					DURING_LOADING ->
				newTransportOrderShip.setState(
						TransportOrderShipDef.State.TO_BE_LOADED.getCode());
			case AWAITING_DEPARTURE -> newTransportOrderShip.setState(
					TransportOrderShipDef.State.AWAITING_DEPARTURE.getCode());
			case DURING_TRANSPORTATION -> newTransportOrderShip
					.setState(TransportOrderShipDef.State.DURING_TRANSPORTATION
							.getCode());
			case TO_BE_UNLOADED, DURING_UNLOADING, DISCHARGED ->
				newTransportOrderShip.setState(
						TransportOrderShipDef.State.DISCHARGED.getCode());
			case COMPLETED -> {
				newTransportOrderShip.setState(
						TransportOrderShipDef.State.DISCHARGED.getCode());
				newTransportOrderShip
						.setIsFinishUnload(CommonDef.Symbol.YES.getCode());
			}
		}
		newTransportOrderShip.setThirdState(transportOrderShip.getState());
	}

	/**
	 * 从链云下载文件上传到中盛
	 *
	 * @param fileId
	 * @return
	 */
	private Long uploadFile(Long fileId) {
		try {
			if (Objects.isNull(fileId)) {
				return null;
			}
			File file = fileConnectClient.findById(fileId).getData();
			if (Objects.nonNull(file)) {
				MultipartFile multipartFile = MultipartFileUtil
						.urlToMultipartFile(file.getPath(), file.getName());
				File newFile = fileService.upload(multipartFile, file.getName())
						.orElseThrow(null);
				fileService.active(newFile.getId());
				return newFile.getId();
			}
			return null;
		} catch (Exception e) {
			log.error("从链云下载文件上传到中盛异常 ===> {}", e.getMessage());
			throw new RuntimeException(e);
		}
	}

	/**
	 * 校验船运单线下新增
	 * 
	 * @param form
	 * @return
	 */
	public TransportOrderShip validateOfflineCreate(
			TransportOrderShipOfflineCreateForm form) {
		// 校验码头
		Port sourcePort = portClient.findById(form.getSourcePortId()).getData();
		Port destinationPort = portClient.findById(form.getDestinationPortId())
				.getData();
		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(form.getGoodsId());

		orderValidator.validateExist(form.getOrderId());

		// 发货完成不能新增删除船运单
		if (DeliverGoodsDef.Status.DELIVER_COMPLETE
				.match(deliverGoods.getStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30139030);
		}

		if (form.getUnloadTime().isBefore(form.getLoadingTime())) {
			throw new BadRequestException(ErrorCode.CODE_30137069);
		}

		Project project = projectValidator
				.validateExistProject(deliverGoods.getProjectId());
		projectValidator.validateProjectPeople(deliverGoods.getProjectId());

		TransportOrderShip transportOrderShip = form.convertToEntity();
		this.validateShip(form.getShipId(), transportOrderShip);
		transportOrderShip.setProjectId(deliverGoods.getProjectId());
		transportOrderShip.setContractId(deliverGoods.getContractId());
		transportOrderShip.setSourcePortName(sourcePort.getShortName());
		transportOrderShip
				.setDestinationPortName(destinationPort.getShortName());
		transportOrderShip.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(
				redisClient, project.getCode(),
				RedisKeys.Cache.TRANSPORT_ORDER_SHIP,
				AutoCodeDef.BusinessRuleCode.TRANSPORT_ORDER_SHIP.getCode(), 4,
				AutoCodeDef.DATE_TYPE.yy));
        if(Objects.nonNull(deliverGoods.getPurchaserId())) {
            customerService.findOne(deliverGoods.getPurchaserId())
                    .ifPresent(customer -> {
                        transportOrderShip.setOwnerId(customer.getId());
                        transportOrderShip.setOwnerName(customer.getRealName());
                    });
        }

		return transportOrderShip;
	}

	/**
	 * 校验船运单线下修改
	 * 
	 * @param id
	 * @param form
	 * @return
	 */
	public TransportOrderShip validateOfflineUpdate(String id,
			TransportOrderShipOfflineUpdateForm form, Integer createType) {
		TransportOrderShip transportOrderShip = this.validateExist(id);

        if(!Objects.equals(createType, transportOrderShip.getCreateType())) {
            throw new DynamicsBadRequestException(ErrorCode.CODE_30138024, TransportOrderRailwayDef.CreateType.from(transportOrderShip.getCreateType()).getName());
        }
		if (TransportOrderShipDef.State.CANCELED
				.match(transportOrderShip.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30138022);
		}

		if (form.getUnloadTime().isBefore(form.getLoadingTime())) {
			throw new BadRequestException(ErrorCode.CODE_30137069);
		}

		// 校验码头
		Port sourcePort = portClient.findById(form.getSourcePortId()).getData();
		Port destinationPort = portClient.findById(form.getDestinationPortId())
				.getData();
		DeliverGoods deliverGoods = deliverGoodsValidator
				.validateExist(transportOrderShip.getGoodsId());

		if (!DeliverGoodsDef.Status.DELIVER_COMPLETE
				.match(deliverGoods.getStatus())) {
			transportOrderShip.setTon(form.getTon());
		}

		projectValidator.validateProjectPeople(deliverGoods.getProjectId());
		transportOrderShip = form.update(transportOrderShip);
		this.validateShip(form.getShipId(), transportOrderShip);
		transportOrderShip.setSourcePortName(sourcePort.getShortName());
		transportOrderShip
				.setDestinationPortName(destinationPort.getShortName());
		return transportOrderShip;
	}

}
