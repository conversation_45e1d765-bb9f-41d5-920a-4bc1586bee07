package com.zhihaoscm.service.config.intceptor;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.common.bean.entity.BaseEntityWithLongId;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.domain.meta.biz.SystemDef;
import com.zhihaoscm.service.config.properties.SupplierChainProperties;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.ContractService;

import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.*;

@Slf4j
public class DataPermissionInterceptor implements InnerInterceptor {

	@Override
	public void beforeQuery(Executor executor, MappedStatement ms,
			Object parameter, RowBounds rowBounds, ResultHandler resultHandler,
			BoundSql boundSql) {
		PluginUtils.MPBoundSql mpBs = PluginUtils.mpBoundSql(boundSql);
		String originalSql = boundSql.getSql();

		// 以下表不需要判断tenantId
		List<String> tableList = Arrays.asList(" t_bank_role ",
				" t_bank_security_setting_device ", " t_bank_user ",
				" t_contract_lock_record ", " t_contract_record ",
				" t_custom_security_setting_device ", " t_customer ",
				" t_customer_bank ", " t_customer_enterprise ",
				" t_customer_enterprise_certification ",
				" t_customer_invoice_header ",
				" t_customer_personal_certification ",
				" t_customer_receiving_address ", " t_dept ", " t_file ",
				" t_institution_apply ", " t_person ", " t_person_dept ",
				" t_regular_customers ", " t_seal ", " t_trajectory ");

		try {
			Statement statement = CCJSqlParserUtil.parse(originalSql);
			if (statement instanceof Select selectStatement) {
				UserInfoContext context = UserInfoContextHolder.getContext();

				if (Objects.nonNull(context)
						&& Objects.nonNull(context.getTenantId())) {

					// 检查是否包含排除的表
					for (String table : tableList) {
						if (originalSql.contains(table)
								&& !(originalSql.contains("JOIN")
										|| originalSql.contains("join"))) {
							// 找到匹配的表后直接返回
							return;
						}
					}

					Long tenantId = context.getTenantId();
					// 递归处理所有的SelectBody，包括UNION查询和子查询
					processSelectBody(selectStatement.getSelectBody(),
							tenantId);
					mpBs.sql(selectStatement.toString());
				}
			}
		} catch (Exception e) {
			log.warn("add tenant_id condition error, sql: {}", originalSql, e);
		}
	}

	/**
	 * 递归处理SelectBody，支持复杂的UNION查询和子查询
	 *
	 * @param selectBody
	 *            查询体
	 * @param tenantId
	 *            租户ID
	 */
	private void processSelectBody(SelectBody selectBody, Long tenantId) {
		if (selectBody instanceof PlainSelect plainSelect) {
			// 处理普通查询
			processPlainSelect(plainSelect, tenantId);
		} else if (selectBody instanceof SetOperationList setOperationList) {
			// 处理UNION查询
			for (SelectBody body : setOperationList.getSelects()) {
				processSelectBody(body, tenantId);
			}
		}
	}

	/**
	 * 处理PlainSelect，为主表添加tenant_id条件
	 *
	 * @param plainSelect
	 *            普通查询
	 * @param tenantId
	 *            租户ID
	 */
	private void processPlainSelect(PlainSelect plainSelect, Long tenantId) {
		// 检查FROM子句中的主表是否需要添加tenant_id条件
		if (shouldAddTenantCondition(plainSelect)) {
			// 获取主表的别名
			String tableAlias = getMainTableAlias(plainSelect);

			EqualsTo equalsTo = new EqualsTo();
			// 如果有别名，使用别名.tenant_id，否则使用tenant_id
			if (tableAlias != null && !tableAlias.isEmpty()) {
				equalsTo.setLeftExpression(
						new Column(new Table(tableAlias), "tenant_id"));
			} else {
				equalsTo.setLeftExpression(new Column("tenant_id"));
			}
			equalsTo.setRightExpression(new LongValue(tenantId));

			// 添加到WHERE条件
			if (plainSelect.getWhere() == null) {
				plainSelect.setWhere(equalsTo);
			} else {
				AndExpression andExpression = new AndExpression(
						plainSelect.getWhere(), equalsTo);
				plainSelect.setWhere(andExpression);
			}
		}

		// 递归处理子查询
		processSubQueries(plainSelect, tenantId);
	}

	/**
	 * 获取主表的别名
	 *
	 * @param plainSelect
	 *            普通查询
	 * @return 主表别名，如果没有别名则返回null
	 */
	private String getMainTableAlias(PlainSelect plainSelect) {
		FromItem fromItem = plainSelect.getFromItem();
		if (fromItem instanceof Table table) {
			// 获取表的别名
			return table.getAlias() != null ? table.getAlias().getName() : null;
		}
		return null;
	}

	/**
	 * 判断是否应该为当前查询添加tenant_id条件
	 *
	 * @param plainSelect
	 *            普通查询
	 * @return 是否应该添加条件
	 */
	private boolean shouldAddTenantCondition(PlainSelect plainSelect) {
		// 检查FROM子句中是否有实际的表（而不是子查询）
		FromItem fromItem = plainSelect.getFromItem();
		return fromItem instanceof Table; // 主表是实际表，需要添加条件
		// 如果FROM是子查询，则不在这里添加条件，而是在子查询中处理
	}

	/**
	 * 递归处理子查询
	 *
	 * @param plainSelect
	 *            普通查询
	 * @param tenantId
	 *            租户ID
	 */
	private void processSubQueries(PlainSelect plainSelect, Long tenantId) {
		// 处理FROM子句中的子查询
		FromItem fromItem = plainSelect.getFromItem();
		if (fromItem instanceof SubSelect subSelect) {
			processSelectBody(subSelect.getSelectBody(), tenantId);
		}

		// 处理JOIN中的子查询
		List<Join> joins = plainSelect.getJoins();
		if (joins != null) {
			for (Join join : joins) {
				FromItem rightItem = join.getRightItem();
				if (rightItem instanceof SubSelect subSelect) {
					processSelectBody(subSelect.getSelectBody(), tenantId);
				}
			}
		}
	}

	/**
	 * 在 update 操作前执行（包括 INSERT/UPDATE/DELETE） 这里仅处理 INSERT 操作，为实体填充 tenantId
	 */
	@Override
	public void beforeUpdate(Executor executor, MappedStatement ms,
			Object parameter) {
		// 1. 判断当前操作是否为“新增”（INSERT）
		if (ms.getSqlCommandType() != SqlCommandType.INSERT) {
			return; // 非新增操作，直接返回
		}

		// 2. 从参数中提取实体对象（处理 MyBatis-Plus 可能的包装逻辑）
		Object entity = extractEntity(parameter);
		if (entity == null) {
			return; // 无实体对象，无需处理
		}

		// 3. 为实体填充 tenantId（仅当字段为 null 时）
		fillTenantId(entity);

		SupplierChainProperties supplierChainProperties = (SupplierChainProperties) SpringUtil
				.getBean("supplierChainProperties");

		if (SystemDef.SystemType.INDEPENDENCE_DEPLOYMENT
				.match(supplierChainProperties.getType())) {
			// 独立部署模式下，直接设置 tenantId
			if (entity instanceof BaseEntityWithLongId) {
				((BaseEntityWithLongId) entity)
						.setTenantId(supplierChainProperties.getTenantId());
			} else if (entity instanceof BaseEntityWithStringId) {
				((BaseEntityWithStringId) entity)
						.setTenantId(supplierChainProperties.getTenantId());
			}
		}

	}

	/**
	 * 从参数中提取实体对象 说明：MyBatis-Plus 执行新增时，参数可能是实体本身，也可能是包装在 Map 中（key 为 "entity"）
	 */
	private Object extractEntity(Object parameter) {
		if (parameter == null) {
			return null;
		}

		// 处理参数为 Map 的情况（例如通过 Wrapper 新增时，实体被包装在 Map 中）
		if (parameter instanceof Map<?, ?> paramMap) {
			// MyBatis-Plus 约定：Map 中 key 为 Constants.ENTITY 时对应实体对象
			if (paramMap.containsKey(Constants.ENTITY)) {
				return paramMap.get(Constants.ENTITY);
			}
		}

		// 处理参数为数组/集合的情况（批量新增时，参数可能是实体集合）
		if (parameter.getClass().isArray()) {
			assert parameter instanceof Object[];
			Object[] array = (Object[]) parameter;
			if (array.length > 0) {
				return array[0]; // 取第一个元素（批量新增时需循环处理，这里简化为单元素）
			}
		}

		// 直接返回参数（若参数本身就是实体对象）
		return parameter;
	}

	/**
	 * 为实体填充 tenantId 字段 逻辑：通过反射查找实体（含父类）的 tenantId 字段，若为 null 则设置当前租户 ID
	 */
	private void fillTenantId(Object entity) {
		try {
			if (CustomerContextHolder.getCustomerLoginVo() != null
					&& this.getCurrentTenantId() != null) {
				// 如果 UserContextHolder 中没有用户信息，则说明为客户端
				// 客户端业务逻辑：通过 contractId 查找tenantId

				ContractService contractService = SpringUtil
						.getBean(ContractService.class);

				// 查找实体类中名为“contractId”的字段
				Field contractIdIdField = findContractIdIdField(
						entity.getClass());
				if (contractIdIdField == null) {
					return; // 实体不含 tenantId 字段，无需处理
				}

				// 允许访问私有字段
				contractIdIdField.setAccessible(true);

				if (contractIdIdField.getType().getName()
						.equals("java.lang.Long")) {
					return;
				}

				// 仅当字段值为 null 时填充（避免覆盖已有值）
				if (contractIdIdField.get(entity) != null) {
					// 获取 contractId 字段值
					String contractId = (String) contractIdIdField.get(entity);
					contractService.findOne(contractId).ifPresent(contract -> {
						// 在context中设置 tenantId
						UserInfoContextHolder.getContext()
								.setTenantId(contract.getTenantId());
					});
				}
			}

			// 如果参数是实体类，自动注入tenantId
			if (entity instanceof BaseEntityWithLongId) {
				if (((BaseEntityWithLongId) entity).getTenantId() == null) {
					((BaseEntityWithLongId) entity)
							.setTenantId(this.getCurrentTenantId());
				}
			}

			if (entity instanceof BaseEntityWithStringId) {
				if (((BaseEntityWithStringId) entity).getTenantId() == null) {
					((BaseEntityWithStringId) entity)
							.setTenantId(this.getCurrentTenantId());
				}
			}

		} catch (Exception e) {
			throw new RuntimeException("新增数据时填充 tenantId 失败", e);
		}
	}

	/**
	 * 递归查找类（含父类）中名为“tenantId”的字段
	 */
	private Field findContractIdIdField(Class<?> clazz) {
		// 递归终止条件：已查找到 Object 类
		if (clazz == null || clazz == Object.class) {
			return null;
		}
		// 在当前类中查找
		try {
			return clazz.getDeclaredField("contractId");
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 获取当前租户 ID（实际场景需从上下文获取） 示例：从 ThreadLocal 中获取（多租户场景常用方式）
	 */
	private Long getCurrentTenantId() {
		UserInfoContext context = UserInfoContextHolder.getContext();
		if (context != null) {
			return context.getTenantId(); // 模拟租户 ID
		}
		return null;
	}
}