package com.zhihaoscm.service.resource.form.project;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.domain.bean.entity.Goods;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.Length;
import com.zhihaoscm.domain.bean.entity.Project;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

@Data
@Schema(name = "ProjectUpdateForm", description = "修改项目表单")
public class ProjectUpdateForm {

	@Schema(title = "项目名称")
	@NotBlank(message = ErrorCode.CODE_30152001)
	@Length(min = 1, max = 32, message = ErrorCode.CODE_30152002)
	private String name;

	@Schema(title = "项目备注")
	@Length(max = 200, message = ErrorCode.CODE_30152004)
	private String introduction;

	@Schema(title = "上游供应商是否自录数据")
	@NotNull(message = ErrorCode.CODE_30152043)
	private Integer supplierIsRecorded;

	@Schema(title = "上游供应商ids")
	@NotNull(message = ErrorCode.CODE_30152005)
	private ArrayLong supplierIds;

	@Schema(description = "上游供应商结算方式：1.先货后款/2.先款后货")
	@Range(min = 1, max = 2, message = ErrorCode.CODE_30151031)
	private Integer supplierSettleWay;

	@Schema(description = "上游供应商预付货款")
	@Digits(integer = 10, fraction = 2, message = ErrorCode.CODE_30152032)
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30152032)
	@DecimalMax(value = "9999999999.99", message = ErrorCode.CODE_30152032)
	private BigDecimal supplierPrePayment;

	@Schema(description = "上游供应商预付货款比例")
	@Digits(integer = 2, fraction = 2, message = ErrorCode.CODE_30152033)
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30152033)
	@DecimalMax(value = "99.99", message = ErrorCode.CODE_30152033)
	private BigDecimal supplierPrePaymentRatio;

	@Schema(description = "上游供应商账期")
	@Range(min = 1, max = 999, message = ErrorCode.CODE_30152034)
	private Integer supplierSettlePeriod;

	@Schema(title = "下游采购方是否自录数据")
	@NotNull(message = ErrorCode.CODE_30152044)
	private Integer customerIsRecorded;

	@Schema(title = "下游采购方ids")
	@NotNull(message = ErrorCode.CODE_30152006)
	private ArrayLong customerIds;

	@Schema(description = "下游采购方结算方式：1.先货后款/2.先款后货")
	@Range(min = 1, max = 2, message = ErrorCode.CODE_30151031)
	private Integer customerSettleWay;

	@Schema(description = "下游采购方预付货款")
	@Digits(integer = 10, fraction = 2, message = ErrorCode.CODE_30152032)
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30152032)
	@DecimalMax(value = "9999999999.99", message = ErrorCode.CODE_30152032)
	private BigDecimal customerPrePayment;

	@Schema(description = "下游采购方预付货款比例")
	@Digits(integer = 2, fraction = 2, message = ErrorCode.CODE_30152033)
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30152033)
	@DecimalMax(value = "99.99", message = ErrorCode.CODE_30152033)
	private BigDecimal customerPrePaymentRatio;

	@Schema(description = "下游采购方账期")
	@Range(min = 1, max = 999, message = ErrorCode.CODE_30152034)
	private Integer customerSettlePeriod;

	@Schema(description = "是否存在仓储")
	@NotNull(message = ErrorCode.CODE_30152031)
	private Integer isExistStorage;

	@Schema(title = "监管单位")
	@Length(max = 32, message = ErrorCode.CODE_30152045)
	private String supervisingUnit;

	@Schema(title = "仓库ids")
	private List<String> warehouseIds;

	@Schema(title = "库位ids")
	private List<Long> storageIds;

	@Schema(description = "出库条件")
	private Integer outCondition;

	@Schema(description = "付款比例")
	@Digits(integer = 2, fraction = 2, message = ErrorCode.CODE_30152029)
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30152029)
	@DecimalMax(value = "99.99", message = ErrorCode.CODE_30152029)
	private BigDecimal paymentRatio;

	@Schema(description = "余款账期")
	@Range(min = 1, max = 999, message = ErrorCode.CODE_30152030)
	private Integer remainPaymentPeriod;

	@Schema(description = "控货比要求方式")
	private Integer controlRatioWay;

	@Schema(title = "库存控货比")
	@Digits(integer = 3, fraction = 2, message = ErrorCode.CODE_30152028)
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30152028)
	@DecimalMax(value = "999.99", message = ErrorCode.CODE_30152028)
	private BigDecimal inventoryControlRatio;

	@Schema(title = "库存金额")
	@Digits(integer = 8, fraction = 2, message = ErrorCode.CODE_30152035)
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30152035)
	@DecimalMax(value = "99999999.99", message = ErrorCode.CODE_30152035)
	private BigDecimal inventoryAmount;

	@Schema(title = "货物id")
	@NotNull(message = ErrorCode.CODE_30152021)
	private Long goodsId;

	@Schema(title = "货物名称")
	@NotNull(message = ErrorCode.CODE_30152022)
	private String goodsName;

	@Schema(title = "项目负责人id")
	@NotNull(message = ErrorCode.CODE_30152007)
	private Long supervisorId;

	@Schema(title = "项目负责人名称")
	@NotNull(message = ErrorCode.CODE_30152008)
	private String supervisorName;

	@Schema(title = "项目经理ids")
	@NotNull(message = ErrorCode.CODE_30152009)
	private ArrayLong projectManagerIds;

	@Schema(title = "有效期开始日期")
	private LocalDateTime validBeginTime;

	@Schema(title = "有效期结束日期")
	private LocalDateTime validEndTime;

	@Schema(title = "项目资金敞口上限")
	@Digits(integer = 10, fraction = 2, message = ErrorCode.CODE_30152026)
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30152026)
	@DecimalMax(value = "9999999999.99", message = ErrorCode.CODE_30152026)
	private BigDecimal projectMaxLimit;

	@Schema(title = "项目预估年化收益")
	@Digits(integer = 2, fraction = 2, message = ErrorCode.CODE_30152027)
	@DecimalMin(value = "0.01", message = ErrorCode.CODE_30152027)
	@DecimalMax(value = "99.99", message = ErrorCode.CODE_30152027)
	private BigDecimal projectReturnRate;

	@Schema(title = "下游可提货余额公式")
	private String formula;

	@Schema(title = "项目可提货额公式项列表")
	private List<ProjectItemForm> projectItemForms;

	public Project convertToEntity(Project project, Goods goods) {
		// 设置项目信息
		project.setName(this.name);
		project.setIntroduction(this.introduction);
		project.setSupplierIds(this.supplierIds);
		project.setSupplierSettleWay(this.supplierSettleWay);
		project.setSupplierPrePayment(this.supplierPrePayment);
		project.setSupplierPrePaymentRatio(this.supplierPrePaymentRatio);
		project.setSupplierSettlePeriod(this.supplierSettlePeriod);
		project.setCustomerIds(this.customerIds);
		project.setCustomerSettleWay(this.customerSettleWay);
		project.setCustomerPrePayment(this.customerPrePayment);
		project.setCustomerPrePaymentRatio(this.customerPrePaymentRatio);
		project.setCustomerSettlePeriod(this.customerSettlePeriod);
		project.setIsExistStorage(this.isExistStorage);
		project.setWarehouseIds(new ArrayString(warehouseIds));
		project.setStorageIds(new ArrayLong(storageIds));
		project.setOutCondition(this.outCondition);
		project.setPaymentRatio(this.paymentRatio);
		project.setRemainPaymentPeriod(this.remainPaymentPeriod);
		project.setControlRatioWay(this.controlRatioWay);
		project.setInventoryControlRatio(this.inventoryControlRatio);
		project.setInventoryAmount(this.inventoryAmount);
		project.setGoodsId(this.goodsId);
		project.setGoodsName(this.goodsName);
		project.setGoodsUnit(goods.getUnit());
		project.setSupervisorId(this.supervisorId);
		project.setSupervisorName(this.supervisorName);
		project.setProjectManagerIds(this.projectManagerIds);
		project.setValidBeginTime(this.validBeginTime);
		project.setValidEndTime(this.validEndTime);
		project.setProjectMaxLimit(this.projectMaxLimit);
		project.setProjectReturnRate(this.projectReturnRate);
		project.setFormula(this.formula);
		project.setSupplierIsRecorded(this.supplierIsRecorded);
		project.setCustomerIsRecorded(this.customerIsRecorded);
		project.setSupervisingUnit(this.supervisingUnit);
		return project;
	}
}
