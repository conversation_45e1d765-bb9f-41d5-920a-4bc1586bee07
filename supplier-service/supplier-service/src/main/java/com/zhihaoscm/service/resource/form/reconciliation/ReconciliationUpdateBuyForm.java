package com.zhihaoscm.service.resource.form.reconciliation;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.DeliverGoods;
import com.zhihaoscm.domain.bean.vo.DeliverGoodsVo;
import org.hibernate.validator.constraints.Length;

import com.zhihaoscm.domain.bean.entity.Reconciliation;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ReconciliationUpdateBuyForm {

	@Schema(description = "提货列表")
	List<DeliverGoods> deliverGoodsList;

	@Schema(description = "提货列表")
	List<DeliverGoodsVo> deliverGoodsVoList;

	@Schema(title = "对账日期")
	private LocalDateTime reconciliationDate;

	@Schema(title = "对账金额")
	private BigDecimal reconciliationAmount;

	@Schema(title = "对账单据文件id")
	private Long reconciliationFileId;

	@Schema(description = "备注")
	@Length(max = 200, message = ErrorCode.CODE_30097026)
	private String remark;

	@Schema(description = "对账数量/重量")
	private BigDecimal reconciliationWeight;

	@Schema(title = "账期")
	private Integer settlePeriod;

	@Schema(title = "预结算方式")
	private Integer preSettleWay;

	@Schema(title = "预付比例/预付金额")
	private BigDecimal prePaymentValue;

	@Schema(title = "结算预付款")
	private BigDecimal settlementPayment;

	@Schema(title = "签署方式 1线上 2.线下")
	private Integer signType;

	@Schema(title = "预对账签署方式 1线上 2.线下")
	private Integer preSignType;

	@Schema(title = "订单保证金")
	private BigDecimal orderDeposit;

	@Schema(title = "保证金是否转货款：0.不转 1.转")
	private Integer depositTransferAmount;

	@Schema(title = "实际应付/应收金额")
	private BigDecimal actualAmount;

	@Schema(description = "保存类型")
	private Integer saveType;

	@Schema(title = "是否存在预对账：0.否 1.是")
	private Integer isPreReconciliation;

	@Schema(description = "预对账金额")
	private BigDecimal preReconciliationAmount;

	@Schema(description = "预对账重量/数量")
	private BigDecimal preReconciliationWeight;

	@Schema(title = "预对账日期")
	private LocalDateTime preReconciliationDate;

	@Schema(title = "预对账单据文件id")
	private Long preReconciliationFileId;

	public Reconciliation convertEntity(Reconciliation reconciliation,
			Integer initiator) {
		reconciliation.setReconciliationDate(this.reconciliationDate);
		reconciliation.setReconciliationAmount(this.reconciliationAmount);
		reconciliation.setReconciliationWeight(this.reconciliationWeight);
		reconciliation.setRemark(this.remark);
		reconciliation.setReconciliationFileId(this.reconciliationFileId);
		reconciliation.setReconciliationWeight(this.reconciliationWeight);
		reconciliation.setReconciliationDate(this.reconciliationDate);
		reconciliation.setReconciliationAmount(this.reconciliationAmount);
		reconciliation.setReconciliationFileId(this.reconciliationFileId);
		reconciliation.setRemark(this.remark);
		reconciliation.setUnbilledAmount(this.reconciliationAmount);
		reconciliation.setSettlePeriod(this.settlePeriod);
		reconciliation.setPreSettleWay(this.preSettleWay);
		reconciliation.setPrePaymentValue(this.prePaymentValue);
		reconciliation.setSettlementPayment(this.settlementPayment);
		reconciliation.setInitiator(initiator);
		reconciliation.setSignType(this.signType);
		reconciliation.setPreSignType(this.preSignType);
		reconciliation.setIsPreReconciliation(this.isPreReconciliation);
		reconciliation.setPreReconciliationDate(this.preReconciliationDate);
		reconciliation.setPreReconciliationAmount(this.preReconciliationAmount);
		reconciliation.setPreReconciliationWeight(this.preReconciliationWeight);
		reconciliation.setPreReconciliationFileId(this.preReconciliationFileId);
		reconciliation.setOrderDeposit(this.orderDeposit);
		reconciliation.setDepositTransferAmount(this.depositTransferAmount);
		reconciliation.setActualAmount(this.actualAmount);
		// 是预对账时 设置预对账信息
		if (CommonDef.Symbol.YES.match(this.isPreReconciliation)) {
			reconciliation
					.setIsConductReconciliation(CommonDef.Symbol.NO.getCode());
			// 设置预对账发起方
			reconciliation.setPreInitiator(initiator);
		} else {
			// 是对账时 设置对账信息
			reconciliation
					.setIsConductReconciliation(CommonDef.Symbol.YES.getCode());
			// 设置对账发起方
			reconciliation.setInitiator(initiator);
		}
		return reconciliation;
	}

}
