package com.zhihaoscm.service.resource.validator.signreceipt;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.Contract;
import com.zhihaoscm.domain.bean.entity.Inbound;
import com.zhihaoscm.domain.bean.entity.Reconciliation;
import com.zhihaoscm.domain.bean.entity.SignReceipt;
import com.zhihaoscm.domain.meta.biz.BusinessContractDef;
import com.zhihaoscm.domain.meta.biz.ContractDef;
import com.zhihaoscm.domain.meta.biz.OrderDef;
import com.zhihaoscm.domain.meta.biz.SignReceiptDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.resource.form.signreceipt.SignReceiptBuyCreateForm;
import com.zhihaoscm.service.resource.form.signreceipt.SignReceiptBuyUpdateForm;
import com.zhihaoscm.service.resource.form.signreceipt.SignReceiptCreateForm;
import com.zhihaoscm.service.resource.form.signreceipt.SignReceiptSaleCreateForm;
import com.zhihaoscm.service.resource.validator.contract.ContractValidator;
import com.zhihaoscm.service.resource.validator.customer.CustomerValidator;
import com.zhihaoscm.service.resource.validator.project.ProjectValidator;

/**
 * @description: 签收单校验器
 * @author: 彭湃
 * @date: 2025/1/17 9:50
 **/
@Component
public class SignReceiptValidator {

	@Autowired
	private SignReceiptService service;

	@Autowired
	private ProjectService projectService;

	@Autowired
	private ContractService contractService;

	@Autowired
	private ReconciliationService reconciliationService;

	@Autowired
	private CustomerValidator customerValidator;

	@Autowired
	private ContractValidator contractValidator;

	@Autowired
	private ProjectValidator projectValidator;

	@Autowired
	private InboundService inboundService;

	/**
	 * @description: 校验创建签收单
	 * @author: 彭湃
	 * @date: 2025/1/17 11:01
	 * @param: [form]
	 * @return: void
	 **/
	public void validateCreateSignReceipt(SignReceiptCreateForm form) {
		projectService.findOne(form.getProjectId()).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30012002));
		Contract contract = contractValidator
				.validateExist(form.getContractId());

		if (ContractDef.Type.SELL.match(contract.getContractType())
				&& Objects.isNull(form.getSignType())) {
			throw new BadRequestException(ErrorCode.CODE_30097025);
		}
		if (OrderDef.SignType.ONLINE.match(form.getSignType())) {
			customerValidator
					.validateSealAdmin(contract.getDownstreamPurchasersId());
		}
	}

	/**
	 * @description: 校验创建签收单
	 * @author: 彭湃
	 * @date: 2025/1/17 11:01
	 * @param: [form]
	 * @return: void
	 **/
	public void validateCreateBuySignReceipt(SignReceiptBuyCreateForm form) {
		projectService.findOne(form.getProjectId()).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30012002));
		Contract contract = contractValidator
				.validateExist(form.getContractId());
		projectValidator.validateProjectPeople(contract.getProjectId());
		if (Boolean.FALSE.equals(contractService
				.validateIsRecorded(contract.getId(),
						ContractDef.ContractType.PURCHASE.getCode())
				.orElse(null))) {
			if (Objects.isNull(form.getSignType())) {
				throw new BadRequestException(ErrorCode.CODE_30097025);
			}
		} else {
			if (Objects.isNull(form.getSignReceiptFileId())) {
				throw new BadRequestException(ErrorCode.CODE_30097031);
			}
		}
	}

	/**
	 * @description: 校验修改签收单
	 * @author: 彭湃
	 * @date: 2025/1/17 11:01
	 * @param: [form]
	 * @return: void
	 **/
	public SignReceipt validateUpdateBuy(String id,
			SignReceiptBuyUpdateForm form) {
		SignReceipt signReceipt = this.validateExist(id);
		Contract contract = contractValidator
				.validateExist(signReceipt.getContractId());
		projectValidator.validateProjectPeople(contract.getProjectId());
		if (Boolean.TRUE.equals(contractService
				.validateIsRecorded(contract.getId(),
						ContractDef.ContractType.PURCHASE.getCode())
				.orElse(null))) {
			if (Objects.isNull(form.getSignReceiptFileId())) {
				throw new BadRequestException(ErrorCode.CODE_30097031);
			}
		}
		return signReceipt;
	}

	/**
	 * @description: 校验签收单是否存在，不存在则抛出异常信息，存在则返回签收单信息
	 * @author: 彭湃
	 * @date: 2025/1/17 11:02
	 * @param: [id]
	 * @return: com.zhihaoscm.domain.bean.entity.SignReceipt
	 **/
	public SignReceipt validateExist(String id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30097001));
	}

	public SignReceipt validatePeople(String id) {
		SignReceipt signReceipt = service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30097001));
		Contract contract = contractValidator
				.validateExist(signReceipt.getContractId());
		projectValidator.validateProjectPeople(contract.getProjectId());
		return signReceipt;
	}

	public SignReceipt validateUpdate(String id) {
		SignReceipt signReceipt = this.validateExist(id);
		projectService.findOne(signReceipt.getProjectId()).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30012002));
		Contract contract = contractService.findOne(signReceipt.getContractId())
				.orElse(new Contract());
		if (OrderDef.SignType.ONLINE.match(signReceipt.getSignType())) {
			customerValidator
					.validateSealAdmin(contract.getDownstreamPurchasersId());
		}
		return signReceipt;
	}

	public SignReceipt validateInitiateSign(String id) {
		SignReceipt signReceipt = this.validateExist(id);
		// 线上才有签署
		if (!OrderDef.SignType.ONLINE.match(signReceipt.getSignType())) {
			throw new BadRequestException(ErrorCode.CODE_30151020);
		}
		if (CommonDef.AccountSource.INNER.match(signReceipt.getInitiator())) {
			// 管理后台：待发起
			if (!SignReceiptDef.Status.TO_BE_INITIATE
					.match(signReceipt.getStatus())) {
				throw new BadRequestException(ErrorCode.CODE_30151017);
			}
		} else {
			// 用户：草稿
			if (!SignReceiptDef.Status.DRAFT.match(signReceipt.getStatus())) {
				throw new BadRequestException(ErrorCode.CODE_30151017);
			}
		}

		return signReceipt;
	}

	/**
	 * @description: 校验删除签收单，如果签收单状态不是草稿状态则抛出异常信息，否则返回签收单信息
	 * @author: 彭湃
	 * @date: 2025/1/17 11:02
	 * @param: [id]
	 * @return: com.zhihaoscm.domain.bean.entity.SignReceipt
	 **/
	public SignReceipt validateDelete(String id) {
		SignReceipt signReceipt = this.validateExist(id);

		projectService.findOne(signReceipt.getProjectId()).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService.findOne(signReceipt.getContractId())
				.orElse(new Contract());
		projectValidator.validateProjectPeople(contract.getProjectId());
		// 采购项目
		if (ContractDef.Type.BUY.match(contract.getContractType())) {
			List<Reconciliation> bySignReceiptId = reconciliationService
					.findBySignReceiptId(id);
			if (!CollectionUtils.isEmpty(bySignReceiptId)) {
				throw new BadRequestException(ErrorCode.CODE_30097029);
			}
			List<Inbound> byReceiptId = inboundService.findByReceiptId(id);
			if (!CollectionUtils.isEmpty(byReceiptId)) {
				throw new BadRequestException(ErrorCode.CODE_30097030);
			}
		} else {
			if (!SignReceiptDef.Status.DRAFT.match(signReceipt.getStatus())
					&& !SignReceiptDef.Status.REVERTED
							.match(signReceipt.getStatus())) {
				throw new BadRequestException(ErrorCode.CODE_30097027);
			}
		}

		if (SignReceiptDef.Status.FINISHED.match(signReceipt.getStatus())
				&& Objects.nonNull(signReceipt.getReconciliationStatus())
				&& !OrderDef.BusinessStatus.NOT_STARTED
						.match(signReceipt.getReconciliationStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30097033);

		}
		return signReceipt;
	}

	public SignReceipt validateRevert(String id) {
		SignReceipt signReceipt = this.validatePeople(id);
		if (OrderDef.SignType.ONLINE.match(signReceipt.getSignType())) {
			if (!SignReceiptDef.Status.SIGNING.match(signReceipt.getStatus())) {
				throw new BadRequestException(ErrorCode.CODE_30151019);
			}
			if (!(BusinessContractDef.CommonSignState.UNSIGNED
					.match(signReceipt.getSignStatus())
					|| BusinessContractDef.CommonSignState.BUYER_SIGNED
							.match(signReceipt.getSignStatus()))) {
				throw new BadRequestException(ErrorCode.CODE_30151019);
			}
		} else {
			if (!SignReceiptDef.Status.CONFIRMING
					.match(signReceipt.getStatus())) {
				throw new BadRequestException(ErrorCode.CODE_30151019);
			}
		}
		return signReceipt;
	}

	public SignReceipt validateInvalid(String id, Integer initiator,
			Boolean flag) {
		SignReceipt signReceipt = this.validateExist(id);
		if (CommonDef.UserType.INNER.match(initiator) && flag) {
			projectValidator.validateProjectPeople(signReceipt.getProjectId());
		}
		if (!SignReceiptDef.Status.FINISHED.match(signReceipt.getStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30009046);
		}
		return signReceipt;
	}

	/**
	 * 校验作废
	 *
	 * @param id
	 * @param initiator
	 * @return
	 */
	public SignReceipt validateInvalidConfirm(String id, Integer initiator) {
		SignReceipt signReceipt = this.validateExist(id);
		if (CommonDef.UserType.INNER.match(initiator)) {
			projectValidator.validateProjectPeople(signReceipt.getProjectId());
		}
		return signReceipt;
	}

	/**
	 * 校验销售签收单新增
	 * 
	 * @param form
	 */
	public void validateCreateSale(SignReceiptSaleCreateForm form) {
		projectService.findOne(form.getProjectId()).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30012002));
		Contract contract = contractValidator
				.validateExist(form.getContractId());
		projectValidator.validateProjectPeople(contract.getProjectId());
		if (Boolean.FALSE.equals(contractService
				.validateIsRecorded(contract.getId(),
						ContractDef.ContractType.SALES.getCode())
				.orElse(null))) {
			throw new BadRequestException(ErrorCode.CODE_30097032);
		}
	}

	/**
	 * 校验提交签收单
	 * 
	 * @param id
	 * @return
	 */
	public SignReceipt validateSubmit(String id) {
		SignReceipt signReceipt = this.validateExist(id);
		projectValidator.validateProjectPeople(signReceipt.getProjectId());
		if (!SignReceiptDef.Status.DRAFT.match(signReceipt.getStatus())) {
			throw new BadRequestException(ErrorCode.CODE_30151017);
		}
		signReceipt.setStatus(SignReceiptDef.Status.TO_BE_SIGNED.getCode());
		return signReceipt;
	}
}
