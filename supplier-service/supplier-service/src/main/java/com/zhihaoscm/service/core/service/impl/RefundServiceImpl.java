package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.RefundCountVo;
import com.zhihaoscm.domain.bean.vo.RefundVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.RefundMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

/**
 * <p>
 * 退款 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@Service
public class RefundServiceImpl
		extends MpStringIdBaseServiceImpl<Refund, RefundMapper>
		implements RefundService {

	@Autowired
	private ProjectService projectService;
	@Autowired
	private PaymentService paymentService;
	@Autowired
	private ContractService contractService;
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	public RefundServiceImpl(RefundMapper repository) {
		super(repository);
	}

	@Override
	public Page<RefundVo> paging(Integer page, Integer size, String param,
			String paymentId, String sellerName, String purchaserName,
			LocalDateTime beginTime, LocalDateTime endTime, List<Integer> state,
			String sortKey, String sortOrder, Boolean hasAll, Long userId) {
		LambdaQueryWrapper<Refund> wrapper = Wrappers.lambdaQuery(Refund.class);
		this.filterDeleted(wrapper);
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (!hasAll) {
			// 处理人是自己在的
			List<String> projectIdList = projectService.findByUserId(userId,
					null);
			if (CollectionUtils.isNotEmpty(projectIdList)) {
				wrapper.in(Refund::getProjectId, projectIdList);
			} else {
				return Page.of(page, size, 0);
			}
		}
		if (StringUtils.isNotBlank(param)) {
			List<String> projectIds = projectService.findByNameLike(param)
					.stream().map(Project::getId).distinct().toList();
			wrapper.and(i -> i.like(Refund::getId, param).or().in(
					CollectionUtils.isNotEmpty(projectIds),
					Refund::getProjectId, projectIds));
		}
		wrapper.like(StringUtils.isNotBlank(paymentId), Refund::getPaymentId,
				paymentId);
		// 退款单位名称
		if (StringUtils.isNotBlank(sellerName)) {
			wrapper.apply(
					"(seller_enterprise -> '$.name' LIKE CONCAT('%',{0},'%'))",
					sellerName);
		}
		// 收款单位名称
		if (StringUtils.isNotBlank(purchaserName)) {
			wrapper.apply(
					"(purchaser_enterprise -> '$.name' LIKE CONCAT('%',{0},'%'))",
					purchaserName);
		}
		wrapper.ge(Objects.nonNull(beginTime), Refund::getRefundDate, beginTime)
				.le(Objects.nonNull(endTime), Refund::getRefundDate, endTime);
		// 状态
		if (CollectionUtils.isEmpty(state)) {
			wrapper.and(
					x -> x.eq(Refund::getInitiator,
							RefundDef.Initiator.ADMIN.getCode())
							.or(y -> y
									.eq(Refund::getInitiator,
											RefundDef.Initiator.CUSTOM
													.getCode())
									.ne(Refund::getState,
											RefundDef.State.REJECTED
													.getCode())));
		} else {
			// 遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer s : state) {

					switch (RefundDef.QueryState.from(s)) {
						// 确认中
						case CONFIRMING -> x.or(y -> y
								.eq(Refund::getInitiator,
										RefundDef.Initiator.ADMIN.getCode())
								.eq(Refund::getState,
										RefundDef.State.PENDING_CONFIRMATION
												.getCode()));
						// 待确认
						case TO_BE_CONFIRMED ->
							x.or(y -> y
									.eq(Refund::getInitiator,
											RefundDef.Initiator.CUSTOM
													.getCode())
									.eq(Refund::getState,
											RefundDef.State.PENDING_CONFIRMATION
													.getCode()));
						// 已驳回
						case REJECTED -> x
								.or(y -> y
										.eq(Refund::getInitiator,
												RefundDef.Initiator.ADMIN
														.getCode())
										.eq(Refund::getState,
												RefundDef.State.REJECTED
														.getCode()));
						// 已完成
						case COMPLETED -> x.or(y -> y.eq(Refund::getState,
								RefundDef.State.COMPLETED.getCode()));
					}
				}
			});
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 列表排序改为更新时间倒序
			wrapper.orderByDesc(Refund::getUpdatedTime)
					.orderByDesc(Refund::getId);
		}
		Page<Refund> paymentPage = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<RefundVo> paymentVos = this.packVo(paymentPage.getRecords());
		return PageUtil.getRecordsInfoPage(paymentPage, paymentVos);
	}

	@Override
	public Page<RefundVo> customPaging(Integer page, Integer size,
			String keyword, String paymentId, String sellerName,
			String purchaserName, LocalDateTime beginTime,
			LocalDateTime endTime, List<Integer> state, String sortKey,
			String sortOrder, Long customId) {
		LambdaQueryWrapper<Refund> wrapper = Wrappers.lambdaQuery(Refund.class);
		this.filterDeleted(wrapper);
		// 收款方是自己，或者退款方是自己
		if (Objects.nonNull(customId)) {
			wrapper.and(i -> i.eq(Refund::getPurchaserId, customId).or()
					.eq(Refund::getSellerId, customId));
		}
		if (StringUtils.isNotBlank(keyword)) {
			List<String> contractIds = contractService.findByNameLike(keyword)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(i -> i.like(Refund::getId, keyword).or().in(
					CollectionUtils.isNotEmpty(contractIds),
					Refund::getContractId, contractIds));
		}
		// 退款单位名称
		if (StringUtils.isNotBlank(sellerName)) {
			wrapper.apply(
					"(seller_enterprise -> '$.name' LIKE CONCAT('%',{0},'%'))",
					sellerName);
		}
		// 收款单位名称
		if (StringUtils.isNotBlank(purchaserName)) {
			wrapper.apply(
					"(purchaser_enterprise -> '$.name' LIKE CONCAT('%',{0},'%'))",
					purchaserName);
		}
		wrapper.like(StringUtils.isNotBlank(paymentId), Refund::getPaymentId,
				paymentId)
				.ge(Objects.nonNull(beginTime), Refund::getPaymentDate,
						beginTime)
				.le(Objects.nonNull(endTime), Refund::getPaymentDate, endTime);

		// 状态
		if (CollectionUtils.isEmpty(state)) {
			// 状态筛选为空：发起人是自己+所有状态 或者 发起人是供应链端+收款方或退款方是自己+不是已驳回状态
			wrapper.and(
					x -> x.eq(Refund::getInitiator,
							RefundDef.Initiator.CUSTOM.getCode())
							.or(y -> y
									.eq(Refund::getInitiator,
											QuotaChangeDef.Initiator.ADMIN
													.getCode())
									.ne(Refund::getState,
											RefundDef.State.REJECTED
													.getCode())));
		} else {
			// 遍历state并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer s : state) {
					switch (RefundDef.QueryState.from(s)) {
						// 确认中
						case CONFIRMING ->
							x.or(y -> y
									.eq(Refund::getInitiator,
											RefundDef.Initiator.CUSTOM
													.getCode())
									.eq(Refund::getState,
											RefundDef.State.PENDING_CONFIRMATION
													.getCode()));
						// 待确认
						case TO_BE_CONFIRMED -> x.or(y -> y
								.eq(Refund::getInitiator,
										RefundDef.Initiator.ADMIN.getCode())
								.eq(Refund::getState,
										RefundDef.State.PENDING_CONFIRMATION
												.getCode()));
						// 已驳回
						case REJECTED ->
							x.or(y -> y
									.eq(Refund::getInitiator,
											RefundDef.Initiator.CUSTOM
													.getCode())
									.eq(Refund::getState,
											RefundDef.State.REJECTED
													.getCode()));
						// 已完成
						case COMPLETED -> x.or(y -> y.eq(Refund::getState,
								RefundDef.State.COMPLETED.getCode()));
					}
				}
			});
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 更新时间倒序
			wrapper.orderByDesc(Refund::getUpdatedTime)
					.orderByDesc(Refund::getId);
		}
		Page<Refund> paymentPage = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<RefundVo> paymentVos = this.packVo(paymentPage.getRecords());
		return PageUtil.getRecordsInfoPage(paymentPage, paymentVos);
	}

	@Override
	public Optional<RefundVo> findVoById(String id) {
		return super.findOne(id).map(refund -> {
			RefundVo vo = new RefundVo();
			vo.setRefund(refund);
			if (Objects.nonNull(refund.getContractId())) {
				vo.setContract(contractService.findOne(refund.getContractId())
						.orElse(null));
			}
			if (Objects.nonNull(refund.getProjectId())) {
				projectService.findOne(refund.getProjectId())
						.ifPresent(vo::setProject);
			}
			return vo;
		});
	}

	@Override
	public List<Refund> findByStates(List<Integer> state) {
		LambdaQueryWrapper<Refund> wrapper = Wrappers.lambdaQuery(Refund.class);
		this.filterDeleted(wrapper);
		wrapper.in(CollectionUtils.isNotEmpty(state), Refund::getState, state);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Refund> findByPaymentIds(List<String> paymentIds,
			Integer state) {
		if (CollectionUtils.isEmpty(paymentIds)) {
			return Collections.emptyList();
		}
		LambdaQueryWrapper<Refund> wrapper = Wrappers.lambdaQuery(Refund.class);
		this.filterDeleted(wrapper);
		wrapper.in(Refund::getPaymentId, paymentIds);
		wrapper.eq(Objects.nonNull(state), Refund::getState, state);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Refund> findUnfinished(String projectId) {
		LambdaQueryWrapper<Refund> queryWrapper = Wrappers
				.lambdaQuery(Refund.class);
		queryWrapper.eq(Refund::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Refund::getProjectId, projectId);
		queryWrapper.ne(Refund::getState, RefundDef.State.COMPLETED.getCode());
		return repository.selectList(queryWrapper);
	}

	@Override
	@FileId
	@Transactional(rollbackFor = Exception.class)
	public Refund create(Refund resource) {
		Project project = projectService.findOne(resource.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService.findOne(resource.getContractId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30151001));
		// 根据规则设置退款id
		resource.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE.apply(redisClient,
				project.getCode(), RedisKeys.Cache.REFUND,
				3 + AutoCodeDef.BusinessRuleCode.RECEIVE_PAYMENT_SUFFIX
						.getCode(),
				4, AutoCodeDef.DATE_TYPE.yy));
		Refund refund = super.create(resource);
		// 如果新增的状态就是退款完成(供应链端新增+采购合同 或
		// 供应链端新增+销售合同+合同下游是录入)，则需要将对应付款单的变更信息增加退款id和退款日期
		if (RefundDef.State.COMPLETED.match(resource.getState())) {
			this.handleRefundComplete(refund);
		}
		// 供应链新增+销售合同+收款单位（下游）是注册企业 才发短信和站内
		if (RefundDef.Initiator.ADMIN.match(resource.getInitiator())
				&& ContractDef.ContractType.SALES
						.match(contract.getContractType())
				&& CommonDef.Symbol.NO.match(project.getCustomerIsRecorded())) {
			this.sendNotice(refund,
					wxSubscriptionProperties.getCreateRefundCode(),
					MessageFormat.format(
							UserMessageConstants.REFUND_UNCONFIRMED_TEMPLATE,
							refund.getId()),
					ContractDef.ContractType.SALES.getCode());
		} else if (RefundDef.Initiator.CUSTOM.match(resource.getInitiator())
				&& ContractDef.ContractType.PURCHASE
						.match(contract.getContractType())
				&& CommonDef.Symbol.NO.match(project.getCustomerIsRecorded())) {
			this.sendNotice(refund,
					wxSubscriptionProperties.getCreateRefundCode(),
					MessageFormat.format(
							UserMessageConstants.REFUND_UNCONFIRMED_TEMPLATE,
							refund.getId()),
					ContractDef.ContractType.PURCHASE.getCode());
		}
		return refund;
	}

	@Override
	@FileId(type = 2)
	public Refund updateAllProperties(Refund resource) {
		return super.updateAllProperties(resource);
	}

	@Override
	@FileId(type = 3)
	public void delete(String id) {
		super.delete(id);
	}

	@Override
	public void reject(Refund refund) {
		refund.setState(RefundDef.State.REJECTED.getCode());
		Project project = projectService.findOne(refund.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService.findOne(refund.getContractId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30151001));
		if (RefundDef.Initiator.ADMIN.match(refund.getInitiator())
				&& ContractDef.ContractType.PURCHASE
						.match(contract.getContractType())
				&& CommonDef.Symbol.NO.match(project.getCustomerIsRecorded())) {
			this.sendNotice(refund,
					wxSubscriptionProperties.getRefundNullifyCode(),
					MessageFormat.format(
							UserMessageConstants.REFUND_DISMISS_TEMPLATE,
							refund.getId()),
					ContractDef.ContractType.PURCHASE.getCode());
		}
		super.updateAllProperties(refund);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void confirm(Refund refund) {
		refund.setState(RefundDef.State.COMPLETED.getCode());
		Project project = projectService.findOne(refund.getProjectId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30152013));
		Contract contract = contractService.findOne(refund.getContractId())
				.orElseThrow(
						() -> new BadRequestException(ErrorCode.CODE_30151001));
		if (RefundDef.Initiator.ADMIN.match(refund.getInitiator())
				&& ContractDef.ContractType.PURCHASE
						.match(contract.getContractType())
				&& CommonDef.Symbol.NO.match(project.getCustomerIsRecorded())) {
			this.sendNotice(refund,
					wxSubscriptionProperties.getRefundConfirmCode(),
					MessageFormat.format(
							UserMessageConstants.REFUND_CONFIRMED_TEMPLATE,
							refund.getId()),
					ContractDef.ContractType.PURCHASE.getCode());
		}
		super.updateAllProperties(refund);
		// 对应收款表的变更信息增加退款id和退款日期
		this.handleRefundComplete(refund);
	}

	@Override
	public Optional<RefundCountVo> staticsAdminRefund(boolean isManage) {
		RefundCountVo refundCountVo = new RefundCountVo();
		refundCountVo.setReject(0L);
		refundCountVo.setWaitConfirm(0L);
		List<String> projectIds = projectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		LambdaQueryWrapper<Refund> queryWrapper = Wrappers
				.lambdaQuery(Refund.class);
		if (isManage) {
			// 统计待确认
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(Refund::getState,
					RefundDef.State.PENDING_CONFIRMATION.getCode());
			queryWrapper.eq(Refund::getInitiator,
					RefundDef.Initiator.CUSTOM.getCode());
			queryWrapper.in(Refund::getProjectId, projectIds);
			refundCountVo.setWaitConfirm(repository.selectCount(queryWrapper));

			// 统计已驳回
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(Refund::getState,
					RefundDef.State.REJECTED.getCode());
			queryWrapper.eq(Refund::getInitiator,
					RefundDef.Initiator.ADMIN.getCode());
			queryWrapper.in(Refund::getProjectId, projectIds);
			refundCountVo.setReject(repository.selectCount(queryWrapper));
		}

		return Optional.of(refundCountVo);
	}

	@Override
	public Optional<RefundCountVo> staticsCustomerRefund(boolean isPermission) {
		RefundCountVo refundCountVo = new RefundCountVo();
		refundCountVo.setReject(0L);
		refundCountVo.setWaitConfirm(0L);
		LambdaQueryWrapper<Refund> queryWrapper = Wrappers
				.lambdaQuery(Refund.class);
		Long customerId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();
		if (isPermission) {
			// 统计待确认
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(Refund::getPurchaserId, customerId);
			queryWrapper.eq(Refund::getInitiator,
					RefundDef.Initiator.ADMIN.getCode());
			queryWrapper.eq(Refund::getState,
					RefundDef.State.PENDING_CONFIRMATION.getCode());
			refundCountVo.setWaitConfirm(repository.selectCount(queryWrapper));

			// 统计已驳回
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(Refund::getPurchaserId, customerId);
			queryWrapper.eq(Refund::getInitiator,
					RefundDef.Initiator.CUSTOM.getCode());
			queryWrapper.eq(Refund::getState,
					RefundDef.State.REJECTED.getCode());
			refundCountVo.setReject(repository.selectCount(queryWrapper));
		}

		return Optional.of(refundCountVo);
	}

	/**
	 * 封装vo
	 *
	 * @param records
	 * @return
	 */
	private List<RefundVo> packVo(List<Refund> records) {
		List<RefundVo> vos = new ArrayList<>();
		// 合同信息
		List<String> contracts = records.stream().map(Refund::getContractId)
				.distinct().toList();
		Map<String, Contract> contractMap = contractService.findByIds(contracts)
				.stream().collect(Collectors.toMap(Contract::getId, e -> e));
		// 项目信息
		List<String> projects = records.stream().map(Refund::getProjectId)
				.distinct().toList();
		Map<String, Project> projectMap = projectService.findByIds(projects)
				.stream().collect(Collectors.toMap(Project::getId, e -> e));
		for (Refund refund : records) {
			RefundVo vo = new RefundVo();
			vo.setRefund(refund);
			vo.setContract(contractMap.get(refund.getContractId()));
			vo.setProject(projectMap.get(refund.getProjectId()));
			vos.add(vo);
		}
		return vos;
	}

	/**
	 * 退款完成时，对应付款/收款表的变更信息增加退款id和退款日期
	 *
	 * @param refund
	 */
	private void handleRefundComplete(Refund refund) {
		paymentService.updateRefund(refund.getPaymentId(), refund.getId(),
				refund.getRefundDate());
		if (PaymentDef.CostType.GOODS_PAYMENT.match(refund.getCostType())) {
			// 合同的预估可提货余额要修改
			Contract contract = contractService.findOne(refund.getContractId())
					.orElse(null);
			BigDecimal refundLoanAmount = BigDecimal.ZERO;
			if (Objects.nonNull(contract)) {
				refundLoanAmount = Objects
						.nonNull(contract.getRefundLoanAmount())
								? contract.getRefundLoanAmount()
								: BigDecimal.ZERO;
			}
			BigDecimal totalRefundLoanAmount = refundLoanAmount
					.add(refund.getAmount());
			contractService.updateRefundLoanAmount(refund.getContractId(),
					totalRefundLoanAmount);
		}
	}

	/**
	 * 发送短信
	 *
	 * @param refund
	 * @param templateCode
	 * @param title
	 */
	private void sendNotice(Refund refund, String templateCode, String title,
			Integer type) {
		Customer customer = null;
		if (ContractDef.ContractType.SALES.match(type)) {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(refund.getPurchaserBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		} else {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(refund.getSellerBusinessId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		}

		if (Objects.nonNull(customer)) {
			if (StringUtils.isNotBlank(templateCode)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(templateCode)
						.params(Map.of("refund_id", refund.getId()))
						.mobile(customer.getMobile()).build());
			}

			if (StringUtils.isNotBlank(title)) {
				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.FUNDING.getCode())
						.title(title)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.REFUND_DETAIL_PAGE)
						.detailId(String.valueOf(refund.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
		}
	}
}
