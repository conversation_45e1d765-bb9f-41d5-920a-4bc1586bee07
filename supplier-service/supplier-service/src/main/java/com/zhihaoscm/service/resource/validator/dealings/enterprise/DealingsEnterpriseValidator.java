package com.zhihaoscm.service.resource.validator.dealings.enterprise;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.dto.DealingsEnterpriseDto;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.meta.biz.DealingsEnterpriseDef;
import com.zhihaoscm.domain.meta.biz.InstitutionApplyDef;
import com.zhihaoscm.domain.meta.biz.SystemDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.client.usercenter.SupplierClient;
import com.zhihaoscm.service.config.properties.SupplierChainProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.service.DealingsEnterpriseService;
import com.zhihaoscm.service.core.service.ProjectService;
import com.zhihaoscm.service.core.service.usercenter.InstitutionApplyService;
import com.zhihaoscm.service.resource.form.dealings.enterprise.AdminDealingsEnterpriseForm;
import com.zhihaoscm.service.resource.form.dealings.enterprise.BankItem;
import com.zhihaoscm.service.resource.form.dealings.enterprise.CustomDealingsEnterpriseForm;
import com.zhihaoscm.service.resource.validator.customer.CustomerValidator;

@Component
public class DealingsEnterpriseValidator {

	@Autowired
	private DealingsEnterpriseService service;

	@Autowired
	private SupplierClient supplierClient;

	@Autowired
	private InstitutionApplyService institutionApplyService;

	@Autowired
	private CustomerValidator customerValidator;

	@Autowired
	private ProjectService projectService;

	@Autowired
	private SupplierChainProperties supplierChainProperties;

	/**
	 * 校验是否存在
	 * 
	 * @param id
	 * @return
	 */
	public DealingsEnterprise validateExist(Long id) {
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30180001));
	}

	/**
	 * 校验新增-供应链提交
	 *
	 * @param form
	 * @return
	 */
	public DealingsEnterpriseDto validateCreate(
			AdminDealingsEnterpriseForm form) {
		// sass 校验当前供应链是否完成认证 独立部署则无需校验
		if (SystemDef.SystemType.SAAS
				.match(supplierChainProperties.getType())) {
			Long tenantId = Objects.requireNonNull(UserContextHolder.getUser())
					.getTenantId();
			Supplier supplier = supplierClient.findByTenantId(tenantId)
					.orElseThrow(() -> new BadRequestException(
							ErrorCode.CODE_30180011));
			if (!CommonDef.Symbol.YES.match(supplier.getApplyState())) {
				throw new BadRequestException(ErrorCode.CODE_30180029);
			}
		}

		switch (DealingsEnterpriseDef.EnterpriseType
				.from(form.getEnterpriseType())) {
			// 注册企业
			case REGISTERED_BUSINESS -> {
				Long customerId = form.getCustomerId();
				if (Objects.isNull(customerId)) {
					throw new BadRequestException(ErrorCode.CODE_30180010);
				}
				Customer customer = customerValidator.validateExist(customerId);
				if (!CommonDef.Symbol.YES.match(customer.getApplyState())) {
					throw new BadRequestException(ErrorCode.CODE_30180012);
				}

				InstitutionApply institutionApply = institutionApplyService
						.findByCustomerIdAndStateCancel(customer.getId(),
								InstitutionApplyDef.State.APPROVED.getCode(),
								CommonDef.Symbol.NO.getCode(),
								InstitutionApplyDef.Source.PURCHASE_CUSTOMER
										.getCode())
						.orElse(null);
				if (Objects.isNull(institutionApply)) {
					throw new BadRequestException(ErrorCode.CODE_30180012);
				}
				String unifiedSocialCreditCode = institutionApply
						.getUnifiedSocialCreditCode();
				DealingsEnterprise exist = service
						.findByUnifiedSocialCreditCode(unifiedSocialCreditCode,
								form.getEnterpriseType(),
								Objects.requireNonNull(
										UserContextHolder.getUser())
										.getTenantId())
						.orElse(null);
				if (Objects.nonNull(exist)) {
					throw new BadRequestException(ErrorCode.CODE_30180014);
				}
				// 填充数据
				DealingsEnterprise dealingsEnterprise = this.packEntity(
						institutionApply, unifiedSocialCreditCode,
						DealingsEnterpriseDef.Initiator.SUPPLIER_CHAIN
								.getCode());
				return form.convertToDto(dealingsEnterprise);

			}
			// 录入企业
			case ENTER_THE_ENTERPRISE -> {
				if (StringUtils.isBlank(form.getInstitutionName())) {
					throw new BadRequestException(ErrorCode.CODE_30180015);
				}
				if (StringUtils.isBlank(form.getUnifiedSocialCreditCode())) {
					throw new BadRequestException(ErrorCode.CODE_30180016);
				}
				if (StringUtils.isBlank(form.getLegalRepresentative())) {
					throw new BadRequestException(ErrorCode.CODE_30180017);
				}
				String unifiedSocialCreditCode = form
						.getUnifiedSocialCreditCode();
				DealingsEnterprise exist = service
						.findByUnifiedSocialCreditCode(unifiedSocialCreditCode,
								form.getEnterpriseType(),
								Objects.requireNonNull(
										UserContextHolder.getUser())
										.getTenantId())
						.orElse(null);

				if (Objects.nonNull(exist)) {
					throw new BadRequestException(ErrorCode.CODE_30180013);
				}
				this.validateBankInfo(form.getBanks());

				DealingsEnterprise dealingsEnterprise = new DealingsEnterprise();
				this.handleSupplierInfo(dealingsEnterprise);
				dealingsEnterprise.setState(
						DealingsEnterpriseDef.State.COOPERATION.getCode());
				return form.convertToDto(dealingsEnterprise);
			}
		}
		throw new BadRequestException(ErrorCode.CODE_30180018);
	}

	/**
	 * 校验新增-客户端提交
	 *
	 * @param form
	 * @return
	 */
	public DealingsEnterpriseDto validateCreate(
			CustomDealingsEnterpriseForm form) {
		Customer proxyAccount = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount();
		if (!CommonDef.Symbol.YES.match(proxyAccount.getApplyState())) {
			throw new BadRequestException(ErrorCode.CODE_30180029);
		}
		// 客户端提交
		Long supplierChainId = form.getSupplierChainId();
		if (Objects.isNull(supplierChainId)) {
			throw new BadRequestException(ErrorCode.CODE_30180010);
		}
		// 设置供应链信息
		Supplier supplier = supplierClient.findByTenantId(supplierChainId)
				.orElse(null);
		if (Objects.isNull(supplier)) {
			throw new BadRequestException(ErrorCode.CODE_30180011);
		}
		if (!CommonDef.Symbol.YES.match(supplier.getApplyState())) {
			throw new BadRequestException(ErrorCode.CODE_30180012);
		}
		InstitutionApply institutionApply = institutionApplyService
				.findByCustomerIdAndStateCancel(supplier.getId(),
						InstitutionApplyDef.State.APPROVED.getCode(),
						CommonDef.Symbol.NO.getCode(),
						InstitutionApplyDef.Source.WAREHOUSE_CUSTOMER.getCode())
				.orElse(null);
		if (Objects.isNull(institutionApply)) {
			throw new BadRequestException(ErrorCode.CODE_30180012);
		}
		String unifiedSocialCreditCode = institutionApply
				.getUnifiedSocialCreditCode();
		// 判断当前用户有没有加个这个供应链
		DealingsEnterprise exist = service
				.findBySupplierChainUnifiedSocialCreditCode(
						unifiedSocialCreditCode,
						DealingsEnterpriseDef.EnterpriseType.REGISTERED_BUSINESS
								.getCode(),
						CustomerContextHolder.getCustomerLoginVo()
								.getProxyAccount().getId())
				.orElse(null);
		if (Objects.nonNull(exist)) {
			throw new BadRequestException(ErrorCode.CODE_30180013);
		}

		DealingsEnterprise dealingsEnterprise = this.packEntity(
				institutionApply, unifiedSocialCreditCode,
				DealingsEnterpriseDef.Initiator.CUSTOM.getCode());
		return form.convertToDto(dealingsEnterprise);
	}

	/**
	 * 校验修改-供应链发起
	 *
	 * @param id
	 * @param form
	 * @return
	 */
	public DealingsEnterpriseDto validateUpdate(Long id,
			AdminDealingsEnterpriseForm form, Integer type) {
		DealingsEnterprise dealingsEnterprise = this.validateExist(id);
		this.validateAuth(type, dealingsEnterprise);
		if (!dealingsEnterprise.getInitiator().equals(type)) {
			throw new BadRequestException(ErrorCode.CODE_30180018);
		}
		switch (DealingsEnterpriseDef.EnterpriseType
				.from(form.getEnterpriseType())) {
			// 注册企业
			case REGISTERED_BUSINESS -> {
				Long accountId = form.getCustomerId();
				if (Objects.isNull(accountId)) {
					throw new BadRequestException(ErrorCode.CODE_30180010);
				}
				Customer customer = customerValidator.validateExist(accountId);
				if (!CommonDef.Symbol.YES.match(customer.getApplyState())) {
					throw new BadRequestException(ErrorCode.CODE_30180012);
				}

				InstitutionApply institutionApply = institutionApplyService
						.findByCustomerIdAndStateCancel(customer.getId(),
								InstitutionApplyDef.State.APPROVED.getCode(),
								CommonDef.Symbol.NO.getCode(),
								InstitutionApplyDef.Source.PURCHASE_CUSTOMER
										.getCode())
						.orElse(null);
				if (Objects.isNull(institutionApply)) {
					throw new BadRequestException(ErrorCode.CODE_30180012);
				}
				String unifiedSocialCreditCode = institutionApply
						.getUnifiedSocialCreditCode();
				DealingsEnterprise exist = service
						.findByUnifiedSocialCreditCode(unifiedSocialCreditCode,
								form.getEnterpriseType(),
								Objects.requireNonNull(
										UserContextHolder.getUser())
										.getTenantId())
						.orElse(null);
				if (Objects.nonNull(exist) && !exist.getId().equals(id)) {
					throw new BadRequestException(ErrorCode.CODE_30180014);
				}

				dealingsEnterprise.setInstitutionName(
						institutionApply.getInstitutionName());
				dealingsEnterprise.setLegalRepresentative(
						institutionApply.getLegalRepresentative());
				dealingsEnterprise
						.setUnifiedSocialCreditCode(unifiedSocialCreditCode);
				// 客户端默认注册企业
				dealingsEnterprise.setEnterpriseType(
						DealingsEnterpriseDef.EnterpriseType.REGISTERED_BUSINESS
								.getCode());
				// 状态默认待确认
				dealingsEnterprise.setState(
						DealingsEnterpriseDef.State.TO_BE_CONFIRMED.getCode());
				return form.convertToDto(dealingsEnterprise);

			}
			// 录入企业
			case ENTER_THE_ENTERPRISE -> {
				if (StringUtils.isBlank(form.getInstitutionName())) {
					throw new BadRequestException(ErrorCode.CODE_30180015);
				}
				if (StringUtils.isBlank(form.getUnifiedSocialCreditCode())) {
					throw new BadRequestException(ErrorCode.CODE_30180016);
				}
				if (StringUtils.isBlank(form.getLegalRepresentative())) {
					throw new BadRequestException(ErrorCode.CODE_30180017);
				}
				String unifiedSocialCreditCode = form
						.getUnifiedSocialCreditCode();
				DealingsEnterprise exist = service
						.findByUnifiedSocialCreditCode(unifiedSocialCreditCode,
								form.getEnterpriseType(),
								Objects.requireNonNull(
										UserContextHolder.getUser())
										.getTenantId())
						.orElse(null);
				if (Objects.nonNull(exist) && !exist.getId().equals(id)) {
					throw new BadRequestException(ErrorCode.CODE_30180013);
				}
				this.validateBankInfo(form.getBanks());
				return form.convertToDto(dealingsEnterprise);
			}
		}
		throw new BadRequestException(ErrorCode.CODE_30180005);
	}

	/**
	 * 校验银行账号
	 * 
	 * @param banks
	 */
	private void validateBankInfo(List<BankItem> banks) {
		if (CollectionUtils.isNotEmpty(banks)) {
			if (banks.size() > 10) {
				throw new BadRequestException(ErrorCode.CODE_30150014);
			}
			if (banks.stream().map(BankItem::getIsDefault)
					.filter(isDefault -> isDefault
							.equals(CommonDef.Symbol.YES.getCode()))
					.count() > 1) {
				throw new BadRequestException(ErrorCode.CODE_30150015);
			}
		}
	}

	/**
	 * 校验删除 录入企业随便删除 注册企业只有已驳回才能删除
	 * 
	 * @param id
	 */
	public void validateDelete(Long id, Integer type) {
		DealingsEnterprise dealingsEnterprise = this.validateExist(id);
		this.validateAuth(type, dealingsEnterprise);
		if (!dealingsEnterprise.getInitiator().equals(type)) {
			throw new BadRequestException(ErrorCode.CODE_30180018);
		}
		// 校验是否被项目关联
		List<Project> projects = projectService
				.findByCustomerIdOrSupplierId(id);
		if (CollectionUtils.isNotEmpty(projects)) {
			throw new BadRequestException(ErrorCode.CODE_30180028);
		}

		if (DealingsEnterpriseDef.EnterpriseType.REGISTERED_BUSINESS
				.match(dealingsEnterprise.getEnterpriseType())) {
			if (!DealingsEnterpriseDef.State.REJECTED
					.match(dealingsEnterprise.getState())) {
				throw new BadRequestException(ErrorCode.CODE_30180019);
			}
		}
	}

	/**
	 * 校验确认 待确认状态且类型和端不一致才能进行确认
	 * 
	 * @param id
	 * @param type
	 */
	public DealingsEnterprise validateConfirm(Long id, Integer type) {
		DealingsEnterprise dealingsEnterprise = this.validateExist(id);
		this.validateAuth(type, dealingsEnterprise);
		if (!DealingsEnterpriseDef.EnterpriseType.REGISTERED_BUSINESS
				.match(dealingsEnterprise.getEnterpriseType())) {
			throw new BadRequestException(ErrorCode.CODE_30180018);
		}
		if (dealingsEnterprise.getInitiator().equals(type)) {
			throw new BadRequestException(ErrorCode.CODE_30180018);
		}
		if (!DealingsEnterpriseDef.State.TO_BE_CONFIRMED
				.match(dealingsEnterprise.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30180020);
		}
		return dealingsEnterprise;
	}

	/**
	 * 校验驳回
	 * 
	 * @param id
	 * @param type
	 */
	public DealingsEnterprise validateReject(Long id, Integer type) {
		DealingsEnterprise dealingsEnterprise = this.validateExist(id);

		this.validateAuth(type, dealingsEnterprise);
		if (!DealingsEnterpriseDef.EnterpriseType.REGISTERED_BUSINESS
				.match(dealingsEnterprise.getEnterpriseType())) {
			throw new BadRequestException(ErrorCode.CODE_30180018);
		}
		if (dealingsEnterprise.getInitiator().equals(type)) {
			throw new BadRequestException(ErrorCode.CODE_30180018);
		}
		if (!DealingsEnterpriseDef.State.TO_BE_CONFIRMED
				.match(dealingsEnterprise.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30180021);
		}
		return dealingsEnterprise;
	}

	/**
	 * 组装实体
	 *
	 * @param institutionApply
	 * @param unifiedSocialCreditCode
	 * @return
	 */
	private DealingsEnterprise packEntity(InstitutionApply institutionApply,
			String unifiedSocialCreditCode, Integer initiator) {
		DealingsEnterprise dealingsEnterprise = new DealingsEnterprise();
		switch (DealingsEnterpriseDef.Initiator.from(initiator)) {
			case SUPPLIER_CHAIN -> {
				dealingsEnterprise.setInstitutionName(
						institutionApply.getInstitutionName());
				dealingsEnterprise.setLegalRepresentative(
						institutionApply.getLegalRepresentative());
				dealingsEnterprise
						.setUnifiedSocialCreditCode(unifiedSocialCreditCode);
				// 状态默认待确认
				dealingsEnterprise.setState(
						DealingsEnterpriseDef.State.TO_BE_CONFIRMED.getCode());

				this.handleSupplierInfo(dealingsEnterprise);

			}
			case CUSTOM -> {
				dealingsEnterprise.setSupplierChainInstitutionName(
						institutionApply.getInstitutionName());
				dealingsEnterprise.setSupplierChainLegalRepresentative(
						institutionApply.getLegalRepresentative());
				dealingsEnterprise.setSupplierChainUnifiedSocialCreditCode(
						unifiedSocialCreditCode);
				// 客户端默认注册企业
				dealingsEnterprise.setEnterpriseType(
						DealingsEnterpriseDef.EnterpriseType.REGISTERED_BUSINESS
								.getCode());
				// 状态默认待确认
				dealingsEnterprise.setState(
						DealingsEnterpriseDef.State.TO_BE_CONFIRMED.getCode());

				// 设置当前当前用户信息
				Customer proxyAccount = CustomerContextHolder
						.getCustomerLoginVo().getProxyAccount();

				InstitutionApply customInstitutionApply = institutionApplyService
						.findByCustomerIdAndStateCancel(proxyAccount.getId(),
								InstitutionApplyDef.State.APPROVED.getCode(),
								CommonDef.Symbol.NO.getCode(),
								InstitutionApplyDef.Source.PURCHASE_CUSTOMER
										.getCode())
						.orElse(new InstitutionApply());
				dealingsEnterprise.setCustomerId(proxyAccount.getId());
				dealingsEnterprise.setInstitutionName(
						customInstitutionApply.getInstitutionName());
				dealingsEnterprise.setLegalRepresentative(
						customInstitutionApply.getLegalRepresentative());
				dealingsEnterprise.setUnifiedSocialCreditCode(
						customInstitutionApply.getUnifiedSocialCreditCode());
			}
		}

		return dealingsEnterprise;
	}

	/**
	 * 处理供应链信息
	 * 
	 * @param dealingsEnterprise
	 */
	private void handleSupplierInfo(DealingsEnterprise dealingsEnterprise) {
		switch (SystemDef.SystemType.from(supplierChainProperties.getType())) {
			case SAAS -> {
				// 设置供应链信息
				Long tenantId = Objects
						.requireNonNull(UserContextHolder.getUser())
						.getTenantId();
				InstitutionApply supplierInstitutionApply = institutionApplyService
						.findByCustomerIdAndStateCancel(tenantId,
								InstitutionApplyDef.State.APPROVED.getCode(),
								CommonDef.Symbol.NO.getCode(),
								InstitutionApplyDef.Source.WAREHOUSE_CUSTOMER
										.getCode())
						.orElse(new InstitutionApply());

				dealingsEnterprise.setSupplierChainInstitutionName(
						supplierInstitutionApply.getInstitutionName());
				dealingsEnterprise.setSupplierChainId(tenantId);
				dealingsEnterprise.setSupplierChainLegalRepresentative(
						supplierInstitutionApply.getLegalRepresentative());
				dealingsEnterprise.setSupplierChainUnifiedSocialCreditCode(
						supplierInstitutionApply.getUnifiedSocialCreditCode());
			}
			case INDEPENDENCE_DEPLOYMENT -> {
				// 设置供应链信息
				dealingsEnterprise.setSupplierChainInstitutionName(
						supplierChainProperties.getInstitutionName());
				dealingsEnterprise
						.setSupplierChainId(supplierChainProperties.getId());
				dealingsEnterprise.setSupplierChainLegalRepresentative(
						supplierChainProperties.getLegalRepresentative());
				dealingsEnterprise.setSupplierChainUnifiedSocialCreditCode(
						supplierChainProperties.getUnifiedSocialCreditCode());
			}
		}
	}

	/**
	 * 校验是否有权限做这些操作
	 *
	 * @param initiator
	 * @param dealingsEnterprise
	 */
	private void validateAuth(Integer initiator,
			DealingsEnterprise dealingsEnterprise) {
		if (DealingsEnterpriseDef.Initiator.CUSTOM.match(initiator)) {
			if (!Objects.equals(CustomerContextHolder.getCustomerLoginVo()
					.getProxyAccount().getId(),
					dealingsEnterprise.getCustomerId())) {
				throw new BadRequestException(ErrorCode.CODE_30180018);
			}
		} else {
			if (!Objects.equals(
					Objects.requireNonNull(UserContextHolder.getUser())
							.getTenantId(),
					dealingsEnterprise.getSupplierChainId())) {
				throw new BadRequestException(ErrorCode.CODE_30180018);
			}
		}
	}
}
