package com.zhihaoscm.service.resource.form.reconciliation;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.zhihaoscm.domain.bean.vo.DeliverGoodsVo;
import org.hibernate.validator.constraints.Length;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.Contract;
import com.zhihaoscm.domain.bean.entity.DeliverGoods;
import com.zhihaoscm.domain.bean.entity.Project;
import com.zhihaoscm.domain.bean.entity.Reconciliation;
import com.zhihaoscm.domain.meta.biz.ReconciliationDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(name = "ReconciliationBuyForm", title = "对账单表单对象")
public class ReconciliationBuyForm {

	@NotNull(message = ErrorCode.CODE_30154001)
	@Schema(title = "项目id")
	private String projectId;

	@NotNull(message = ErrorCode.CODE_30154002)
	@Schema(title = "关联合同id")
	private String contractId;

	@Schema(title = "账期")
	private Integer settlePeriod;

	@Schema(title = "预结算方式")
	private Integer preSettleWay;

	@Schema(title = "预付比例/预付金额")
	private BigDecimal prePaymentValue;

	@Schema(title = "结算预付款")
	private BigDecimal settlementPayment;

	@Schema(title = "签署方式 1线上 2.线下")
	private Integer signType;

	@Schema(title = "预对账签署方式 1线上 2.线下")
	private Integer preSignType;

	@Schema(description = "提货列表")
	List<DeliverGoods> deliverGoodsList;

	@Schema(description = "提货列表")
	List<DeliverGoodsVo> deliverGoodsVoList;

	@Schema(title = "对账金额")
	private BigDecimal reconciliationAmount;

	@Schema(description = "对账数量/重量")
	private BigDecimal reconciliationWeight;

	@Schema(title = "订单保证金")
	private BigDecimal orderDeposit;

	@Schema(title = "保证金是否转货款：0.不转 1.转")
	private Integer depositTransferAmount;

	@Schema(title = "实际应付/应收金额")
	private BigDecimal actualAmount;

	@Schema(title = "对账日期")
	private LocalDateTime reconciliationDate;

	@Schema(title = "对账单据文件id")
	private Long reconciliationFileId;

	@Schema(title = "备注")
	@Length(max = 200, message = ErrorCode.CODE_30154007)
	private String remark;

	@Schema(description = "保存类型")
	private Integer saveType;

	@Schema(title = "是否存在预对账：0.否 1.是")
	private Integer isPreReconciliation;

	@Schema(description = "预对账金额")
	private BigDecimal preReconciliationAmount;

	@Schema(description = "预对账重量/数量")
	private BigDecimal preReconciliationWeight;

	@Schema(title = "预对账日期")
	private LocalDateTime preReconciliationDate;

	@Schema(title = "预对账单据文件id")
	private Long preReconciliationFileId;

	public Reconciliation convertEntity(Project project, Contract contract,
			Integer initiator) {
		Reconciliation reconciliation = new Reconciliation();
		reconciliation.setProjectId(projectId);
		reconciliation.setProjectName(project.getName());
		reconciliation.setGoodsName(project.getGoodsName());
		// 采购对账
		reconciliation.setType(ReconciliationDef.Type.BUY.getCode());
		// 采购方是供应链
		reconciliation.setPurchaserId(contract.getSupplierChainId());
		reconciliation
				.setPurchaserEnterprise(contract.getSupplierChainEnterprise());
		// 销售方是上游供应商
		reconciliation.setSellerBusinessId(contract.getUpstreamId());
		reconciliation.setSellerId(contract.getUpstreamSuppliersId());
		reconciliation
				.setSellerEnterprise(contract.getUpstreamSuppliersEnterprise());
		reconciliation.setContractId(this.contractId);
		reconciliation.setContractName(contract.getName());
		reconciliation.setReconciliationWeight(this.reconciliationWeight);
		reconciliation.setReconciliationDate(this.reconciliationDate);
		reconciliation.setReconciliationAmount(this.reconciliationAmount);
		reconciliation.setReconciliationFileId(this.reconciliationFileId);
		reconciliation.setRemark(this.remark);
		reconciliation.setUnbilledAmount(this.reconciliationAmount);
		// 设置发起方
		reconciliation.setInitiator(initiator);
		reconciliation.setSettlePeriod(this.settlePeriod);
		reconciliation.setPreSettleWay(this.preSettleWay);
		reconciliation.setPrePaymentValue(this.prePaymentValue);
		reconciliation.setSettlementPayment(this.settlementPayment);
		// 项目的上游为自录数据时 状态为对账完成
		if (CommonDef.Symbol.YES.match(project.getSupplierIsRecorded())) {
			// 设置对账完成
			reconciliation.setState(ReconciliationDef.State.FINISHED.getCode());
			// 是否自录设置为是
			reconciliation.setIsRecord(CommonDef.Symbol.YES.getCode());
			// 开票状态为未开票
			reconciliation.setInvoiceState(
					ReconciliationDef.InvoiceState.NOT_INVOICED.getCode());
		} else {
			if (ReconciliationDef.SaveType.SAVE_TO_DRAFT.match(saveType)) {
				// 设置草稿状态
				reconciliation
						.setState(ReconciliationDef.State.DRAFT.getCode());
			} else if (ReconciliationDef.SaveType.SAVE_AND_COMMIT
					.match(saveType)) {
				// 设置确认中状态
				reconciliation
						.setState(ReconciliationDef.State.CONFIRMING.getCode());
			}
			// 是否自录设置为否
			reconciliation.setIsRecord(CommonDef.Symbol.NO.getCode());
		}
		reconciliation.setSignType(this.signType);
		reconciliation.setPreSignType(this.preSignType);
		reconciliation.setIsPreReconciliation(this.isPreReconciliation);
		reconciliation.setPreReconciliationDate(this.preReconciliationDate);
		reconciliation.setPreReconciliationAmount(this.preReconciliationAmount);
		reconciliation.setPreReconciliationWeight(this.preReconciliationWeight);
		reconciliation.setPreReconciliationFileId(this.preReconciliationFileId);
		reconciliation.setOrderDeposit(this.orderDeposit);
		reconciliation.setDepositTransferAmount(this.depositTransferAmount);
		reconciliation.setActualAmount(this.actualAmount);
		// 是预对账时 设置预对账信息
		if (CommonDef.Symbol.YES.match(this.isPreReconciliation)) {
			reconciliation
					.setIsConductReconciliation(CommonDef.Symbol.NO.getCode());
			// 设置预对账发起方
			reconciliation.setPreInitiator(initiator);
		} else {
			// 是对账时 设置对账信息
			reconciliation
					.setIsConductReconciliation(CommonDef.Symbol.YES.getCode());
			// 设置对账发起方
			reconciliation.setInitiator(initiator);
		}
		return reconciliation;
	}
}
