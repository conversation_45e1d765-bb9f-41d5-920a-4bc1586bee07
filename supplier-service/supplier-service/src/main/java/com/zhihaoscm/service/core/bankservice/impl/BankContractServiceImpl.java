package com.zhihaoscm.service.core.bankservice.impl;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.PledgeInfo;
import com.zhihaoscm.domain.bean.vo.ContractGoodsInfoVo;
import com.zhihaoscm.domain.bean.vo.ContractVo;
import com.zhihaoscm.domain.meta.ProjectInceptionDef;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.service.config.LocalDateTimeAdapter;
import com.zhihaoscm.service.core.bankservice.*;
import com.zhihaoscm.service.core.mapper.ContractMapper;
import com.zhihaoscm.service.core.service.*;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 合同 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Slf4j
@Service
public class BankContractServiceImpl
		extends MpStringIdBaseServiceImpl<Contract, ContractMapper>
		implements BankContractService {

	@Autowired
	private BankProjectService projectService;
	@Autowired
	private BankPaymentService paymentService;
	@Autowired
	private BankOrderService orderService;
	@Autowired
	private BankSignReceiptService signReceiptService;
	@Autowired
	private BankReconciliationService reconciliationService;
	@Autowired
	private FinancingService financingService;
	@Autowired
	private PledgeService pledgeService;
	@Autowired
	private StorageInceptionInboundDetailService storageInceptionInboundDetailService;
	@Autowired
	private StorageInceptionOutboundDetailService storageInceptionOutboundDetailService;
	@Autowired
	private GoodsService goodsService;
	@Autowired
	private InboundService inboundService;
	@Autowired
	private OutboundService outboundService;
	@Autowired
	private RepaymentService repaymentService;
	@Autowired
	private ProjectInceptionDetailService projectInceptionDetailService;

	public BankContractServiceImpl(ContractMapper repository) {
		super(repository);
	}

	@Override
	public Page<ContractVo> paging(Integer page, Integer size, String keyword,
			List<Integer> contractType, Integer signMode,
			LocalDateTime beginTime, LocalDateTime endTime, String sortKey,
			String sortOrder, Long customId, String projectName,
			String projectId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);

		if (Objects.isNull(projectId)) {
			// 查询供应链新增融资关联的项目
			List<Financing> all = financingService.findAll();
			if (CollectionUtils.isEmpty(all)) {
				return Page.of(page, size, 0);
			}
			List<String> collect = all.stream().map(Financing::getProjectId)
					.toList();

			wrapper.in(CollectionUtils.isNotEmpty(collect),
					Contract::getProjectId, collect);
		} else {
			wrapper.eq(Contract::getProjectId, projectId);
		}

		wrapper.and(StringUtils.isNotBlank(keyword),
				w -> w.like(Contract::getId, keyword).or()
						.like(Contract::getName, keyword));
		wrapper.in(CollectionUtils.isNotEmpty(contractType),
				Contract::getContractType, contractType)
				.eq(Objects.nonNull(signMode), Contract::getSignMode, signMode)
				.ge(Objects.nonNull(beginTime), Contract::getSignDate,
						beginTime)
				.le(Objects.nonNull(endTime), Contract::getSignDate, endTime)
				.eq(Objects.nonNull(customId),
						Contract::getDownstreamPurchasersId, customId);
		if (StringUtils.isNotBlank(projectName)) {
			List<String> projectIds = projectService.findByNameLike(projectName)
					.stream().map(Project::getId).distinct().toList();
			if (CollectionUtils.isEmpty(projectIds)) {
				return Page.of(page, size, 0);
			}
			wrapper.in(CollectionUtils.isNotEmpty(projectIds),
					Contract::getProjectId, projectIds);
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 更新日期倒序排序
			wrapper.orderByDesc(Contract::getUpdatedTime)
					.orderByDesc(Contract::getId);
		}
		Page<Contract> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<ContractVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<ContractVo> customPaging(Integer page, Integer size,
			String keyword, List<Integer> contractType, Integer signMode,
			LocalDateTime beginTime, LocalDateTime endTime,
			List<Integer> states, String sortKey, String sortOrder,
			Long customId, String projectName, String projectId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.ne(Contract::getState, ContractDef.State.COMPLETED.getCode());
		wrapper.and(StringUtils.isNotBlank(keyword),
				w -> w.like(Contract::getId, keyword).or()
						.like(Contract::getName, keyword));
		wrapper.in(CollectionUtils.isNotEmpty(contractType),
				Contract::getContractType, contractType)
				.eq(Objects.nonNull(signMode), Contract::getSignMode, signMode)
				.ge(Objects.nonNull(beginTime), Contract::getSignDate,
						beginTime)
				.le(Objects.nonNull(endTime), Contract::getSignDate, endTime);
		if (CollectionUtils.isEmpty(states)) {
			// 状态筛选是空的，则全部状态:待签署、待确认、签署中
			List<Integer> defaultState = List.of(
					ContractDef.State.SIGNING.getCode(),
					ContractDef.State.PENDING_CONFIRMATION.getCode());
			wrapper.in(Contract::getState, defaultState);
		} else {
			// 买方未签署
			List<Integer> purchaserUnsigned = List.of(
					BusinessContractDef.CommonSignState.UNSIGNED.getCode(),
					BusinessContractDef.CommonSignState.SUPPLY_SIGNED
							.getCode());
			// 如果states不为空，遍历states并根据每个状态构建查询条件
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					// 签署中，买方已签署
					if (ContractDef.QueryState.SIGNING.match(state)) {
						x.or(y -> y
								.eq(Contract::getState,
										ContractDef.State.SIGNING.getCode())
								.eq(Contract::getSignStatus,
										BusinessContractDef.CommonSignState.BUYER_SIGNED
												.getCode()));

					}
					// 待确认
					else if (ContractDef.QueryState.PENDING_CONFIRMATION
							.match(state)) {
						x.or(y -> y.eq(Contract::getState,
								ContractDef.State.PENDING_CONFIRMATION
										.getCode()));
					}
					// 待签署，买方未签署
					else if (ContractDef.QueryState.TO_BE_SIGNED.match(state)) {
						x.or(y -> y
								.eq(Contract::getState,
										ContractDef.State.SIGNING.getCode())
								.in(Contract::getSignStatus,
										purchaserUnsigned));
					}
				}
			});
		}
		wrapper.eq(Objects.nonNull(customId),
				Contract::getDownstreamPurchasersId, customId)
				.eq(Objects.nonNull(projectId), Contract::getProjectId,
						projectId);
		if (StringUtils.isNotBlank(projectName)) {
			List<String> projectIds = projectService.findByNameLike(projectName)
					.stream().map(Project::getId).distinct().toList();
			if (CollectionUtils.isEmpty(projectIds)) {
				return Page.of(page, size, 0);
			}
			wrapper.in(CollectionUtils.isNotEmpty(projectIds),
					Contract::getProjectId, projectIds);
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 更新日期倒序排序
			wrapper.orderByDesc(Contract::getUpdatedTime)
					.orderByDesc(Contract::getId);
		}
		Page<Contract> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<ContractVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<ContractVo> pagingDone(Integer page, Integer size,
			String keyword, List<Integer> contractType, Integer signMode,
			LocalDateTime beginTime, LocalDateTime endTime, String sortKey,
			String sortOrder, Long customId, String projectName,
			String projectId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);

		if (Objects.isNull(projectId)) {
			// 查询供应链新增融资关联的项目
			List<Financing> all = financingService.findAll();
			if (CollectionUtils.isEmpty(all)) {
				return Page.of(page, size, 0);
			}
			List<String> collect = all.stream().map(Financing::getProjectId)
					.toList();

			wrapper.in(CollectionUtils.isNotEmpty(collect),
					Contract::getProjectId, collect);
		} else {
			wrapper.eq(Contract::getProjectId, projectId);
		}

		wrapper.eq(Contract::getState, ContractDef.State.COMPLETED.getCode());
		wrapper.and(StringUtils.isNotBlank(keyword),
				w -> w.like(Contract::getId, keyword).or()
						.like(Contract::getName, keyword));
		wrapper.in(CollectionUtils.isNotEmpty(contractType),
				Contract::getContractType, contractType)
				.eq(Objects.nonNull(signMode), Contract::getSignMode, signMode)
				.ge(Objects.nonNull(beginTime), Contract::getSignDate,
						beginTime)
				.le(Objects.nonNull(endTime), Contract::getSignDate, endTime)
				.eq(Objects.nonNull(customId),
						Contract::getDownstreamPurchasersId, customId);
		if (StringUtils.isNotBlank(projectName)) {
			List<String> projectIds = projectService.findByNameLike(projectName)
					.stream().map(Project::getId).distinct().toList();
			if (CollectionUtils.isEmpty(projectIds)) {
				return Page.of(page, size, 0);
			}
			wrapper.in(CollectionUtils.isNotEmpty(projectIds),
					Contract::getProjectId, projectIds);
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 更新日期倒序排序
			wrapper.orderByDesc(Contract::getUpdatedTime)
					.orderByDesc(Contract::getId);
		}
		Page<Contract> paging = repository.selectPage(new Page<>(page, size),
				wrapper);
		List<ContractVo> vos = this.packPageVos(paging.getRecords());
		return PageUtil.getRecordsInfoPage(paging, vos);
	}

	@Override
	public Page<Contract> selector(Integer page, Integer size, String projectId,
			String name, Integer contractType, Long customId, Integer origin,
			String neId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		if (CommonDef.AccountSource.INNER.match(origin)) {
			if (Objects.isNull(projectId)) {
				return new Page<>();
			}
			wrapper.eq(Contract::getProjectId, projectId)
					.ne(StringUtils.isNotBlank(neId), Contract::getId, neId);
			if (Objects.nonNull(contractType)) {
				wrapper.eq(Contract::getContractType, contractType);
			}
		} else {
			// 客户端下拉
			if (Objects.isNull(customId)) {
				return new Page<>();
			}
			// 关联采购方为自己的采购合同
			wrapper.eq(Contract::getDownstreamPurchasersId, customId).eq(
					Contract::getContractType,
					ContractDef.ContractType.SALES.getCode());
			// 下游采购商id+state
			List<String> projectIds = projectService
					.findByCustomerIdAndState(customId,
							ProjectDef.State.PROCESSING.getCode())
					.stream().map(Project::getId).toList();
			wrapper.in(CollectionUtils.isNotEmpty(projectIds),
					Contract::getProjectId, projectIds);
		}
		wrapper.like(StringUtils.isNotBlank(name), Contract::getName, name)
				.eq(Contract::getState, ContractDef.State.COMPLETED.getCode())
				.orderByDesc(Contract::getUpdatedTime);
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Optional<ContractVo> findVoById(String id) {
		return this.findOne(id).map(this::packVo);
	}

	@Override
	public Page<ContractGoodsInfoVo> findByGoods(Integer page, Integer size,
			String goodsName, String param, String buyerName, String sortKey,
			String sortOrder) {
		// 只有采购合同的项目
		List<String> projectIdList1 = new ArrayList<>();
		// 有销售合同的项目
		List<String> projectIdList2 = new ArrayList<>();
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		// 查询供应链新增融资关联的项目
		List<Financing> all = financingService.findAll();
		if (CollectionUtils.isEmpty(all)) {
			return Page.of(page, size, 0);
		}
		// 融资关联的项目ids
		List<String> collect = all.stream().map(Financing::getProjectId)
				.toList();
		// 融资关联的项目ids并且在履约中且存在仓储的项目ids
		List<String> projectIdList = projectService
				.findByProjectIdsAndIsExistStorage(collect,
						ProjectDef.State.PROCESSING.getCode(),
						CommonDef.Symbol.YES.getCode());
		List<Contract> contractList;
		if (CollectionUtils.isNotEmpty(projectIdList)) {
			contractList = this.findByProjectId(projectIdList);
			if (CollectionUtils.isNotEmpty(contractList)) {
				Map<String, List<Contract>> map = contractList.stream()
						.collect(Collectors.groupingBy(Contract::getProjectId));
				for (List<Contract> contracts : map.values()) {
					if (contracts.stream()
							.filter(contract -> ContractDef.State.COMPLETED
									.match(contract.getState()))
							.anyMatch(contract -> ContractDef.ContractType.SALES
									.match(contract.getContractType()))) {
						projectIdList2.add(contracts.get(0).getProjectId());
					} else if (contracts.stream()
							.filter(contract -> ContractDef.State.COMPLETED
									.match(contract.getState()))
							.noneMatch(
									contract -> ContractDef.ContractType.SALES
											.match(contract
													.getContractType()))) {
						projectIdList1.add(contracts.get(0).getProjectId());
					}
				}
			}
			queryWrapper.in(CollectionUtils.isNotEmpty(projectIdList),
					Contract::getProjectId, projectIdList);
		}

		// 货物名称
		if (Objects.nonNull(goodsName)) {
			queryWrapper.like(Contract::getGoodsName, goodsName);
		}
		// 项目名称或合同名称
		if (StringUtils.isNotBlank(param)) {
			List<String> projectIds = projectService.findByNameLike(param)
					.stream()
					.filter(project -> CommonDef.Symbol.YES
							.match(project.getIsExistStorage()))
					.map(Project::getId).distinct().toList();
			// 根据合同名称查询时要过滤出销售合同
			List<String> contractIds = this.findByNameLike(param).stream()
					.filter(contract -> ContractDef.ContractType.SALES
							.match(contract.getContractType()))
					.map(Contract::getId).distinct().toList();
			queryWrapper.and(i -> i
					.in(CollectionUtils.isNotEmpty(projectIds),
							Contract::getProjectId, projectIds)
					.or()
					.in(com.baomidou.mybatisplus.core.toolkit.CollectionUtils
							.isNotEmpty(contractIds), Contract::getId,
							contractIds));
		}
		// 采购方名称
		if (StringUtils.isNotBlank(buyerName)) {
			// 根据采购方名称查询时需要加上合同类型为销售合同
			queryWrapper.eq(Contract::getContractType,
					ContractDef.ContractType.SALES.getCode());
			queryWrapper.and(i -> i.apply(StringUtils.isNoneBlank(buyerName),
					"(JSON_EXTRACT(downstream_purchasers_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
					buyerName));
		}
		// 采购合同或者 销售合同
		queryWrapper.in(Contract::getContractType,
				List.of(ContractDef.ContractType.SALES.getCode(),
						ContractDef.ContractType.PURCHASE.getCode()));
		// 已完成状态
		queryWrapper.eq(Contract::getState,
				ContractDef.State.COMPLETED.getCode());

		if (com.zhihaoscm.common.util.utils.StringUtils.isNoneBlank(sortKey,
				sortOrder)) {
			queryWrapper.last(
					"order by " + sortKey + " " + sortOrder + " , id DESC");
		} else {
			// 按项目创建时间时间倒序，项目相同按合同创建时间倒序；
			queryWrapper.last("order by project_id desc, created_time desc");
		}

		contractList = repository.selectList(queryWrapper);
		// 要过滤的合同
		List<String> contractIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(contractList)) {
			if (CollectionUtils.isNotEmpty(projectIdList1)) {
				for (String projectId : projectIdList1) {
					contractIds.addAll(contractList.stream()
							.filter(contract -> projectId
									.equals(contract.getProjectId()))
							.filter(contract -> ContractDef.ContractType.PURCHASE
									.match(contract.getContractType()))
							.skip(1).map(Contract::getId).toList());

				}
			}

			if (CollectionUtils.isNotEmpty(projectIdList2)) {
				for (String projectId : projectIdList2) {
					contractIds.addAll(contractList.stream()
							.filter(contract -> projectId
									.equals(contract.getProjectId()))
							.filter(contract -> ContractDef.ContractType.PURCHASE
									.match(contract.getContractType()))
							.map(Contract::getId).toList());
				}
			}
		}

		if (CollectionUtils.isNotEmpty(contractIds)) {
			if (CollectionUtils.isNotEmpty(contractIds)) {
				queryWrapper.notIn(Contract::getId, contractIds);
			}
		}

		Page<Contract> infoPage = repository.selectPage(new Page<>(page, size),
				queryWrapper);

		return PageUtil.getRecordsInfoPage(infoPage,
				this.packPageGoodsInfoVos(infoPage.getRecords()));

	}

	/**
	 * 根据合同名称模糊查询合同列表
	 *
	 * @param name
	 * @return
	 */
	@Override
	public List<Contract> findByNameLike(String name) {
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.like(StringUtils.isNotBlank(name), Contract::getName,
				name);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Contract> findByProjectId(List<String> projectId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.in(Contract::getProjectId, projectId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Contract> findUnfinished(String projectId) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Contract::getProjectId, projectId);
		wrapper.ne(Contract::getState, ContractDef.State.COMPLETED.getCode());
		return repository.selectList(wrapper);
	}

	@Override
	public List<ContractVo> findByProjectIdAndType(String projectId,
			Integer type) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Contract::getProjectId, projectId);
		wrapper.eq(Contract::getContractType, type);
		return this.packContractVos(repository.selectList(wrapper));
	}

	@Override
	public List<Contract> findByPurchaserIdAndState(Long customId,
			Integer state) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(customId),
				Contract::getDownstreamPurchasersId, customId)
				.eq(Objects.nonNull(state), Contract::getState, state);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Contract> find(LocalDateTime startTime, LocalDateTime endTime) {
		LambdaQueryWrapper<Contract> wrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(wrapper);
		wrapper.ge(Objects.nonNull(startTime), Contract::getFinishDate,
				startTime)
				.lt(Objects.nonNull(endTime), Contract::getFinishDate, endTime);
		return repository.selectList(wrapper);
	}

	@Override
	public List<Contract> find(String purchaserName, String goodsName,
			String contractName, Integer type, Long purchaserId) {
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.like(Objects.nonNull(contractName), Contract::getName,
				contractName);
		queryWrapper.like(Objects.nonNull(goodsName), Contract::getGoodsName,
				goodsName);
		queryWrapper.eq(Objects.nonNull(type), Contract::getContractType, type);
		queryWrapper.eq(Objects.nonNull(purchaserId),
				Contract::getDownstreamPurchasersId, purchaserId);
		if (StringUtils.isNotBlank(purchaserName)
				&& ContractDef.Type.SELL.match(type)) {
			queryWrapper.and(i -> i.apply(
					StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(downstream_purchasers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName));
		} else if (StringUtils.isNotBlank(purchaserName)
				&& ContractDef.Type.BUY.match(type)) {
			queryWrapper.and(i -> i.apply(
					StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(upstream_suppliers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName));
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Contract> find(String purchaserName, String goodsName,
			Integer type, Long purchaserId) {
		LambdaQueryWrapper<Contract> queryWrapper = Wrappers
				.lambdaQuery(Contract.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.like(Objects.nonNull(goodsName), Contract::getGoodsName,
				goodsName);
		queryWrapper.eq(Objects.nonNull(type), Contract::getContractType, type);
		queryWrapper.eq(Objects.nonNull(purchaserId),
				Contract::getDownstreamPurchasersId, purchaserId);
		if (StringUtils.isNotBlank(purchaserName)
				&& ContractDef.Type.SELL.match(type)) {
			queryWrapper.and(i -> i.apply(
					StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(downstream_purchasers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName).or().eq(Contract::getName, purchaserName));
		} else if (StringUtils.isNotBlank(purchaserName)
				&& ContractDef.Type.BUY.match(type)) {
			queryWrapper.and(i -> i.apply(
					StringUtils.isNoneBlank(purchaserName),
					"(JSON_EXTRACT(upstream_suppliers_enterprise, '$.name') LIKE CONCAT('%',{0},'%')) ",
					purchaserName).or().eq(Contract::getName, purchaserName));
		}
		return repository.selectList(queryWrapper);
	}

	/**
	 * 根据合同id获取预估可提货余额
	 *
	 * @param contractId
	 * @return
	 */
	@Override
	public Optional<BigDecimal> findEstimatedAmount(String contractId) {
		Contract contract = super.findOne(contractId).orElse(null);
		if (Objects.nonNull(contract)) {

			if (ContractDef.ContractType.SALES
					.match(contract.getContractType())) {
				// 预估可提货余额：关联了该合同的还款（关联路径：还款-借据-融资-合同）中供应链确认释放的可提货额度金额之和+状态为已完成的收款费用类型为货款付款方式不是流动贷的总金额-已提货预估金额-状态为已完成的货款退款+额度变更增加的货款-额度变更减少的货款
				// 关联了该合同的还款（关联路径：还款-借据-融资-合同）中供应链确认释放的可提货额度金额之和
				BigDecimal repaymentAmount = repaymentService
						.findAmount(contractId,
								RepaymentDef.State.RELEASED.getCode())
						.orElse(BigDecimal.ZERO);
				// 实际付货款/收货款总金额 =该项目收款状态为完成的费用类型为货款 付款方式不是流动贷的 金额汇总
				BigDecimal payedAmount = paymentService
						.findAmount(contractId,
								PaymentDef.State.COMPLETED.getCode(),
								contract.getContractType(),
								PaymentDef.CostType.GOODS_PAYMENT.getCode())
						.orElse(BigDecimal.ZERO);

				// 已提货未签收总金额 订单状态为已完成 签收状态为未开始和进行中的数据
				// 1.已提货未签收总金额==
				// 已申请提货（已完成、作废中、签署中、待签署、待确认订单）未签收（无签收单、草稿、待确认、待签署/签署中、作废中）数量/重量*单价汇总
				Optional<BigDecimal> orderAmount = orderService
						.findAmount(contractId);
				// 已提货已签收（签收状态为已完成）未对账（对账状态为未开始和进行中的数据）总金额
				// 2.已提货已签收未对账总金额=已提货已签收（签收完成）未对账（无对账单、草稿、签署中/待签署、确认中/待确认、已驳回、作废中）的签收数量/重量*订单单价汇总（如果签收订单选择合并签收单价取包含的规格的平均单价）
				Optional<BigDecimal> signAmount = signReceiptService.findAmount(
						contractId,
						List.of(SignReceiptDef.Status.FINISHED.getCode()),
						List.of(OrderDef.BusinessStatus.NOT_STARTED.getCode(),
								OrderDef.BusinessStatus.IN_PROGRESS.getCode(),
								OrderDef.BusinessStatus.INVALID.getCode()),
						contract.getContractType());
				// 已提货已对账（对账状态为已完成）对账总金额
				// 3.已提货已对账总金额=已提货已对账（对账完成,作废中）实际对账金额合计
				Optional<BigDecimal> reconciliationAmount = reconciliationService
						.findRecAmount(contractId,
								List.of(ReconciliationDef.State.FINISHED
										.getCode()),
								contract.getContractType());
				// 已提货预估金额=已提货未签收总金额+已提货已签收未对账总金额+已提货已对账总金额：
				BigDecimal goodsAmount = orderAmount.orElse(BigDecimal.ZERO)
						.add(signAmount.orElse(BigDecimal.ZERO))
						.add(reconciliationAmount.orElse(BigDecimal.ZERO));
				// 货款金额做处理
				BigDecimal loanAmount = BigDecimal.ZERO;
				if (Objects.nonNull(contract.getLoanAmount())) {
					loanAmount = contract.getLoanAmount();
				}
				// 状态为已完成的货款退款
				BigDecimal refundLoanAmount = BigDecimal.ZERO;
				if (Objects.nonNull(contract.getRefundLoanAmount())) {
					refundLoanAmount = contract.getRefundLoanAmount();
				}
				// 期初可提货余额 =期初货款收入总金额-对账总金额
				BigDecimal projectInceptionAmount = projectInceptionDetailService
						.findAmount(contractId,
								ProjectInceptionDef.Type.SELL.getCode())
						.orElse(BigDecimal.ZERO);
				// 预估可提货余额：确认释放的可提货额度金额之和+实际收款总金额-状态为已完成的货款退款-已提货预估金额+货款金额+期初可提货余额
				BigDecimal estimatedGoodsAmount = repaymentAmount
						.add(payedAmount).subtract(refundLoanAmount)
						.subtract(goodsAmount).add(loanAmount)
						.add(projectInceptionAmount);

				return Optional.of(estimatedGoodsAmount);
			} else if (ContractDef.ContractType.PURCHASE
					.match(contract.getContractType())) {
				// 预估可提货余额：付款费用类型为货款的总金额-已提货预估金额

				// 实际付货款款总金额=该合同 所关联的 付款的货款 金额汇总
				BigDecimal payedAmount = paymentService
						.findAmount(contractId, null,
								contract.getContractType(),
								PaymentDef.CostType.GOODS_PAYMENT.getCode())
						.orElse(BigDecimal.ZERO);

				// 已提货未关联签收总金额 关联了订单 未关联签收的项目
				// 1. 已提货未签收总金额 = 已提货未关联签收数量/重量*单价 汇总
				Optional<BigDecimal> orderAmount = orderService.findAmount(
						contractId, null, null, contract.getContractType());
				// 已提货已签收未对账总金额
				// 2. 已提货已签收未对账总金额 = 已提货已关联签收数量/重量*单价 汇总
				Optional<BigDecimal> signAmount = signReceiptService.findAmount(
						contractId, null, null, contract.getContractType());
				// 已提货已对账总金额
				// 3. 已提货已对账总金额 = 已提货已关联签收已关联对账实际对账金额合计
				Optional<BigDecimal> reconciliationAmount = reconciliationService
						.findRecAmount(contractId, null,
								contract.getContractType());
				// 已提货预估金额 = 已提货未签收总金额+已提货已签收未对账总金额+已提货已对账总金额：
				// 已提货预估金额 = 已提货未关联签收总金额+已提货已关联签收未关联对账总金额+已提货已关联签收已关联对账总金额：
				BigDecimal goodsAmount = orderAmount.orElse(BigDecimal.ZERO)
						.add(signAmount.orElse(BigDecimal.ZERO))
						.add(reconciliationAmount.orElse(BigDecimal.ZERO));
				// 货款金额做处理
				BigDecimal loanAmount = BigDecimal.ZERO;
				if (Objects.nonNull(contract.getLoanAmount())) {
					loanAmount = contract.getLoanAmount();
				}
				// 期初可提货余额 =期初货款收入总金额-对账总金额
				BigDecimal projectInceptionAmount = projectInceptionDetailService
						.findAmount(contractId,
								ProjectInceptionDef.Type.BUY.getCode())
						.orElse(BigDecimal.ZERO);
				// 预估可提货余额：实际付货款总金额-已提货预估金额+(额度变更的)货款金额+期初可提货余额
				BigDecimal estimatedGoodsAmount = payedAmount
						.subtract(goodsAmount).add(loanAmount)
						.add(projectInceptionAmount);
				return Optional.of(estimatedGoodsAmount);
			}
		}
		return Optional.of(BigDecimal.ZERO);
	}

	/**
	 * 封装vo
	 * 
	 * @param contract
	 * @return
	 */
	private ContractVo packVo(Contract contract) {
		ContractVo vo = new ContractVo();
		vo.setContract(contract);
		// 设置项目信息
		if (ObjectUtils.isNotEmpty(contract.getProjectId())) {
			projectService.findOne(contract.getProjectId())
					.ifPresent(vo::setProject);
		}
		// 设置关联合同
		List<Contract> relateContractList = super.findByIdsNoDeleted(
				contract.getRelateContractIds());
		vo.setRelateContracts(relateContractList);
		return vo;
	}

	/**
	 * 封装分页查询的vo信息
	 *
	 * @param
	 * @return
	 */
	private List<ContractVo> packPageVos(List<Contract> contractList) {
		List<ContractVo> vos = new ArrayList<>();
		// 项目信息
		List<String> projectIds = contractList.stream()
				.map(Contract::getProjectId).distinct().toList();
		Map<String, Project> projectMap = projectService.findByIds(projectIds)
				.stream().collect(Collectors.toMap(Project::getId, e -> e));
		for (Contract contract : contractList) {
			ContractVo vo = new ContractVo();
			vo.setContract(contract);
			vo.setProject(projectMap.get(contract.getProjectId()));
			vos.add(vo);
		}
		return vos;
	}

	/**
	 * 封装合同vo信息
	 *
	 * @param
	 * @return
	 */
	private List<ContractVo> packContractVos(List<Contract> contractList) {
		List<ContractVo> contractVos = new ArrayList<>();
		for (Contract contract : contractList) {
			ContractVo vo = new ContractVo();
			vo.setContract(contract);
			this.findEstimatedAmount(contract.getId())
					.ifPresent(vo::setEstimatedGoodsAmount);
			contractVos.add(vo);
		}
		return contractVos;
	}

	/**
	 * 封装分页查询的vo信息
	 *
	 * @param
	 * @return
	 */
	private List<ContractGoodsInfoVo> packPageGoodsInfoVos(
			List<Contract> contractList) {
		if (CollectionUtils.isEmpty(contractList)) {
			return List.of();
		}
		List<String> contractIds = contractList.stream().map(Contract::getId)
				.distinct().toList();
		List<String> projectIds = contractList.stream()
				.map(Contract::getProjectId).distinct().toList();
		List<Project> projectList = projectService.findByIds(projectIds);
		List<Long> goodIds = projectList.stream().map(Project::getGoodsId)
				.toList();
		List<Goods> goodsList = goodsService.findByIds(goodIds);
		Map<Long, Goods> goodsMap = goodsList.stream()
				.collect(Collectors.toMap(Goods::getId, e -> e));

		Map<String, Project> projectMap = projectList.stream()
				.collect(Collectors.toMap(Project::getId, e -> e));
		// 仓储期初入库信息
		List<StorageInceptionInboundDetail> storageInboundList = storageInceptionInboundDetailService
				.findByProjectIds(projectIds);
		// 仓储期初入库根据项目分组
		Map<String, List<StorageInceptionInboundDetail>> inboundMap = storageInboundList
				.stream().collect(Collectors.groupingBy(
						StorageInceptionInboundDetail::getProjectId));
		// 仓储期初出库信息
		List<StorageInceptionOutboundDetail> storagOutnboundList = storageInceptionOutboundDetailService
				.findByProjectIds(projectIds);
		// 仓储期初出库根据项目分组
		Map<String, List<StorageInceptionOutboundDetail>> outboundMap = storagOutnboundList
				.stream().collect(Collectors.groupingBy(
						StorageInceptionOutboundDetail::getProjectId));

		// 根据项目id查询已入库的数据
		List<Inbound> inboundList = inboundService.findByProjectIds(projectIds,
				List.of(InboundDef.Status.INBOUNDED.getCode()),
				InboundDef.Type.JXC.getCode());
		// 已入库的数据根据项目id进行分组
		Map<String, List<Inbound>> inboundMap1 = inboundList.stream()
				.collect(Collectors.groupingBy(Inbound::getProjectId));
		// 根据项目id查询已出库的数据
		List<Outbound> outboundList = outboundService.findByProjectIds(
				projectIds, List.of(OutboundDef.Status.OUTBOUND.getCode()),
				InboundDef.Type.JXC.getCode());
		// 已入库的数据根据项目id进行分组
		Map<String, List<Outbound>> outboundMap1 = outboundList.stream()
				.collect(Collectors.groupingBy(Outbound::getProjectId));

		// 根据项目id查询待出库的数据
		List<Outbound> toOutboundList = outboundService.findByProjectIds(
				projectIds,
				List.of(OutboundDef.Status.CONFIRMING.getCode(),
						OutboundDef.Status.TO_BE_INITIATED.getCode(),
						OutboundDef.Status.DRAFT.getCode(),
						OutboundDef.Status.REJECTED.getCode(),
						OutboundDef.Status.INVALIDING.getCode(),
						OutboundDef.Status.SIGNING.getCode()),
				InboundDef.Type.JXC.getCode());
		// 待出库的数据根据项目id进行分组
		Map<String, List<Outbound>> toOutboundMap = toOutboundList.stream()
				.collect(Collectors.groupingBy(Outbound::getProjectId));
		// 根据合同id查询质押信息
		List<Pledge> pledges = pledgeService.findByContractIds(contractIds);
		// 质押信息根据合同id进行分组
		Map<String, List<Pledge>> pledgeMap = pledges.stream()
				.collect(Collectors.groupingBy(Pledge::getContractId));

		return contractList.stream().map(e -> {
			ContractGoodsInfoVo vo = new ContractGoodsInfoVo();
			vo.setContract(e);
			Project project = projectMap.get(e.getProjectId());
			vo.setProject(project);
			// 期初入库数量/重量
			BigDecimal inboundWeight1 = BigDecimal.ZERO;
			// 期初出库数量/重量
			BigDecimal outboundWeight1 = BigDecimal.ZERO;
			// 已完成入库数量/重量
			BigDecimal inboundWeight2 = BigDecimal.ZERO;
			// 已完成出库数量/重量
			BigDecimal outboundWeight2 = BigDecimal.ZERO;
			// 待出库数量/重量
			BigDecimal outboundWeight3 = BigDecimal.ZERO;
			// 库存数量/重量
			BigDecimal stockWeight = BigDecimal.ZERO;
			List<StorageInceptionInboundDetail> inbounds1 = inboundMap
					.get(e.getProjectId());
			// 期初入库信息
			if (CollectionUtils.isNotEmpty(inbounds1)) {
				inboundWeight1 = inbounds1.stream()
						.map(StorageInceptionInboundDetail::getQuantity)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			List<StorageInceptionOutboundDetail> outbounds1 = outboundMap
					.get(e.getProjectId());
			// 期初出库信息
			if (CollectionUtils.isNotEmpty(outbounds1)) {
				outboundWeight1 = outbounds1.stream()
						.map(StorageInceptionOutboundDetail::getQuantity)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			// 已完成入库信息
			List<Inbound> inbounds2 = inboundMap1.get(e.getProjectId());
			// 已完成入库信息
			if (CollectionUtils.isNotEmpty(inbounds2)) {
				inboundWeight2 = inbounds2.stream()
						.map(Inbound::getInboundWeight).filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);

			}
			// 已完成出库信息
			List<Outbound> outbounds2 = outboundMap1.get(e.getProjectId());
			// 已完成出库数量/重量
			if (CollectionUtils.isNotEmpty(outbounds2)) {
				outboundWeight2 = outbounds2.stream()
						.map(Outbound::getOutboundWeight)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			// 待出库信息
			List<Outbound> outbounds3 = toOutboundMap.get(e.getProjectId());
			// 待出库数量/重量
			if (CollectionUtils.isNotEmpty(outbounds3)) {
				outboundWeight3 = outbounds3.stream()
						.map(Outbound::getOutboundWeight)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			// 库存数量/重量= 期初库存（期初入库-期初出库) + 有效入库数量 - 有效出库数量
			stockWeight = stockWeight.add(inboundWeight1).add(inboundWeight2)
					.subtract(outboundWeight1).subtract(outboundWeight2);
			vo.setStockWeight(stockWeight);
			// 质押重量
			BigDecimal pledgeWeight = BigDecimal.ZERO;
			// 质押信息
			List<Pledge> pledgeList = pledgeMap.get(e.getId());
			if (CollectionUtils.isNotEmpty(pledgeList)) {
				Pledge pledge = pledgeList.stream()
						.max(Comparator.comparing(Pledge::getCreatedTime))
						.orElse(null);
				if (Objects.nonNull(pledge)) {
					String pledgeInfo = pledge.getPledgeInfo();
					List<PledgeInfo> pledgeInfos = this
							.convertPledgeInfo(pledgeInfo);
					PledgeInfo pledgeInfo1 = pledgeInfos
							.get(pledgeInfos.size() - 1);
					pledgeWeight = pledgeInfo1.getPledgeNum();
				}
			}
			if (BigDecimal.ZERO.compareTo(pledgeWeight) != 0) {
				vo.setPledgeWeight(pledgeWeight);
			}
			// 质押数量/重量*库存控货比
			BigDecimal pledgeWeight1;
			// 质押数量/重量*库存控货比
			pledgeWeight1 = Objects.nonNull(project.getInventoryControlRatio())
					? pledgeWeight.multiply(project.getInventoryControlRatio())
							.divide(new BigDecimal(100), 2,
									RoundingMode.HALF_UP)
					: pledgeWeight;
			// 可销售出库数量/重量=库存数量/重量-质押数量/重量*库存控货比-待出库数量/重量
			BigDecimal saleOutboundWeight;
			saleOutboundWeight = stockWeight.subtract(pledgeWeight1)
					.subtract(outboundWeight3);
			vo.setSaleOutboundWeight(saleOutboundWeight);
			vo.setInventoryControlRatio(project.getInventoryControlRatio());
			vo.setGoodsName(project.getGoodsName());
			vo.setUnit(goodsMap.get(project.getGoodsId()).getUnit());
			return vo;
		}).toList();

	}

	private List<PledgeInfo> convertPledgeInfo(String data) {
		Gson gson = new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();
		Type listType = new TypeToken<List<PledgeInfo>>() {
		}.getType();
		return gson.fromJson(data, listType);
	}
}
