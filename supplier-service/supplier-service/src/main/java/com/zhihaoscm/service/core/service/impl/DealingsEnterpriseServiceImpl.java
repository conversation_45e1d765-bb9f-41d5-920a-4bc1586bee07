package com.zhihaoscm.service.core.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.domain.bean.entity.CustomerBank;
import com.zhihaoscm.domain.bean.entity.DealingsEnterprise;
import com.zhihaoscm.domain.bean.vo.DealingsEnterpriseVo;
import com.zhihaoscm.domain.meta.biz.CustomerBankDef;
import com.zhihaoscm.domain.meta.biz.DealingsEnterpriseDef;
import com.zhihaoscm.service.core.mapper.DealingsEnterpriseMapper;
import com.zhihaoscm.service.core.service.DealingsEnterpriseService;
import com.zhihaoscm.service.core.service.usercenter.CustomerBankService;

/**
 * <p>
 * 往来企业 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class DealingsEnterpriseServiceImpl extends
		MpLongIdBaseServiceImpl<DealingsEnterprise, DealingsEnterpriseMapper>
		implements DealingsEnterpriseService {

	public DealingsEnterpriseServiceImpl(DealingsEnterpriseMapper repository) {
		super(repository);
	}

	@Autowired
	private CustomerBankService customerBankService;

	@Override
	public Page<DealingsEnterprise> paging(Integer page, Integer size,
			String institutionName, Integer enterpriseType,
			List<Integer> states, String sortKey, String sortOrder) {
		LambdaQueryWrapper<DealingsEnterprise> wrapper = Wrappers
				.lambdaQuery(DealingsEnterprise.class);
		this.filterDeleted(wrapper);
		wrapper.like(StringUtils.isNotBlank(institutionName),
				DealingsEnterprise::getInstitutionName, institutionName);
		wrapper.eq(Objects.nonNull(enterpriseType),
				DealingsEnterprise::getEnterpriseType, enterpriseType);
		// 状态
		if (CollectionUtils.isEmpty(states)) {
			wrapper.and(x -> x
					.eq(DealingsEnterprise::getInitiator,
							DealingsEnterpriseDef.Initiator.SUPPLIER_CHAIN
									.getCode())
					.or(y -> y
							.eq(DealingsEnterprise::getInitiator,
									DealingsEnterpriseDef.Initiator.CUSTOM
											.getCode())
							.ne(DealingsEnterprise::getState,
									DealingsEnterpriseDef.State.REJECTED
											.getCode())));
		} else {
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {

					switch (DealingsEnterpriseDef.QueryState.from(state)) {
						// 确认中
						case CONFIRMING -> x.or(y -> y.eq(
								DealingsEnterprise::getInitiator,
								DealingsEnterpriseDef.Initiator.SUPPLIER_CHAIN
										.getCode())
								.eq(DealingsEnterprise::getState,
										DealingsEnterpriseDef.State.TO_BE_CONFIRMED
												.getCode()));
						// 待确认
						case TO_BE_CONFIRMED -> x.or(y -> y
								.eq(DealingsEnterprise::getInitiator,
										DealingsEnterpriseDef.Initiator.CUSTOM
												.getCode())
								.eq(DealingsEnterprise::getState,
										DealingsEnterpriseDef.State.TO_BE_CONFIRMED
												.getCode()));
						// 已驳回
						case REJECTED -> x.or(y -> y.eq(
								DealingsEnterprise::getInitiator,
								DealingsEnterpriseDef.Initiator.SUPPLIER_CHAIN
										.getCode())
								.eq(DealingsEnterprise::getState,
										DealingsEnterpriseDef.State.REJECTED
												.getCode()));
						// 已完成
						case COOPERATION ->
							x.or(y -> y.eq(DealingsEnterprise::getState,
									DealingsEnterpriseDef.State.COOPERATION
											.getCode()));
					}
				}
			});
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("ORDER BY " + sortKey + " " + sortOrder);
		} else {
			wrapper.orderByDesc(DealingsEnterprise::getUpdatedTime);
		}
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Page<DealingsEnterprise> customPaging(Integer page, Integer size,
			String institutionName, List<Integer> states, String sortKey,
			String sortOrder, Long customerId) {
		LambdaQueryWrapper<DealingsEnterprise> wrapper = Wrappers
				.lambdaQuery(DealingsEnterprise.class);
		this.filterDeleted(wrapper);
		wrapper.like(StringUtils.isNotBlank(institutionName),
				DealingsEnterprise::getInstitutionName, institutionName);
		wrapper.eq(DealingsEnterprise::getCustomerId, customerId);
		// 状态
		if (CollectionUtils.isEmpty(states)) {
			wrapper.and(x -> x
					.eq(DealingsEnterprise::getInitiator,
							DealingsEnterpriseDef.Initiator.CUSTOM.getCode())
					.or(y -> y.eq(DealingsEnterprise::getInitiator,
							DealingsEnterpriseDef.Initiator.SUPPLIER_CHAIN
									.getCode())
							.ne(DealingsEnterprise::getState,
									DealingsEnterpriseDef.State.REJECTED
											.getCode())));
		} else {
			wrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {

					switch (DealingsEnterpriseDef.QueryState.from(state)) {
						// 确认中
						case CONFIRMING -> x.or(y -> y
								.eq(DealingsEnterprise::getInitiator,
										DealingsEnterpriseDef.Initiator.CUSTOM
												.getCode())
								.eq(DealingsEnterprise::getState,
										DealingsEnterpriseDef.State.TO_BE_CONFIRMED
												.getCode()));
						// 待确认
						case TO_BE_CONFIRMED -> x.or(y -> y.eq(
								DealingsEnterprise::getInitiator,
								DealingsEnterpriseDef.Initiator.SUPPLIER_CHAIN
										.getCode())
								.eq(DealingsEnterprise::getState,
										DealingsEnterpriseDef.State.TO_BE_CONFIRMED
												.getCode()));
						// 已驳回
						case REJECTED -> x.or(y -> y
								.eq(DealingsEnterprise::getInitiator,
										DealingsEnterpriseDef.Initiator.CUSTOM
												.getCode())
								.eq(DealingsEnterprise::getState,
										DealingsEnterpriseDef.State.REJECTED
												.getCode()));
						// 已完成
						case COOPERATION ->
							x.or(y -> y.eq(DealingsEnterprise::getState,
									DealingsEnterpriseDef.State.COOPERATION
											.getCode()));
					}
				}
			});
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("ORDER BY " + sortKey + " " + sortOrder);
		} else {
			wrapper.orderByDesc(DealingsEnterprise::getCreatedTime);
		}
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Page<DealingsEnterprise> backendPaging(Integer page, Integer size,
			String key, String sortKey, String sortOrder, Long customerId) {

		LambdaQueryWrapper<DealingsEnterprise> wrapper = Wrappers
				.lambdaQuery(DealingsEnterprise.class);
		this.filterDeleted(wrapper);

		if (StringUtils.isNotBlank(key)) {
			wrapper.and(x -> x.like(DealingsEnterprise::getInstitutionName, key)
					.or()
					.like(DealingsEnterprise::getUnifiedSocialCreditCode, key));
		}

		wrapper.eq(DealingsEnterprise::getInitiator,
				DealingsEnterpriseDef.Initiator.SUPPLIER_CHAIN.getCode());

		if (Objects.nonNull(customerId)) {
			wrapper.eq(DealingsEnterprise::getCustomerId, customerId);
		}

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("ORDER BY " + sortKey + " " + sortOrder);
		} else {
			wrapper.orderByDesc(DealingsEnterprise::getCreatedTime);
		}
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Page<DealingsEnterprise> backendSupplierPaging(Integer page,
			Integer size, Long tenantId, String sortKey, String sortOrder) {

		LambdaQueryWrapper<DealingsEnterprise> wrapper = Wrappers
				.lambdaQuery(DealingsEnterprise.class);
		this.filterDeleted(wrapper);

		if (Objects.nonNull(tenantId)) {
			wrapper.eq(DealingsEnterprise::getTenantId, tenantId);
		}

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			wrapper.last("ORDER BY " + sortKey + " " + sortOrder);
		} else {
			wrapper.orderByDesc(DealingsEnterprise::getCreatedTime);
		}
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Page<DealingsEnterprise> selector(Integer page, Integer size,
			String institutionName, Integer enterpriseType) {
		LambdaQueryWrapper<DealingsEnterprise> wrapper = Wrappers
				.lambdaQuery(DealingsEnterprise.class);
		this.filterDeleted(wrapper);
		wrapper.like(StringUtils.isNotBlank(institutionName),
				DealingsEnterprise::getInstitutionName, institutionName);
		wrapper.eq(Objects.nonNull(enterpriseType),
				DealingsEnterprise::getEnterpriseType, enterpriseType);
		wrapper.eq(DealingsEnterprise::getState,
				DealingsEnterpriseDef.State.COOPERATION.getCode());
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Optional<DealingsEnterpriseVo> findVoById(Long id) {
		return super.findOne(id).map(this::packVo);
	}

	@Override
	public Optional<DealingsEnterprise> findByUnifiedSocialCreditCode(
			String unifiedSocialCreditCode, Integer enterpriseType,
			Long userId) {
		LambdaQueryWrapper<DealingsEnterprise> wrapper = Wrappers
				.lambdaQuery(DealingsEnterprise.class);
		this.filterDeleted(wrapper);
		wrapper.eq(DealingsEnterprise::getUnifiedSocialCreditCode,
				unifiedSocialCreditCode);
		wrapper.eq(DealingsEnterprise::getEnterpriseType, enterpriseType);
		wrapper.eq(DealingsEnterprise::getSupplierChainId, userId);
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public Optional<DealingsEnterprise> findBySupplierChainUnifiedSocialCreditCode(
			String unifiedSocialCreditCode, Integer enterpriseType,
			Long customerId) {
		LambdaQueryWrapper<DealingsEnterprise> wrapper = Wrappers
				.lambdaQuery(DealingsEnterprise.class);
		this.filterDeleted(wrapper);
		wrapper.eq(DealingsEnterprise::getSupplierChainUnifiedSocialCreditCode,
				unifiedSocialCreditCode);
		wrapper.eq(DealingsEnterprise::getEnterpriseType, enterpriseType);
		wrapper.eq(DealingsEnterprise::getCustomerId, customerId);
		return Optional.ofNullable(repository.selectOne(wrapper));
	}

	@Override
	public List<DealingsEnterprise> findByCustomerIdAndEnterpriseType(
			Long customerId, Integer enterpriseType) {
		LambdaQueryWrapper<DealingsEnterprise> wrapper = Wrappers
				.lambdaQuery(DealingsEnterprise.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(customerId),
				DealingsEnterprise::getCustomerId, customerId);
		wrapper.eq(Objects.nonNull(enterpriseType),
				DealingsEnterprise::getEnterpriseType, enterpriseType);
		return repository.selectList(wrapper);
	}

	@Override
	public List<DealingsEnterprise> findByTenantId(List<Long> tenantIds) {
		LambdaQueryWrapper<DealingsEnterprise> wrapper = Wrappers
				.lambdaQuery(DealingsEnterprise.class);
		this.filterDeleted(wrapper);
		wrapper.in(DealingsEnterprise::getTenantId, tenantIds);
		return repository.selectList(wrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public DealingsEnterprise create(DealingsEnterprise dealingsEnterprise,
			List<CustomerBank> banks) {
		DealingsEnterprise result = super.create(dealingsEnterprise);
		this.createBankInfo(result.getId(), banks);
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public DealingsEnterprise updateAllProperties(
			DealingsEnterprise dealingsEnterprise, List<CustomerBank> banks) {
		DealingsEnterprise result = super.updateAllProperties(
				dealingsEnterprise);
		this.updateBankAccount(result.getId(), banks);
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delete(Long id) {
		super.delete(id);
		customerBankService.deleteByCustomerIdAndBizType(id,
				CustomerBankDef.BizType.DEALING_ENTERPRISE.getCode());
	}

	@Override
	public void confirm(DealingsEnterprise dealingsEnterprise) {
		dealingsEnterprise
				.setState(DealingsEnterpriseDef.State.COOPERATION.getCode());
		super.updateAllProperties(dealingsEnterprise);
	}

	@Override
	public void reject(DealingsEnterprise dealingsEnterprise) {
		dealingsEnterprise
				.setState(DealingsEnterpriseDef.State.REJECTED.getCode());
		super.updateAllProperties(dealingsEnterprise);
	}

	/**
	 * 组装数据
	 * 
	 * @param dealingsEnterprise
	 * @return
	 */
	private DealingsEnterpriseVo packVo(DealingsEnterprise dealingsEnterprise) {
		DealingsEnterpriseVo dealingsEnterpriseVo = new DealingsEnterpriseVo();
		dealingsEnterpriseVo.setDealingsEnterprise(dealingsEnterprise);
		List<CustomerBank> customerBanks = customerBankService
				.findByCustomerIdAndBizType(dealingsEnterprise.getId(),
						CustomerBankDef.BizType.DEALING_ENTERPRISE.getCode());
		dealingsEnterpriseVo.setBanks(customerBanks);
		return dealingsEnterpriseVo;
	}

	/**
	 * 组装银行账户数据
	 * 
	 * @param accountId
	 * @param customerBanks
	 */
	private void createBankInfo(Long accountId,
			List<CustomerBank> customerBanks) {
		if (CollectionUtils.isNotEmpty(customerBanks)) {
			if (customerBanks.size() == 1) {
				for (CustomerBank customerBank : customerBanks) {
					customerBank.setCustomerId(accountId);
					customerBank
							.setType(CustomerBankDef.Type.ENTERPRISE.getCode());
					customerBank.setIsDefault(CommonDef.Symbol.YES.getCode());
					customerBank.setBizType(
							CustomerBankDef.BizType.DEALING_ENTERPRISE
									.getCode());
				}
			} else {
				for (CustomerBank customerBank : customerBanks) {
					customerBank.setCustomerId(accountId);
					customerBank
							.setType(CustomerBankDef.Type.ENTERPRISE.getCode());
					customerBank.setBizType(
							CustomerBankDef.BizType.DEALING_ENTERPRISE
									.getCode());
				}
			}
			customerBankService.batchCreate(customerBanks);
		}
	}

	/**
	 * 更新银行账户-先删再增
	 */
	private void updateBankAccount(Long accountId,
			List<CustomerBank> newBanks) {
		List<CustomerBank> oldBanks = customerBankService
				.findByCustomerIdAndBizType(accountId,
						CustomerBankDef.BizType.DEALING_ENTERPRISE.getCode());
		List<Long> customerBankIds = oldBanks.stream().map(CustomerBank::getId)
				.distinct().toList();
		if (CollectionUtils.isNotEmpty(customerBankIds)) {
			customerBankService.batchDelete(customerBankIds);
		}
		this.createBankInfo(accountId, newBanks);
	}
}
