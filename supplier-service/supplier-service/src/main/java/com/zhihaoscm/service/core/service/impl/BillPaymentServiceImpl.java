package com.zhihaoscm.service.core.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.api.util.SpringUtil;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.log.annotation.LogRecord;
import com.zhihaoscm.common.log.context.LogRecordContext;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.ArrayReconciliationJsonInfo;
import com.zhihaoscm.domain.bean.json.ReconciliationJsonInfo;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.BillPaymentCountVo;
import com.zhihaoscm.domain.bean.vo.BillPaymentVo;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.utils.BillPaymentUtils;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.service.core.mapper.BillPaymentMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 开票 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
public class BillPaymentServiceImpl
		extends MpStringIdBaseServiceImpl<BillPayment, BillPaymentMapper>
		implements BillPaymentService {
	@Autowired
	private ReconciliationService reconciliationService;

	@Autowired
	private ContractService contractService;

	@Autowired
	private ProjectService projectService;

	@Autowired
	private StringRedisClient redisClient;

	@Autowired
	private MessageService messageService;

	@Autowired
	private SMSProperties wxSubscriptionProperties;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	public BillPaymentServiceImpl(BillPaymentMapper repository) {
		super(repository);
	}

	@Override
	public Page<BillPaymentVo> adminBuyPaging(Integer page, Integer size,
			String keyword, LocalDateTime startTime, LocalDateTime endTime,
			Boolean hasAll, String goodsName, List<Integer> states,
			String sortKey, String sortOrder) {
		LambdaQueryWrapper<BillPayment> queryWrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		queryWrapper.eq(BillPayment::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(BillPayment::getType,
				BillPaymentDef.Type.BUY.getCode());
		queryWrapper.eq(StringUtils.isNotBlank(goodsName),
				BillPayment::getGoodsName, goodsName);
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (!hasAll) {
			// 处理人是自己在的
			List<String> projectIdList = projectService.findByUserId(
					Objects.requireNonNull(UserContextHolder.getUser()).getId(),
					null);
			if (CollectionUtils.isNotEmpty(projectIdList)) {
				queryWrapper.in(BillPayment::getProjectId, projectIdList);
			} else {
				return Page.of(page, size, 0);
			}
		}

		if (StringUtils.isNotBlank(keyword)) {
			queryWrapper.and(i -> i.like(BillPayment::getProjectName, keyword)
					.or().like(BillPayment::getId, keyword));
		}

		queryWrapper.ge(Objects.nonNull(startTime), BillPayment::getBillDate,
				startTime);
		queryWrapper.le(Objects.nonNull(endTime), BillPayment::getBillDate,
				endTime);

		if (CollectionUtils.isNotEmpty(states)) {
			queryWrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					switch (BillPaymentDef.State.from(state)) {
						// 草稿，发起方是自己
						case DRAFT ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode()));

						// 待确认，发起方是对方
						case PENDING_CONFIRMATION ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

						// 确认中，发起方是自己
						case CONFIRMING ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

						// 已驳回，发起方是自己
						case REJECTED ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.REJECTED
													.getCode()));

						// 已完成，双方
						case COMPLETED -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.COMPLETED.getCode()));

						// 作废中，双方
						case INVALIDING -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.INVALIDING.getCode()));

						// 已作废，双方
						case INVALID -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.INVALID.getCode()));
					}
				}
			});
		} else {
			queryWrapper
					.and(x -> x
							.in(BillPayment::getState,
									BillPaymentDef.State.CONFIRMING.getCode(),
									BillPaymentDef.State.PENDING_CONFIRMATION
											.getCode(),
									BillPaymentDef.State.COMPLETED.getCode(),
									BillPaymentDef.State.INVALIDING.getCode(),
									BillPaymentDef.State.INVALID.getCode())
							.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.in(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode(),
											BillPaymentDef.State.REJECTED
													.getCode())));
		}

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last(
					"order by " + sortKey + " " + sortOrder + ", id DESC");
		} else {
			queryWrapper.orderByDesc(BillPayment::getUpdatedTime,
					BillPayment::getId);
		}
		Page<BillPayment> paging = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Page<BillPaymentVo> adminSalePaging(Integer page, Integer size,
			String keyword, String name, LocalDateTime startTime,
			LocalDateTime endTime, Boolean hasAll, String goodsName,
			List<Integer> states, String sortKey, String sortOrder) {
		LambdaQueryWrapper<BillPayment> queryWrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		queryWrapper.eq(BillPayment::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(BillPayment::getType,
				BillPaymentDef.Type.SELL.getCode());
		queryWrapper.eq(StringUtils.isNotBlank(goodsName),
				BillPayment::getGoodsName, goodsName);
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (!hasAll) {
			// 处理人是自己在的
			List<String> projectIdList = projectService.findByUserId(
					Objects.requireNonNull(UserContextHolder.getUser()).getId(),
					null);
			if (CollectionUtils.isNotEmpty(projectIdList)) {
				queryWrapper.in(BillPayment::getProjectId, projectIdList);
			} else {
				return Page.of(page, size, 0);
			}
		}

		if (StringUtils.isNotBlank(keyword)) {
			queryWrapper.and(i -> i.like(BillPayment::getProjectName, keyword)
					.or().like(BillPayment::getId, keyword));
		}

		if (StringUtils.isNotBlank(name)) {
			queryWrapper.and(i -> i.apply(StringUtils.isNoneBlank(name),
					"(JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
					name));
		}

		queryWrapper.ge(Objects.nonNull(startTime), BillPayment::getBillDate,
				startTime);
		queryWrapper.le(Objects.nonNull(endTime), BillPayment::getBillDate,
				endTime);

		if (CollectionUtils.isNotEmpty(states)) {
			queryWrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					switch (BillPaymentDef.State.from(state)) {
						// 草稿，发起方是自己
						case DRAFT ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode()));

						// 待确认，发起方是对方
						case PENDING_CONFIRMATION ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

						// 确认中，发起方是自己
						case CONFIRMING ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

						// 已驳回，发起方是自己
						case REJECTED ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.REJECTED
													.getCode()));

						// 已完成，双方
						case COMPLETED -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.COMPLETED.getCode()));

						// 作废中，双方
						case INVALIDING -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.INVALIDING.getCode()));

						// 已作废，双方
						case INVALID -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.INVALID.getCode()));
					}
				}
			});
		} else {
			queryWrapper
					.and(x -> x
							.in(BillPayment::getState,
									BillPaymentDef.State.CONFIRMING.getCode(),
									BillPaymentDef.State.PENDING_CONFIRMATION
											.getCode(),
									BillPaymentDef.State.COMPLETED.getCode(),
									BillPaymentDef.State.INVALIDING.getCode(),
									BillPaymentDef.State.INVALID.getCode())
							.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.in(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode(),
											BillPaymentDef.State.REJECTED
													.getCode())));
		}

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last(
					"order by " + sortKey + " " + sortOrder + ", id DESC");
		} else {
			queryWrapper.orderByDesc(BillPayment::getUpdatedTime,
					BillPayment::getId);
		}
		Page<BillPayment> paging = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Page<BillPaymentVo> customerBuyPaging(Integer page, Integer size,
			String keyword, String name, LocalDateTime startTime,
			LocalDateTime endTime, String goodsName, List<Integer> states,
			String sortKey, String sortOrder) {
		LambdaQueryWrapper<BillPayment> queryWrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		queryWrapper.eq(BillPayment::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(BillPayment::getType,
				BillPaymentDef.Type.BUY.getCode());
		queryWrapper.eq(StringUtils.isNotBlank(goodsName),
				BillPayment::getGoodsName, goodsName);
		queryWrapper.eq(BillPayment::getSellerId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		if (StringUtils.isNotBlank(keyword)) {
			queryWrapper.and(i -> i.like(BillPayment::getContractName, keyword)
					.or().like(BillPayment::getId, keyword));
		}

		if (StringUtils.isNotBlank(name)) {
			queryWrapper.and(i -> i.apply(StringUtils.isNoneBlank(name),
					"(JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
					name));
		}

		queryWrapper.ge(Objects.nonNull(startTime), BillPayment::getBillDate,
				startTime);
		queryWrapper.le(Objects.nonNull(endTime), BillPayment::getBillDate,
				endTime);

		if (CollectionUtils.isNotEmpty(states)) {
			queryWrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					switch (BillPaymentDef.State.from(state)) {
						// 草稿，发起方是自己
						case DRAFT ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode()));

						// 待确认，发起方是对方
						case PENDING_CONFIRMATION ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

						// 确认中，发起方是自己
						case CONFIRMING ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

						// 已驳回，发起方是自己
						case REJECTED ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.REJECTED
													.getCode()));

						// 已完成，双方
						case COMPLETED -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.COMPLETED.getCode()));

						// 作废中，双方
						case INVALIDING -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.INVALIDING.getCode()));

						// 已作废，双方
						case INVALID -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.INVALID.getCode()));
					}
				}
			});
		} else {
			queryWrapper
					.and(x -> x
							.in(BillPayment::getState,
									BillPaymentDef.State.CONFIRMING.getCode(),
									BillPaymentDef.State.PENDING_CONFIRMATION
											.getCode(),
									BillPaymentDef.State.COMPLETED.getCode(),
									BillPaymentDef.State.INVALIDING.getCode(),
									BillPaymentDef.State.INVALID.getCode())
							.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.in(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode(),
											BillPaymentDef.State.REJECTED
													.getCode())));
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last(
					"order by " + sortKey + " " + sortOrder + ", id DESC");
		} else {
			queryWrapper.orderByDesc(BillPayment::getUpdatedTime,
					BillPayment::getId);
		}
		Page<BillPayment> paging = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Page<BillPaymentVo> customerSalePaging(Integer page, Integer size,
			String keyword, LocalDateTime startTime, LocalDateTime endTime,
			String goodsName, List<Integer> states, String sortKey,
			String sortOrder) {
		LambdaQueryWrapper<BillPayment> queryWrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		queryWrapper.eq(BillPayment::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(BillPayment::getType,
				BillPaymentDef.Type.SELL.getCode());
		queryWrapper.eq(StringUtils.isNotBlank(goodsName),
				BillPayment::getGoodsName, goodsName);
		queryWrapper.eq(BillPayment::getPurchaserId, CustomerContextHolder
				.getCustomerLoginVo().getProxyAccount().getId());
		if (StringUtils.isNotBlank(keyword)) {
			queryWrapper.and(i -> i.like(BillPayment::getProjectName, keyword)
					.or().like(BillPayment::getId, keyword));
		}

		queryWrapper.ge(Objects.nonNull(startTime), BillPayment::getBillDate,
				startTime);
		queryWrapper.le(Objects.nonNull(endTime), BillPayment::getBillDate,
				endTime);

		if (CollectionUtils.isNotEmpty(states)) {
			queryWrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					switch (BillPaymentDef.State.from(state)) {
						// 草稿，发起方是自己
						case DRAFT ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode()));

						// 待确认，发起方是对方
						case PENDING_CONFIRMATION ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

						// 确认中，发起方是自己
						case CONFIRMING ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

						// 已驳回，发起方是自己
						case REJECTED ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.REJECTED
													.getCode()));

						// 已完成，双方
						case COMPLETED -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.COMPLETED.getCode()));

						// 作废中，双方
						case INVALIDING -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.INVALIDING.getCode()));

						// 已作废，双方
						case INVALID -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.INVALID.getCode()));
					}
				}
			});
		} else {
			queryWrapper
					.and(x -> x
							.in(BillPayment::getState,
									BillPaymentDef.State.CONFIRMING.getCode(),
									BillPaymentDef.State.PENDING_CONFIRMATION
											.getCode(),
									BillPaymentDef.State.COMPLETED.getCode(),
									BillPaymentDef.State.INVALIDING.getCode(),
									BillPaymentDef.State.INVALID.getCode())
							.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.in(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode(),
											BillPaymentDef.State.REJECTED
													.getCode())));
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last(
					"order by " + sortKey + " " + sortOrder + ", id DESC");
		} else {
			queryWrapper.orderByDesc(BillPayment::getUpdatedTime,
					BillPayment::getId);
		}
		Page<BillPayment> paging = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Page<BillPaymentVo> buyDetailsPaging(Integer page, Integer size,
			String projectId, String keyword, String name, List<Integer> states,
			LocalDateTime startTime, LocalDateTime endTime, String sortKey,
			String sortOrder) {
		LambdaQueryWrapper<BillPayment> queryWrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		queryWrapper.eq(BillPayment::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Objects.nonNull(projectId), BillPayment::getProjectId,
				projectId);
		// 开票编号或合同名称
		if (StringUtils.isNotBlank(keyword)) {
			List<String> contractIds = contractService.findByNameLike(keyword)
					.stream().map(Contract::getId).distinct().toList();
			queryWrapper.and(x -> x.like(BillPayment::getId, keyword).or()
					.in(com.baomidou.mybatisplus.core.toolkit.CollectionUtils
							.isNotEmpty(contractIds),
							BillPayment::getContractId, contractIds));
		}
		if (StringUtils.isNotBlank(name)) {
			queryWrapper.apply(
					"(JSON_EXTRACT(seller_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
					name);
		}
		queryWrapper.eq(BillPayment::getType,
				BillPaymentDef.Type.BUY.getCode());

		if (CollectionUtils.isNotEmpty(states)) {
			queryWrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					switch (BillPaymentDef.State.from(state)) {
						// 草稿，发起方是自己
						case DRAFT ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode()));

						// 待确认，发起方是对方
						case PENDING_CONFIRMATION ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

						// 确认中，发起方是自己
						case CONFIRMING ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

						// 已驳回，发起方是自己
						case REJECTED ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.REJECTED
													.getCode()));

						// 已完成，双方
						case COMPLETED -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.COMPLETED.getCode()));

						// 作废中，双方
						case INVALIDING -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.INVALIDING.getCode()));

						// 已作废，双方
						case INVALID -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.INVALID.getCode()));
					}
				}
			});
		} else {
			queryWrapper
					.and(x -> x
							.in(BillPayment::getState,
									BillPaymentDef.State.CONFIRMING.getCode(),
									BillPaymentDef.State.PENDING_CONFIRMATION
											.getCode(),
									BillPaymentDef.State.COMPLETED.getCode(),
									BillPaymentDef.State.INVALIDING.getCode(),
									BillPaymentDef.State.INVALID.getCode())
							.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.in(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode(),
											BillPaymentDef.State.REJECTED
													.getCode())));
		}

		queryWrapper.ge(Objects.nonNull(startTime), BillPayment::getBillDate,
				startTime);
		queryWrapper.le(Objects.nonNull(endTime), BillPayment::getBillDate,
				endTime);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 默认按照创建时间降序排列
			queryWrapper.orderByDesc(BillPayment::getCreatedTime);
		}
		Page<BillPayment> paging = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Page<BillPaymentVo> saleDetailsPaging(Integer page, Integer size,
			String projectId, String keyword, String name,
			LocalDateTime startTime, LocalDateTime endTime,
			List<Integer> states, String sortKey, String sortOrder) {
		LambdaQueryWrapper<BillPayment> queryWrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		queryWrapper.eq(BillPayment::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Objects.nonNull(projectId), BillPayment::getProjectId,
				projectId);
		// 开票编号或合同名称
		if (StringUtils.isNotBlank(keyword)) {
			List<String> contractIds = contractService.findByNameLike(keyword)
					.stream().map(Contract::getId).distinct().toList();
			queryWrapper.and(x -> x.like(BillPayment::getId, keyword).or()
					.in(com.baomidou.mybatisplus.core.toolkit.CollectionUtils
							.isNotEmpty(contractIds),
							BillPayment::getContractId, contractIds));
		}
		if (StringUtils.isNotBlank(name)) {
			queryWrapper.apply(
					"(JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
					name);
		}
		if (CollectionUtils.isNotEmpty(states)) {
			queryWrapper.and(x -> {
				// 判断状态，并根据不同状态添加查询条件
				for (Integer state : states) {
					switch (BillPaymentDef.State.from(state)) {
						// 草稿，发起方是自己
						case DRAFT ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode()));

						// 待确认，发起方是对方
						case PENDING_CONFIRMATION ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

						// 确认中，发起方是自己
						case CONFIRMING ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

						// 已驳回，发起方是自己
						case REJECTED ->
							x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.REJECTED
													.getCode()));

						// 已完成，双方
						case COMPLETED -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.COMPLETED.getCode()));

						// 作废中，双方
						case INVALIDING -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.INVALIDING.getCode()));

						// 已作废，双方
						case INVALID -> x.or(y -> y.eq(BillPayment::getState,
								BillPaymentDef.State.INVALID.getCode()));
					}
				}
			});
		} else {
			queryWrapper
					.and(x -> x
							.in(BillPayment::getState,
									BillPaymentDef.State.CONFIRMING.getCode(),
									BillPaymentDef.State.PENDING_CONFIRMATION
											.getCode(),
									BillPaymentDef.State.COMPLETED.getCode(),
									BillPaymentDef.State.INVALIDING.getCode(),
									BillPaymentDef.State.INVALID.getCode())
							.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.in(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode(),
											BillPaymentDef.State.REJECTED
													.getCode())));
		}
		queryWrapper.eq(BillPayment::getType,
				BillPaymentDef.Type.SELL.getCode());
		queryWrapper.ge(Objects.nonNull(startTime), BillPayment::getBillDate,
				startTime);
		queryWrapper.le(Objects.nonNull(endTime), BillPayment::getBillDate,
				endTime);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 默认按照创建时间降序排列
			queryWrapper.orderByDesc(BillPayment::getCreatedTime);
		}
		Page<BillPayment> paging = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public Page<BillPaymentVo> detailsPaging(Integer page, Integer size,
			String projectId, LocalDateTime startTime, LocalDateTime endTime,
			String sortKey, String sortOrder, Integer type) {
		LambdaQueryWrapper<BillPayment> queryWrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		queryWrapper.eq(BillPayment::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Objects.nonNull(projectId), BillPayment::getProjectId,
				projectId);
		queryWrapper.eq(Objects.nonNull(type), BillPayment::getType, type);
		queryWrapper.ge(Objects.nonNull(startTime), BillPayment::getBillDate,
				startTime);
		queryWrapper.le(Objects.nonNull(endTime), BillPayment::getBillDate,
				endTime);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 默认按照创建时间降序排列
			queryWrapper.orderByDesc(BillPayment::getCreatedTime);
		}
		Page<BillPayment> paging = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}

	@Override
	public List<BillPayment> findByContractId(String contractId) {
		LambdaQueryWrapper<BillPayment> wrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		this.filterDeleted(wrapper);
		wrapper.eq(BillPayment::getContractId, contractId);
		return repository.selectList(wrapper);
	}

	@Override
	public List<BillPayment> findByReconciliationIds(
			List<String> reconciliationIds) {
		if (CollectionUtils.isEmpty(reconciliationIds)) {
			return List.of();
		}
		LambdaQueryWrapper<BillPayment> queryWrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		queryWrapper.eq(BillPayment::getDel, CommonDef.Symbol.NO.getCode());

		// 动态添加 JSON_CONTAINS 条件
		if (CollectionUtils.isNotEmpty(reconciliationIds)) {
			String conditions = reconciliationIds.stream()
					.map(id -> "JSON_CONTAINS(reconciliation_ids, '{\"id\": \""
							+ id + "\"}', '$')")
					.collect(Collectors.joining(" OR "));
			queryWrapper.apply(conditions);
		}

		return repository.selectList(queryWrapper);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public BillPayment create(BillPayment billPayment) {
		this.amountBack(billPayment.getReconciliationIds(),
				CommonDef.Symbol.NO.getCode(), billPayment.getType());
		projectService.findOne(billPayment.getProjectId())
				.ifPresent(project -> contractService
						.findOne(billPayment.getContractId())
						.ifPresent(contract -> {
							billPayment.setContractName(contract.getName());
							// 按规则设置开票单id
							billPayment.setId(AutoCodeDef.DEAL_CREATE_AUTO_CODE
									.apply(redisClient, project.getCode(),
											RedisKeys.Cache.PURCHASE_INVOICE_CODE_GENERATOR,
											ContractDef.Type
													.from(contract
															.getContractType())
													.getStr()
													+ AutoCodeDef.BusinessRuleCode.INVOICE_SUFFIX
															.getCode(),
											4, AutoCodeDef.DATE_TYPE.yy));
							billPayment.setProjectName(project.getName());
							billPayment.setGoodsName(project.getGoodsName());
							// 采购合同
							if (ContractDef.ContractType.PURCHASE
									.match(contract.getContractType())) {
								billPayment.setPurchaserId(
										contract.getSupplierChainId());
								billPayment.setPurchaserEnterprise(
										contract.getSupplierChainEnterprise());
								billPayment.setSellerId(
										contract.getUpstreamSuppliersId());
								billPayment.setSellerInputId(
										contract.getUpstreamId());
								billPayment.setSellerEnterprise(contract
										.getUpstreamSuppliersEnterprise());
							} else {
								// 销售合同
								billPayment.setSellerId(
										contract.getSupplierChainId());
								billPayment.setSellerEnterprise(
										contract.getSupplierChainEnterprise());
								billPayment.setPurchaserId(
										contract.getDownstreamPurchasersId());
								billPayment.setPurchaserInputId(
										contract.getDownstreamId());
								billPayment.setPurchaserEnterprise(contract
										.getDownstreamPurchasersEnterprise());
							}
							List<String> reconciliationIds = billPayment
									.getReconciliationIds().stream()
									.map(ReconciliationJsonInfo::getId)
									.toList();
							if (CollectionUtils.isNotEmpty(reconciliationIds)) {
								List<Reconciliation> reconciliationList = reconciliationService
										.findByIds(reconciliationIds);
								BigDecimal recAmount = BigDecimal.ZERO;
								if (CollectionUtils
										.isNotEmpty(reconciliationList)) {
									for (Reconciliation reconciliation : reconciliationList) {
										recAmount = recAmount.add(reconciliation
												.getReconciliationAmount());
									}
								}
								billPayment.setRecAmount(recAmount);
							}
						}));
		if (BillPaymentDef.BillType.SELL.match(billPayment.getBillType())
				&& BillPaymentDef.State.CONFIRMING
						.match(billPayment.getState())) {
			this.sendNotice(billPayment,
					wxSubscriptionProperties.getUnConfirmBillCode(),
					MessageFormat.format(
							UserMessageConstants.BILL_PAYMENT_UNCONFIRMED_TEMPLATE,
							billPayment.getId()),
					BillPaymentDef.Type.SELL.getCode());
		}
		return super.create(billPayment);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public BillPayment update(BillPayment resource) {
		// 根据id查找对账单
		BillPayment billPayment = super.findOne(resource.getId()).orElse(null);
		assert billPayment != null;
		// 数据库中关联的对账单
		ArrayReconciliationJsonInfo reconciliations = billPayment
				.getReconciliationIds();
		// 修改的关联的对账单
		ArrayReconciliationJsonInfo reconciliationIds = resource
				.getReconciliationIds();

		// 将数据库中关联的对账单转换为map，方便后续操作
		Map<String, ReconciliationJsonInfo> reconciliationsMap = reconciliations
				.stream()
				.collect(Collectors.toMap(ReconciliationJsonInfo::getId,
						reconciliationJsonInfo -> reconciliationJsonInfo));

		// 将修改的关联的对账单转换为map，方便后续操作
		Map<String, ReconciliationJsonInfo> reconciliationIdsMap = reconciliationIds
				.stream()
				.collect(Collectors.toMap(ReconciliationJsonInfo::getId,
						reconciliationJsonInfo -> reconciliationJsonInfo));

		// 删除的对账单ids
		ArrayReconciliationJsonInfo deleteList = BillPaymentUtils
				.filterReconciliation(reconciliations, reconciliationIds);
		if (CollectionUtils.isNotEmpty(deleteList)) {
			// 对账单金额回退
			this.amountBack(deleteList, CommonDef.Symbol.YES.getCode(),
					billPayment.getType());
		}

		// 新增的对账单ids
		ArrayReconciliationJsonInfo createList = BillPaymentUtils
				.filterReconciliation(reconciliationIds, reconciliations);
		if (CollectionUtils.isNotEmpty(createList)) {
			// 对账单金额回退
			this.amountBack(createList, CommonDef.Symbol.NO.getCode(),
					billPayment.getType());
		}

		// 修改的对账单ids
		List<String> updateIds = reconciliations.stream()
				.map(ReconciliationJsonInfo::getId)
				.filter(id -> reconciliationIds.stream()
						.map(ReconciliationJsonInfo::getId).toList()
						.contains(id))
				.toList();
		List<Reconciliation> reconciliationList = reconciliationService
				.findByIds(updateIds);
		if (CollectionUtils.isNotEmpty(reconciliationList)) {
			// 更新对账单金额
			reconciliationList.forEach(reconciliation -> reconciliation
					.setUnbilledAmount(reconciliation.getUnbilledAmount()
							.add(reconciliationsMap.get(reconciliation.getId())
									.getBillAmount()
									.subtract(reconciliationIdsMap
											.get(reconciliation.getId())
											.getBillAmount()))));
			// 批量更新对账单
			reconciliationService.batchUpdate(reconciliationList);
		}
		if (BillPaymentDef.BillType.SELL.match(resource.getBillType())
				&& BillPaymentDef.State.CONFIRMING.match(resource.getState())) {
			this.sendNotice(billPayment,
					wxSubscriptionProperties.getUnConfirmBillCode(),
					MessageFormat.format(
							UserMessageConstants.BILL_PAYMENT_UNCONFIRMED_TEMPLATE,
							resource.getId()),
					BillPaymentDef.Type.SELL.getCode());
		}
		// 更新开票单
		List<String> reconciliationId = resource.getReconciliationIds().stream()
				.map(ReconciliationJsonInfo::getId).toList();
		if (CollectionUtils.isNotEmpty(reconciliationId)) {
			List<Reconciliation> reconciliationLists = reconciliationService
					.findByIds(reconciliationId);
			BigDecimal recAmount = BigDecimal.ZERO;
			if (CollectionUtils.isNotEmpty(reconciliationLists)) {
				for (Reconciliation reconciliation : reconciliationLists) {
					recAmount = recAmount
							.add(reconciliation.getReconciliationAmount());
				}
			}
			billPayment.setRecAmount(recAmount);
		}
		return super.updateAllProperties(resource);
	}

	@Override
	public BillPayment updateAllProperties(BillPayment resource) {
		if (BillPaymentDef.State.CONFIRMING.match(resource.getState())) {
			if (BillPaymentDef.Type.SELL.match(resource.getType())) {
				if (BillPaymentDef.BillType.SELL
						.match(resource.getBillType())) {
					this.sendNotice(resource,
							wxSubscriptionProperties.getUnConfirmBillCode(),
							MessageFormat.format(
									UserMessageConstants.BILL_PAYMENT_UNCONFIRMED_TEMPLATE,
									resource.getId()),
							BillPaymentDef.Type.SELL.getCode());
				} else {
					SpringUtil.getBean(BillPaymentService.class)
							.notice(resource, 1);
				}
			} else {
				if (BillPaymentDef.BillType.SELL
						.match(resource.getBillType())) {
					SpringUtil.getBean(BillPaymentService.class)
							.notice(resource, 1);
				} else {
					this.sendNotice(resource,
							wxSubscriptionProperties.getUnConfirmBillCode(),
							MessageFormat.format(
									UserMessageConstants.BILL_PAYMENT_UNCONFIRMED_TEMPLATE,
									resource.getId()),
							BillPaymentDef.Type.BUY.getCode());
				}
			}
		}

		return super.updateAllProperties(resource);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void delete(String id) {
		super.findOne(id).ifPresent(billPayment -> this.amountBack(
				billPayment.getReconciliationIds(),
				CommonDef.Symbol.YES.getCode(), billPayment.getType()));
		super.delete(id);
	}

	@Override
	public Optional<BillPaymentVo> findVoById(String id) {
		return super.findOne(id).map(billPayment -> {
			BillPaymentVo billPaymentVo = new BillPaymentVo();
			List<String> list = billPayment.getReconciliationIds().stream()
					.map(ReconciliationJsonInfo::getId).toList();
			List<Reconciliation> reconciliationList = reconciliationService
					.findByIds(list);
			billPaymentVo.setBillPayment(billPayment);
			billPaymentVo.setReconciliationList(reconciliationList);
			billPaymentVo.setContract(contractService
					.findOne(billPayment.getContractId()).orElse(null));
			billPaymentVo.setProject(projectService
					.findOne(billPayment.getProjectId()).orElse(null));
			return billPaymentVo;
		});
	}

	@Override
	public List<BillPayment> findProjectIdByState(String projectId,
			Integer type, Integer state, LocalDateTime beginTime,
			LocalDateTime endTime) {
		LambdaQueryWrapper<BillPayment> wrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(type), BillPayment::getType, type);
		wrapper.eq(Objects.nonNull(projectId), BillPayment::getProjectId,
				projectId);
		wrapper.eq(Objects.nonNull(state), BillPayment::getState, state);
		wrapper.ge(Objects.nonNull(beginTime), BillPayment::getBillDate,
				beginTime);
		wrapper.le(Objects.nonNull(endTime), BillPayment::getBillDate, endTime);
		return repository.selectList(wrapper);
	}

	@Override
	public List<BillPayment> findUnfinished(String projectId) {
		LambdaQueryWrapper<BillPayment> queryWrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		queryWrapper.eq(BillPayment::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(BillPayment::getProjectId, projectId);
		queryWrapper.ne(BillPayment::getState,
				BillPaymentDef.State.COMPLETED.getCode());
		return repository.selectList(queryWrapper);
	}

	@Override
	public Optional<BillPayment> invalidOffLine(BillPayment billPayment,
			Integer initiator) {
		// 作废合同
		billPayment.setState(BillPaymentDef.State.INVALIDING.getCode());
		billPayment.setInvalidInitiator(initiator);
		billPayment.setInvalidRevokeReason(null);
		billPayment.setInvalidRevokeTime(null);
		billPayment.setPurchaseInvalidTime(null);
		billPayment.setSellerInvalidTime(null);
		this.amountBack(billPayment.getReconciliationIds(),
				CommonDef.Symbol.YES.getCode(), billPayment.getType());
		if (BillPaymentDef.Type.SELL.match(billPayment.getType())) {
			if (CommonDef.UserType.INNER.match(initiator)) {
				billPayment.setInvalidSignState(
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
				billPayment.setSellerInvalidTime(LocalDateTime.now());
				this.sendNotice(billPayment,
						wxSubscriptionProperties
								.getBillPaymentNullifyConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.BILL_PAYMENT_INVALID_TEMPLATE,
								billPayment.getId()),
						BillPaymentDef.Type.SELL.getCode());
			} else {
				billPayment.setInvalidSignState(
						BusinessContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				billPayment.setPurchaseInvalidTime(LocalDateTime.now());
				SpringUtil.getBean(BillPaymentService.class).notice(billPayment,
						4);
			}
		} else {
			if (CommonDef.UserType.INNER.match(initiator)) {
				billPayment.setInvalidSignState(
						BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
				billPayment.setSellerInvalidTime(LocalDateTime.now());
				SpringUtil.getBean(BillPaymentService.class).notice(billPayment,
						4);
			} else {
				billPayment.setInvalidSignState(
						BusinessContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				billPayment.setPurchaseInvalidTime(LocalDateTime.now());
				this.sendNotice(billPayment,
						wxSubscriptionProperties
								.getBillPaymentNullifyConfirmCode(),
						MessageFormat.format(
								UserMessageConstants.BILL_PAYMENT_INVALID_TEMPLATE,
								billPayment.getId()),
						BillPaymentDef.Type.BUY.getCode());
			}
		}
		// 处理对账的已开票金额
		// 该开票关联的所有对账单id
		List<String> reconciliationIds = billPayment.getReconciliationIds()
				.stream().map(ReconciliationJsonInfo::getId).toList();
		// 对账单id-已完成开票金额 map
		Map<String, BigDecimal> recInfoMap = new HashMap<>();
		for (ReconciliationJsonInfo reconciliationInfo : billPayment
				.getReconciliationIds()) {
			recInfoMap.put(reconciliationInfo.getId(),
					reconciliationInfo.getBillAmount());
		}
		// 对账单列表
		List<Reconciliation> reconciliationList = reconciliationService
				.findByIds(reconciliationIds);
		// 循环关联的对账单列表，设置已完成开票金额
		for (Reconciliation reconciliation : reconciliationList) {
			BigDecimal billedAmount = reconciliation.getBilledAmount();
			// 已开票金额为空时设置为0
			if (Objects.isNull(billedAmount)) {
				billedAmount = BigDecimal.ZERO;
			}
			// 减去该开票的金额
			reconciliation.setBilledAmount(billedAmount
					.subtract(recInfoMap.get(reconciliation.getId())));
		}
		reconciliationService.batchUpdate(reconciliationList);
		return Optional.of(super.updateAllProperties(billPayment));
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Optional<BillPayment> confirmInvalid(BillPayment billPayment) {
		// 确认作废合同
		billPayment.setState(BillPaymentDef.State.INVALID.getCode());
		billPayment.setInvalidSignState(
				BusinessContractDef.CommonSignState.COMPLETED.getCode());
		if (CommonDef.UserType.INNER.match(billPayment.getInvalidInitiator())) {
			billPayment.setPurchaseInvalidTime(LocalDateTime.now());
		} else {
			billPayment.setSellerInvalidTime(LocalDateTime.now());
		}
		return Optional.of(super.updateAllProperties(billPayment));
	}

	@Override
	public Optional<BillPayment> revertInvalid(BillPayment billPayment) {
		// 撤销作废合同
		billPayment.setState(BillPaymentDef.State.COMPLETED.getCode());
		billPayment.setInvalidSignState(null);
		billPayment.setInvalidRevokeTime(LocalDateTime.now());
		if (BillPaymentDef.Type.SELL.match(billPayment.getType())) {
			if (CommonDef.UserType.INNER
					.match(billPayment.getInvalidInitiator())) {
				SpringUtil.getBean(BillPaymentService.class).notice(billPayment,
						5);
			} else {
				this.sendNotice(billPayment,
						wxSubscriptionProperties
								.getBillPaymentNullifyDismissCode(),
						MessageFormat.format(
								UserMessageConstants.BILL_PAYMENT_INVALID_DISMISS_TEMPLATE,
								billPayment.getId()),
						BillPaymentDef.Type.SELL.getCode());
			}
		} else {
			if (CommonDef.UserType.INNER
					.match(billPayment.getInvalidInitiator())) {
				this.sendNotice(billPayment,
						wxSubscriptionProperties
								.getBillPaymentNullifyDismissCode(),
						MessageFormat.format(
								UserMessageConstants.BILL_PAYMENT_INVALID_DISMISS_TEMPLATE,
								billPayment.getId()),
						BillPaymentDef.Type.BUY.getCode());
			} else {
				SpringUtil.getBean(BillPaymentService.class).notice(billPayment,
						5);
			}
		}
		this.amountBack(billPayment.getReconciliationIds(),
				CommonDef.Symbol.NO.getCode(), billPayment.getType());
		// 处理对账的已开票金额
		// 该开票关联的所有对账单id
		List<String> reconciliationIds = billPayment.getReconciliationIds()
				.stream().map(ReconciliationJsonInfo::getId).toList();
		// 对账单id-已完成开票金额 map
		Map<String, BigDecimal> recInfoMap = new HashMap<>();
		for (ReconciliationJsonInfo reconciliationInfo : billPayment
				.getReconciliationIds()) {
			recInfoMap.put(reconciliationInfo.getId(),
					reconciliationInfo.getBillAmount());
		}
		// 对账单列表
		List<Reconciliation> reconciliationList = reconciliationService
				.findByIds(reconciliationIds);
		// 循环关联的对账单列表，设置已完成开票金额
		for (Reconciliation reconciliation : reconciliationList) {
			BigDecimal billedAmount = reconciliation.getBilledAmount();
			// 已开票金额为空时设置为0
			if (Objects.isNull(billedAmount)) {
				billedAmount = BigDecimal.ZERO;
			}
			// 加上该开票的金额
			reconciliation.setBilledAmount(
					billedAmount.add(recInfoMap.get(reconciliation.getId())));
		}
		reconciliationService.batchUpdate(reconciliationList);
		return Optional.of(super.updateAllProperties(billPayment));
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Optional<BillPayment> confirm(BillPayment billPayment,
			Long billFileId) {
		billPayment.setState(BillPaymentDef.State.COMPLETED.getCode());
		if (Objects.nonNull(billFileId)) {
			billPayment.setBillFileId(billFileId);
		}
		BillPayment payment = super.updateAllProperties(billPayment);
		List<String> reconciliationIds = billPayment.getReconciliationIds()
				.stream().map(ReconciliationJsonInfo::getId).toList();
		// 对账单id-已完成开票金额 map
		Map<String, BigDecimal> recInfoMap = new HashMap<>();
		for (ReconciliationJsonInfo reconciliationInfo : billPayment
				.getReconciliationIds()) {
			recInfoMap.put(reconciliationInfo.getId(),
					reconciliationInfo.getBillAmount());
		}
		// 对账单列表
		List<Reconciliation> reconciliationList = reconciliationService
				.findByIds(reconciliationIds);
		// 循环关联的对账单列表，设置已完成开票金额
		for (Reconciliation reconciliation : reconciliationList) {
			BigDecimal billedAmount = reconciliation.getBilledAmount();
			// 已开票金额为空时设置为0
			if (Objects.isNull(billedAmount)) {
				billedAmount = BigDecimal.ZERO;
			}
			reconciliation.setBilledAmount(
					billedAmount.add(recInfoMap.get(reconciliation.getId())));
		}
		if (CollectionUtils.isNotEmpty(reconciliationList)) {
			// 查询对账单关联的其他开票单
			List<BillPayment> billPaymentList = this
					.findByReconciliationIds(reconciliationIds);
			Map<String, List<BillPayment>> map = new HashMap<>();
			for (BillPayment billPayment1 : billPaymentList) {
				for (ReconciliationJsonInfo reconciliationInfo : billPayment1
						.getReconciliationIds()) {
					map.computeIfAbsent(reconciliationInfo.getId(),
							k -> new ArrayList<>()).add(billPayment1);
				}
			}
			reconciliationList.forEach(reconciliation -> {
				if (map.get(reconciliation.getId()).stream()
						.allMatch(bill -> BillPaymentDef.State.COMPLETED
								.match(bill.getState())
								&& reconciliation.getUnbilledAmount()
										.compareTo(BigDecimal.ZERO) == 0)) {
					reconciliation.setInvoiceState(
							ReconciliationDef.InvoiceState.FINISHED.getCode());
				}
			});

			// 更新为已开票的对账单
			reconciliationService.batchUpdate(reconciliationList);
		}

		if (BillPaymentDef.Type.SELL.match(billPayment.getType())) {
			if (BillPaymentDef.BillType.BUY.match(billPayment.getBillType())) {
				this.sendNotice(billPayment,
						wxSubscriptionProperties.getConfirmBillCode(),
						MessageFormat.format(
								UserMessageConstants.BILL_PAYMENT_CONFIRMED_TEMPLATE,
								billPayment.getId()),
						BillPaymentDef.Type.SELL.getCode());
			} else {
				SpringUtil.getBean(BillPaymentService.class).notice(billPayment,
						2);
			}
		} else {
			if (BillPaymentDef.BillType.BUY.match(billPayment.getBillType())) {
				SpringUtil.getBean(BillPaymentService.class).notice(billPayment,
						2);
			} else {
				this.sendNotice(billPayment,
						wxSubscriptionProperties.getConfirmBillCode(),
						MessageFormat.format(
								UserMessageConstants.BILL_PAYMENT_CONFIRMED_TEMPLATE,
								billPayment.getId()),
						BillPaymentDef.Type.BUY.getCode());
			}
		}
		return Optional.ofNullable(payment);
	}

	@Override
	public Optional<BillPayment> dismiss(BillPayment billPayment) {
		billPayment.setState(BillPaymentDef.State.REJECTED.getCode());
		if (BillPaymentDef.Type.SELL.match(billPayment.getType())) {
			if (BillPaymentDef.BillType.BUY.match(billPayment.getBillType())) {
				this.sendNotice(billPayment,
						wxSubscriptionProperties.getDismissBillCode(),
						MessageFormat.format(
								UserMessageConstants.BILL_PAYMENT_DISMISS_TEMPLATE,
								billPayment.getId()),
						BillPaymentDef.Type.SELL.getCode());
			} else {
				SpringUtil.getBean(BillPaymentService.class).notice(billPayment,
						3);
			}
		} else {
			if (BillPaymentDef.BillType.BUY.match(billPayment.getBillType())) {
				SpringUtil.getBean(BillPaymentService.class).notice(billPayment,
						3);
			} else {
				this.sendNotice(billPayment,
						wxSubscriptionProperties.getDismissBillCode(),
						MessageFormat.format(
								UserMessageConstants.BILL_PAYMENT_DISMISS_TEMPLATE,
								billPayment.getId()),
						BillPaymentDef.Type.BUY.getCode());
			}
		}
		return Optional.ofNullable(super.updateAllProperties(billPayment));
	}

	@Override
	public Optional<BillPaymentCountVo> staticsAdminBillPayment(
			boolean isManage) {
		BillPaymentCountVo billPaymentCountVo = new BillPaymentCountVo();
		billPaymentCountVo.setWaitConfirm(0L);
		billPaymentCountVo.setReject(0L);
		billPaymentCountVo.setInvaliding(0L);
		billPaymentCountVo.setPurchaseToBeConfirmed(0L);
		billPaymentCountVo.setPurchaseRejected(0L);
		billPaymentCountVo.setPurchaseInvaliding(0L);
		billPaymentCountVo.setSaleToBeConfirmed(0L);
		billPaymentCountVo.setSaleRejected(0L);
		billPaymentCountVo.setSaleInvaliding(0L);
		List<String> projectIds = projectService.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		LambdaQueryWrapper<BillPayment> queryWrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		if (isManage) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				// 统计销售待确认
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(BillPayment::getType,
						BillPaymentDef.Type.SELL.getCode());
				queryWrapper.and(x -> x
						.eq(BillPayment::getBillType,
								BillPaymentDef.BillType.BUY.getCode())
						.eq(BillPayment::getState,
								BillPaymentDef.State.CONFIRMING.getCode()));
				queryWrapper.in(BillPayment::getProjectId, projectIds);
				billPaymentCountVo.setSaleToBeConfirmed(
						repository.selectCount(queryWrapper));

				// 统计销售已驳回
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(BillPayment::getType,
						BillPaymentDef.Type.SELL.getCode());
				queryWrapper.and(x -> x
						.eq(BillPayment::getBillType,
								BillPaymentDef.BillType.SELL.getCode())
						.eq(BillPayment::getState,
								BillPaymentDef.State.REJECTED.getCode()));
				queryWrapper.in(BillPayment::getProjectId, projectIds);
				billPaymentCountVo
						.setSaleRejected(repository.selectCount(queryWrapper));

				// 统计销售作废中
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(BillPayment::getType,
						BillPaymentDef.Type.SELL.getCode());
				queryWrapper.eq(BillPayment::getState,
						BillPaymentDef.State.INVALIDING.getCode());
				queryWrapper.in(BillPayment::getProjectId, projectIds);
				billPaymentCountVo.setSaleInvaliding(
						repository.selectCount(queryWrapper));

				// 统计采购待确认
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(BillPayment::getType,
						BillPaymentDef.Type.BUY.getCode());
				queryWrapper.and(x -> x
						.eq(BillPayment::getBillType,
								BillPaymentDef.BillType.SELL.getCode())
						.eq(BillPayment::getState,
								BillPaymentDef.State.CONFIRMING.getCode()));
				queryWrapper.in(BillPayment::getProjectId, projectIds);
				billPaymentCountVo.setPurchaseToBeConfirmed(
						repository.selectCount(queryWrapper));

				// 统计采购已驳回
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(BillPayment::getType,
						BillPaymentDef.Type.BUY.getCode());
				queryWrapper.and(x -> x
						.eq(BillPayment::getBillType,
								BillPaymentDef.BillType.BUY.getCode())
						.eq(BillPayment::getState,
								BillPaymentDef.State.REJECTED.getCode()));
				queryWrapper.in(BillPayment::getProjectId, projectIds);
				billPaymentCountVo.setPurchaseRejected(
						repository.selectCount(queryWrapper));

				// 统计采购作废中
				queryWrapper.clear();
				this.filterDeleted(queryWrapper);
				queryWrapper.eq(BillPayment::getType,
						BillPaymentDef.Type.BUY.getCode());
				queryWrapper.eq(BillPayment::getState,
						BillPaymentDef.State.INVALIDING.getCode());
				queryWrapper.in(BillPayment::getProjectId, projectIds);
				billPaymentCountVo.setPurchaseInvaliding(
						repository.selectCount(queryWrapper));
			}
		}
		return Optional.of(billPaymentCountVo);
	}

	@Override
	public Optional<BillPaymentCountVo> staticsCustomerBillPayment(
			boolean isPermission) {
		BillPaymentCountVo billPaymentCountVo = new BillPaymentCountVo();
		billPaymentCountVo.setWaitConfirm(0L);
		billPaymentCountVo.setReject(0L);
		billPaymentCountVo.setInvaliding(0L);
		billPaymentCountVo.setPurchaseToBeConfirmed(0L);
		billPaymentCountVo.setPurchaseRejected(0L);
		billPaymentCountVo.setPurchaseInvaliding(0L);
		billPaymentCountVo.setSaleToBeConfirmed(0L);
		billPaymentCountVo.setSaleRejected(0L);
		billPaymentCountVo.setSaleInvaliding(0L);
		LambdaQueryWrapper<BillPayment> queryWrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		Long customerId = CustomerContextHolder.getCustomerLoginVo()
				.getProxyAccount().getId();
		if (isPermission) {
			// 统计开票申请待确认
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.and(x -> x
					.eq(BillPayment::getBillType,
							BillPaymentDef.BillType.SELL.getCode())
					.eq(BillPayment::getState,
							BillPaymentDef.State.CONFIRMING.getCode()));
			queryWrapper.eq(BillPayment::getPurchaserId, customerId);
			billPaymentCountVo.setPurchaseToBeConfirmed(
					repository.selectCount(queryWrapper));

			// 统计开票申请已驳回
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.and(x -> x
					.eq(BillPayment::getBillType,
							BillPaymentDef.BillType.BUY.getCode())
					.eq(BillPayment::getState,
							BillPaymentDef.State.REJECTED.getCode()));
			queryWrapper.eq(BillPayment::getPurchaserId, customerId);
			billPaymentCountVo
					.setPurchaseRejected(repository.selectCount(queryWrapper));

			// 统计开票申请作废中
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(BillPayment::getPurchaserId, customerId);
			queryWrapper.eq(BillPayment::getState,
					BillPaymentDef.State.INVALIDING.getCode());
			billPaymentCountVo.setPurchaseInvaliding(
					repository.selectCount(queryWrapper));

			// 统计开票待确认
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.and(x -> x
					.eq(BillPayment::getBillType,
							BillPaymentDef.BillType.BUY.getCode())
					.eq(BillPayment::getState,
							BillPaymentDef.State.CONFIRMING.getCode()));
			queryWrapper.eq(BillPayment::getSellerId, customerId);
			billPaymentCountVo
					.setSaleToBeConfirmed(repository.selectCount(queryWrapper));

			// 统计开票已驳回
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.and(x -> x
					.eq(BillPayment::getBillType,
							BillPaymentDef.BillType.SELL.getCode())
					.eq(BillPayment::getState,
							BillPaymentDef.State.REJECTED.getCode()));
			queryWrapper.eq(BillPayment::getSellerId, customerId);
			billPaymentCountVo
					.setSaleRejected(repository.selectCount(queryWrapper));

			// 统计开票作废中
			queryWrapper.clear();
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(BillPayment::getSellerId, customerId);
			queryWrapper.eq(BillPayment::getState,
					BillPaymentDef.State.INVALIDING.getCode());
			billPaymentCountVo
					.setSaleInvaliding(repository.selectCount(queryWrapper));
		}

		return Optional.of(billPaymentCountVo);
	}

	@Override
	public BigDecimal totalAmount(String projectId, LocalDateTime startTime,
			LocalDateTime endTime, Integer type, String keyword, String name,
			List<Integer> states) {
		LambdaQueryWrapper<BillPayment> wrapper = Wrappers
				.lambdaQuery(BillPayment.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(projectId), BillPayment::getProjectId,
				projectId);
		wrapper.ge(Objects.nonNull(startTime), BillPayment::getBillDate,
				startTime);
		wrapper.le(Objects.nonNull(endTime), BillPayment::getBillDate, endTime);
		wrapper.eq(Objects.nonNull(type), BillPayment::getType, type);
		if (StringUtils.isNotBlank(name)) {
			if (BillPaymentDef.Type.SELL.match(type)) {
				wrapper.apply(
						"(JSON_EXTRACT(purchaser_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
						name);
			} else {
				wrapper.apply(
						"(JSON_EXTRACT(seller_enterprise, '$.name') LIKE CONCAT('%',{0},'%'))",
						name);
			}
		}

		// 开票编号或合同名称
		if (StringUtils.isNotBlank(keyword)) {
			List<String> contractIds = contractService.findByNameLike(keyword)
					.stream().map(Contract::getId).distinct().toList();
			wrapper.and(x -> x.like(BillPayment::getId, keyword).or()
					.in(com.baomidou.mybatisplus.core.toolkit.CollectionUtils
							.isNotEmpty(contractIds),
							BillPayment::getContractId, contractIds));
		}
		if (BillPaymentDef.Type.SELL.match(type)) {
			if (CollectionUtils.isNotEmpty(states)) {
				wrapper.and(x -> {
					// 判断状态，并根据不同状态添加查询条件
					for (Integer state : states) {
						switch (BillPaymentDef.State.from(state)) {
							// 草稿，发起方是自己
							case DRAFT -> x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.DRAFT
													.getCode()));

							// 待确认，发起方是对方
							case PENDING_CONFIRMATION -> x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.BUY
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

							// 确认中，发起方是自己
							case CONFIRMING -> x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.CONFIRMING
													.getCode()));

							// 已驳回，发起方是自己
							case REJECTED -> x.or(y -> y
									.eq(BillPayment::getBillType,
											BillPaymentDef.BillType.SELL
													.getCode())
									.eq(BillPayment::getState,
											BillPaymentDef.State.REJECTED
													.getCode()));

							// 已完成，双方
							case COMPLETED -> x.or(y -> y.eq(
									BillPayment::getState,
									BillPaymentDef.State.COMPLETED.getCode()));

							// 作废中，双方
							case INVALIDING -> x.or(y -> y.eq(
									BillPayment::getState,
									BillPaymentDef.State.INVALIDING.getCode()));

							// 已作废，双方
							case INVALID -> x.or(y -> y.eq(
									BillPayment::getState,
									BillPaymentDef.State.INVALID.getCode()));
						}
					}
				});
			} else {
				wrapper.and(
						x -> x.in(BillPayment::getState,
								BillPaymentDef.State.CONFIRMING.getCode(),
								BillPaymentDef.State.PENDING_CONFIRMATION
										.getCode(),
								BillPaymentDef.State.COMPLETED.getCode(),
								BillPaymentDef.State.INVALIDING.getCode(),
								BillPaymentDef.State.INVALID.getCode())
								.or(y -> y
										.eq(BillPayment::getBillType,
												BillPaymentDef.BillType.SELL
														.getCode())
										.in(BillPayment::getState,
												BillPaymentDef.State.DRAFT
														.getCode(),
												BillPaymentDef.State.REJECTED
														.getCode())));
			}
		}
		BigDecimal totalAmount = BigDecimal.ZERO;
		List<BillPayment> billPayments = repository.selectList(wrapper);
		if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
				.isNotEmpty(billPayments)) {
			if (ContractDef.Type.SELL.match(type)) {
				// 销售类型的要过滤出已完成状态的
				List<BillPayment> billPayments1 = billPayments.stream()
						.filter(i -> i.getState().equals(
								BillPaymentDef.State.COMPLETED.getCode()))
						.toList();
				totalAmount = billPayments1.stream()
						.map(BillPayment::getBillAmount)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			} else {
				totalAmount = billPayments.stream()
						.map(BillPayment::getBillAmount)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);

			}
		}

		return totalAmount.divide(BigDecimal.ONE, 2, RoundingMode.HALF_UP);
	}

	@Override
	@LogRecord(operatorType = LogDef.INNER, subType = LogDef.CREATE, success = "{{#success}}", type = LogDef.INVOICE_INFO, bizNo = "{{#resource.getId()}}", kvParam = {
			@LogRecord.KeyValuePair(key = "#highlight#", value = "{{#resource.getId()}}"),
			@LogRecord.KeyValuePair(key = "#projectId#", value = "{{#code}}") }, messageType = LogDef.MESSAGE_TYPE_ORDER, permission = LogDef.PROJECT_DEAL)
	public void notice(BillPayment resource, Integer type) {
		Project project = projectService.findOne(resource.getProjectId())
				.orElse(new Project());
		LogRecordContext.putVariable("code", project.getName());
		switch (type) {
			case 1 -> LogRecordContext.putVariable("success",
					LogDef.INVOICE_PENDING_CONFIRMATION);
			case 2 -> LogRecordContext.putVariable("success",
					LogDef.INVOICE_CONFIRMED);
			case 3 -> LogRecordContext.putVariable("success",
					LogDef.INVOICE_REJECTED);
			case 4 -> LogRecordContext.putVariable("success",
					LogDef.INVOICE_INVALID_ADD);
			case 5 -> LogRecordContext.putVariable("success",
					LogDef.INVOICE_INVALID_REJECTED);
			default -> {
			}
		}
		log.info("开票发送通知:{}", resource.getId());
	}

	/**
	 * 更新对账单未开票金额及状态
	 */
	private void amountBack(
			ArrayReconciliationJsonInfo arrayReconciliationJsonInfo,
			Integer code, Integer type) {
		// 关联的对账单的id列表
		List<String> reconciliationIds = arrayReconciliationJsonInfo.stream()
				.map(ReconciliationJsonInfo::getId).toList();

		// 对账单列表
		List<Reconciliation> reconciliations = reconciliationService
				.findByIds(reconciliationIds);

		// 使用Map来快速查找Reconciliation对象
		Map<String, Reconciliation> reconciliationMap = reconciliations.stream()
				.collect(Collectors.toMap(Reconciliation::getId,
						reconciliation -> reconciliation));

		// 更新的对账单
		List<Reconciliation> reconciliationList = new ArrayList<>();

		// 遍历回写信息并更新未开票金额
		arrayReconciliationJsonInfo.forEach(reconciliationJsonInfo -> {
			Reconciliation reconciliation = reconciliationMap
					.get(reconciliationJsonInfo.getId());
			switch (CommonDef.Symbol.from(code)) {
				// 新增开票
				case NO -> {
					reconciliation.setUnbilledAmount(
							reconciliation.getUnbilledAmount().subtract(
									reconciliationJsonInfo.getBillAmount()));
					if (BillPaymentDef.Type.SELL.match(type)) {
						// 更新对账单状态
						reconciliation.setInvoiceState(
								ReconciliationDef.InvoiceState.INVOICING
										.getCode());
					}

				}
				// 删除开票
				case YES -> {
					reconciliation.setUnbilledAmount(
							reconciliation.getUnbilledAmount().add(
									reconciliationJsonInfo.getBillAmount()));
					if (BillPaymentDef.Type.SELL.match(type)) {
						if (reconciliation.getUnbilledAmount()
								.add(reconciliationJsonInfo.getBillAmount())
								.equals(reconciliation
										.getReconciliationAmount())) {
							// 更新对账单状态
							reconciliation.setInvoiceState(
									ReconciliationDef.InvoiceState.NOT_INVOICED
											.getCode());
						}
					}

				}
			}

			reconciliationList.add(reconciliation);
		});

		// 批量更新对账单
		if (CollectionUtils.isNotEmpty(reconciliationList)) {
			reconciliationService.batchUpdate(reconciliationList);
		}
	}

	/**
	 * 组装vo
	 *
	 * @param billPayments
	 * @return
	 */
	private List<BillPaymentVo> packVo(List<BillPayment> billPayments) {
		List<String> list = billPayments.stream().map(BillPayment::getProjectId)
				.toList();
		Map<String, Project> projectMap = projectService.findByIds(list)
				.stream()
				.collect(Collectors.toMap(Project::getId, project -> project));
		return billPayments.stream().map(billPayment -> {
			BillPaymentVo vo = new BillPaymentVo();
			vo.setBillPayment(billPayment);
			Project project = projectMap.get(billPayment.getProjectId());
			if (Objects.nonNull(project)) {
				vo.setProject(project);
			}
			return vo;
		}).toList();
	}

	/**
	 * 发送短信
	 *
	 * @param billPayment
	 * @param templateCode
	 * @param title
	 */
	private void sendNotice(BillPayment billPayment, String templateCode,
			String title, Integer type) {
		Customer customer = null;
		if (ReconciliationDef.Type.SELL.match(type)) {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(billPayment.getPurchaserInputId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		} else {
			DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
					.findOne(billPayment.getSellerInputId()).orElse(null);
			if (Objects.nonNull(dealingsEnterprise)
					&& Objects.nonNull(dealingsEnterprise.getCustomerId())) {
				customer = customerService
						.findOne(dealingsEnterprise.getCustomerId())
						.orElse(null);
			}
		}

		if (Objects.nonNull(customer)) {
			if (StringUtils.isNotBlank(templateCode)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(templateCode)
						.params(Map.of("order_id", billPayment.getId()))
						.mobile(customer.getMobile()).build());
			}

			if (StringUtils.isNotBlank(title)) {
				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.ORDER.getCode())
						.title(title)
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.BILL_PAYMENT_DETAIL_PAGE)
						.detailId(String.valueOf(billPayment.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
		}
	}

}
