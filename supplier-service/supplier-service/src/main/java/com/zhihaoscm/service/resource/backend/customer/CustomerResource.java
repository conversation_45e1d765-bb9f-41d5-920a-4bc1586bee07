package com.zhihaoscm.service.resource.backend.customer;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.json.ArrayInteger;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;
import com.zhihaoscm.service.resource.validator.customer.CustomerValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "管理后台客户管理", description = "管理后台客户管理API")
@RestController
@RequestMapping("/backend-customer")
public class CustomerResource {

	@Autowired
	private CustomerService service;

	@Autowired
	private CustomerValidator customerValidator;

	@Operation(summary = "查询是否有项目-组织机构变更身份使用")
	@GetMapping("/has-project/{customerId}")
	@Secured({ AdminPermissionDef.SUPPLY_W })
	public ApiResponse<Boolean> findProject(@PathVariable Long customerId) {
		return new ApiResponse<>(
				customerValidator.validatorHasProject(customerId));
	}

	@Operation(summary = "修改客户企业身份")
	@PutMapping("/change-role/{customerId}")
	@Secured({ AdminPermissionDef.SUPPLY_W })
	public ApiResponse<Customer> changeRole(@PathVariable Long customerId,
			@RequestParam List<Integer> role) {
		Customer customer = customerValidator.validateBusiness(customerId);
		customer.setRoles(new ArrayInteger(role));
		return new ApiResponse<>(service.updateAllProperties(customer));
	}

}
