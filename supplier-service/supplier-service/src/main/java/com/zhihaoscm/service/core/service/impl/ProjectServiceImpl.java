package com.zhihaoscm.service.core.service.impl;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpStringIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.annotation.History;
import com.zhihaoscm.domain.bean.dto.DashboardScopeDto;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.json.ArrayChangeInfo;
import com.zhihaoscm.domain.bean.json.ChangeInfo;
import com.zhihaoscm.domain.bean.json.PledgeInfo;
import com.zhihaoscm.domain.bean.vo.*;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.domain.utils.DashboardDateUtils;
import com.zhihaoscm.domain.utils.PaginationUtils;
import com.zhihaoscm.service.aop.history.HistoryContext;
import com.zhihaoscm.service.config.LocalDateAdapter;
import com.zhihaoscm.service.config.LocalDateTimeAdapter;
import com.zhihaoscm.service.config.properties.SupplierChainProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.core.mapper.ProjectMapper;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.UserService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 项目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-13
 */
@Slf4j
@Service
public class ProjectServiceImpl
		extends MpStringIdBaseServiceImpl<Project, ProjectMapper>
		implements ProjectService {
	@Autowired
	private StringRedisClient redisClient;
	@Autowired
	private UserService userService;
	@Autowired
	private AccountsService accountsService;
	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;
	@Autowired
	private ContractService contractService;
	@Autowired
	private RepaymentService repaymentService;
	@Autowired
	private ReconciliationService reconciliationService;
	@Autowired
	private ProjectInceptionService projectInceptionService;
	@Autowired
	private GoodsService goodsService;
	@Autowired
	private StorageInceptionService storageInceptionService;
	@Autowired
	private StorageInceptionInboundDetailService storageInceptionInboundDetailService;
	@Autowired
	private StorageInceptionOutboundDetailService storageInceptionOutboundDetailService;
	@Autowired
	private InboundService inboundService;
	@Autowired
	private OutboundService outboundService;
	@Autowired
	private PaymentService paymentService;
	@Autowired
	private InventoryVerifyReportService inventoryVerifyReportService;
	@Autowired
	private PledgeService pledgeService;
	@Autowired
	private WarehouseService warehouseService;
	@Autowired
	private StorageService storageService;
	@Autowired
	private WarehouseGoodsInfoService warehouseGoodsInfoService;
	@Autowired
	private ProjectItemService projectItemService;
	@Autowired
	private OperationHistoryService operationHistoryService;
	@Autowired
	private OrderService orderService;
	@Autowired
	private SupplierChainProperties supplierChainProperties;

	public ProjectServiceImpl(ProjectMapper repository) {
		super(repository);
	}

	@Override
	public Page<ProjectVo> paging(Integer page, Integer size, String keyword,
			String paramName, String goodsName, Long handlerId,
			List<Integer> state, Integer delay, String sortKey,
			String sortOrder, Boolean hasAll, Long userId) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		queryWrapper.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		if (StringUtils.isNotBlank(keyword)) {
			queryWrapper.and(i -> i.like(Project::getName, keyword).or()
					.like(Project::getCode, keyword).or());
		}
		// 供应商或采购方名称不为空时
		if (StringUtils.isNotBlank(paramName)) {
			queryWrapper.and(wrapper -> wrapper.apply(
					"JSON_UNQUOTE(JSON_EXTRACT(supplier_names, '$[*]')) LIKE CONCAT('%', {0}, '%')",
					paramName).or()
					.apply("JSON_UNQUOTE(JSON_EXTRACT(customer_names, '$[*]')) LIKE CONCAT('%', {0}, '%')",
							paramName));
		}
		// 查询指派人id不为空时
		if (Objects.nonNull(handlerId)) {
			queryWrapper.and(wrapper -> wrapper.apply(
					"JSON_CONTAINS(project_manager_ids, {0}) or supervisor_id = {0}",
					handlerId.toString()));
		}
		if (StringUtils.isNotBlank(goodsName)) {
			queryWrapper.and(i -> i.like(Project::getGoodsName, goodsName));
		}

		queryWrapper.in(CollectionUtils.isNotEmpty(state), Project::getState,
				state);
		// 查询已逾期的收款列表
		List<Accounts> accountsList = accountsService.findByProjectIdsAndStatus(
				new ArrayList<>(), AccountsDef.Status.DELAY.getCode());
		// 从逾期数据中找出项目id并去重
		List<String> projectIds = accountsList.stream()
				.map(Accounts::getProjectId).distinct().toList();
		// 是否逾期查询
		if (Objects.nonNull(delay)) {
			if (CollectionUtils.isNotEmpty(projectIds)) {
				if (CommonDef.Symbol.YES.match(delay)) {
					queryWrapper.in(Project::getId, projectIds);
				} else {
					queryWrapper.notIn(Project::getId, projectIds);
				}
			} else {
				if (CommonDef.Symbol.YES.match(delay)) {
					return Page.of(page, size, 0);
				}
			}
			queryWrapper.eq(Project::getState,
					ProjectDef.State.PROCESSING.getCode());
		}
		// 没有查看所有权限时 只能查看指派人员是自己的项目
		if (!hasAll) {
			// 处理人是自己在的
			List<String> projectIdList = this.findByUserId(userId, null);
			if (CollectionUtils.isNotEmpty(projectIdList)) {
				queryWrapper.in(Project::getId, projectIdList);
			} else {
				return Page.of(page, size, 0);
			}
		}
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last(
					"order by " + sortKey + " " + sortOrder + ", id DESC");
		} else {
			// 默认按合同待签约、履约中、已结清状态排序状态相同按更新时间倒序排序。
			queryWrapper.last(
					"ORDER BY FIELD(state, 1,2,3) ASC, updated_time DESC");
		}
		Page<Project> pageResult = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		List<ProjectVo> projectVoList = this.packVo(pageResult.getRecords());
		return PageUtil.getRecordsInfoPage(pageResult, projectVoList);
	}

	@Override
	public com.zhihaoscm.common.bean.page.Page<ProjectProfitRankVo> projectProfitRank(
			Integer page, Integer size, String keyword, String supplierName,
			String purchaserName, Integer state) {
		LambdaQueryWrapper<Project> wrapper = Wrappers
				.lambdaQuery(Project.class);
		this.filterDeleted(wrapper);
		wrapper.and(StringUtils.isNotBlank(keyword),
				x -> x.like(Project::getName, keyword).or()
						.like(Project::getCode, keyword));

		wrapper.apply(StringUtils.isNotBlank(supplierName),
				"JSON_UNQUOTE(JSON_EXTRACT(supplier_names, '$[*]')) LIKE CONCAT('%', {0}, '%')",
				supplierName);

		wrapper.apply(StringUtils.isNotBlank(purchaserName),
				"JSON_UNQUOTE(JSON_EXTRACT(customer_names, '$[*]')) LIKE CONCAT('%', {0}, '%')",
				purchaserName);
		if (Objects.nonNull(state)) {
			wrapper.eq(Project::getState, state);
		} else {
			wrapper.in(Project::getState,
					List.of(ProjectDef.State.PROCESSING.getCode(),
							ProjectDef.State.FINISHED.getCode()));
		}
		return PaginationUtils.handelPage(page, size,
				this.packRankVo(repository.selectList(wrapper)));
	}

	@Override
	public List<Project> findByName(String name) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Objects.nonNull(name), Project::getName, name);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Project> findByNameLike(String name) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.like(StringUtils.isNotBlank(name), Project::getName, name);
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Project> findByNameOrGoodsNameLike(String name) {
		List<Goods> goods = goodsService.findByGoodsNameLike(name);
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.and(i -> i.like(Project::getName, name).or().in(
				CollectionUtils.isNotEmpty(goods), Project::getGoodsId,
				goods.stream().map(Goods::getId).toList()));
		return repository.selectList(queryWrapper);
	}

	@Override
	public Optional<ProjectVo> findVoById(String id) {
		// 根据项目id查询已逾期的付款记录
		List<Accounts> accounts = accountsService.findByProjectIdAndStates(id,
				List.of(AccountsDef.Status.DELAY.getCode()), null, null);
		ProjectVo projectVo = new ProjectVo();
		Project project = super.findOne(id).orElse(null);
		if (Objects.nonNull(project)) {
			projectVo.setProject(project);
			List<DealingsEnterprise> suppliers = dealingsEnterpriseService
					.findByIds(project.getSupplierIds());
			projectVo.setSuppliers(suppliers);
			List<DealingsEnterprise> customers = dealingsEnterpriseService
					.findByIds(project.getCustomerIds());
			projectVo.setCustomers(customers);
			// 状态为履约中才需要显示 是否逾期
			if (ProjectDef.State.PROCESSING.match(project.getState())) {
				if (CollectionUtils.isNotEmpty(accounts)) {
					projectVo.setDelay(CommonDef.Symbol.YES.getCode());
				} else {
					projectVo.setDelay(CommonDef.Symbol.NO.getCode());
				}
			}
			// 设置项目负责人vo
			projectVo.setSupervisor(
					this.packSupervisor(project.getSupervisorId()));
			// 设置项目经理vo
			projectVo.setProjectManagers(
					this.packProjectManagers(project.getProjectManagerIds()));
			// 设置货物信息
			goodsService.findOne(project.getGoodsId())
					.ifPresent(projectVo::setGoods);
			// 设置仓库信息
			List<Warehouse> warehouseList = warehouseService
					.findByIds(project.getWarehouseIds());
			List<Storage> storageList = storageService
					.findByIds(project.getStorageIds());
			// 封装仓库Vo
			List<WarehouseVo> warehouseVoList = this
					.packWarehouseVo(warehouseList, storageList);
			projectVo.setWarehouseVoList(warehouseVoList);
			// 项目采购期初对账总金额
			BigDecimal totalReconciledPurchaseAmount = BigDecimal.ZERO;
			// 项目销售期初对账总金额
			BigDecimal totalReconciledSalesAmount = BigDecimal.ZERO;
			// 项目采购期初货款支出总金额
			BigDecimal totalPaymentAmount = BigDecimal.ZERO;
			// 项目销售期初货款收入总金额
			BigDecimal totalReceiptAmount = BigDecimal.ZERO;
			// 项目对应的期初数据
			List<ProjectInception> projectInceptions = projectInceptionService
					.findByProjectId(id);
			if (CollectionUtils.isNotEmpty(projectInceptions)) {
				for (ProjectInception projectInception : projectInceptions) {
					if (Objects.nonNull(projectInception
							.getTotalReconciledPurchaseAmount())) {
						totalReconciledPurchaseAmount = totalReconciledPurchaseAmount
								.add(projectInception
										.getTotalReconciledPurchaseAmount());
					}
					if (Objects.nonNull(
							projectInception.getTotalReconciledSalesAmount())) {
						totalReconciledSalesAmount = totalReconciledSalesAmount
								.add(projectInception
										.getTotalReconciledSalesAmount());
					}
					if (Objects.nonNull(
							projectInception.getTotalPaymentAmount())) {
						totalPaymentAmount = totalPaymentAmount
								.add(projectInception.getTotalPaymentAmount());
					}
					if (Objects.nonNull(
							projectInception.getTotalReceiptAmount())) {
						totalReceiptAmount = totalReceiptAmount
								.add(projectInception.getTotalReceiptAmount());
					}
				}
			}

			// 上游供应商货款初始化
			BigDecimal supplierAmount;
			// 采购管理-列表的对账金额之和
			BigDecimal supplierRecAmount = BigDecimal.ZERO;
			// 采购管理-对账列表的数据
			List<Reconciliation> supplierReconciliations = reconciliationService
					.findProjectIdByState(id,
							ReconciliationDef.Type.BUY.getCode(), null, null,
							null);
			// 累计采购金额：取采购管理-对账列表的对账金额之和+项目采购期初对账总金额
			if (CollectionUtils.isNotEmpty(supplierReconciliations)) {
				supplierRecAmount = supplierReconciliations.stream()
						.map(Reconciliation::getReconciliationAmount)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			supplierAmount = supplierRecAmount
					.add(totalReconciledPurchaseAmount);
			projectVo.setSupplierAmount(supplierAmount
					.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
			// 下游采购方货款初始化
			BigDecimal customerAmount;
			// 销售管理-对账列表的状态为对账完成的数据之和
			BigDecimal customerRecAmount = BigDecimal.ZERO;
			// 销售管理-对账列表的状态为对账完成的数据
			List<Reconciliation> customReconciliations = reconciliationService
					.findProjectIdByState(id,
							ReconciliationDef.Type.SELL.getCode(),
							ReconciliationDef.State.FINISHED.getCode(), null,
							null);
			// 累计销售金额：取销售管理-对账列表的状态为对账完成的对账金额之和+项目销售期初对账总金额
			if (CollectionUtils.isNotEmpty(customReconciliations)) {
				customerRecAmount = customReconciliations.stream()
						.map(Reconciliation::getReconciliationAmount)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			customerAmount = customerRecAmount.add(totalReconciledSalesAmount);
			projectVo.setCustomerAmount(customerAmount
					.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
			// 上游供应商保证金初始化
			BigDecimal supplierDeposit = BigDecimal.ZERO;
			// 取资金管理-付款列表费用类型为履约保证金和订单保证金的付款金额之和
			List<Payment> supplierPayments = paymentService
					.findByTypeAndProjectId(PaymentDef.Type.BUY.getCode(), id,
							null,
							List.of(PaymentDef.CostType.ORDER_DEPOSIT.getCode(),
									PaymentDef.CostType.DEPOSIT.getCode()),
							null, null, null);
			if (CollectionUtils.isNotEmpty(supplierPayments)) {
				supplierDeposit = supplierPayments.stream()
						.map(Payment::getAmount).filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			projectVo.setSupplierDeposit(supplierDeposit
					.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));

			// 下游采购方保证金初始化
			BigDecimal customerDeposit = BigDecimal.ZERO;
			// 收取保证金：取资金管理-收款列表费用类型为履约保证金和订单保证金状态为收款完成且未关联退款完成的退款单的金额之和。
			List<Payment> customerPayments = paymentService
					.findByTypeAndProjectId(PaymentDef.Type.SELL.getCode(), id,
							PaymentDef.State.COMPLETED.getCode(),
							List.of(PaymentDef.CostType.ORDER_DEPOSIT.getCode(),
									PaymentDef.CostType.DEPOSIT.getCode()),
							false, null, null);
			if (CollectionUtils.isNotEmpty(customerPayments)) {
				customerDeposit = customerPayments.stream()
						.map(Payment::getAmount).filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			projectVo.setCustomerDeposit(customerDeposit
					.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
			// 上游供应商预付货款初始化
			BigDecimal supplierPreAmount = BigDecimal.ZERO;
			// 资金管理付款列表费用类型为货款的付款金额之和
			List<Payment> supplierPreAmounts = paymentService
					.findByTypeAndProjectId(PaymentDef.Type.BUY.getCode(), id,
							null, List.of(PaymentDef.CostType.GOODS_PAYMENT
									.getCode()),
							false, null, null);
			if (CollectionUtils.isNotEmpty(supplierPreAmounts)) {
				supplierPreAmount = supplierPreAmounts.stream()
						.map(Payment::getAmount).filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			// 预收货款余额：资金管理付款列表费用类型为货款的付款金额之和-取采购管理-对账列表的对账金额之和+项目采购期初货款支出总金额-项目采购期初对账总金额
			supplierPreAmount = supplierPreAmount.subtract(supplierRecAmount)
					.add(totalPaymentAmount)
					.subtract(totalReconciledPurchaseAmount);
			projectVo.setSupplierPreAmount(supplierPreAmount
					.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
			// 下游采购方预付货款初始化
			BigDecimal customerPreAmount = BigDecimal.ZERO;
			// 资金管理付款列表费用类型为货款的付款金额之和
			List<Payment> customerPreAmounts = paymentService
					.findByTypeAndProjectId(PaymentDef.Type.SELL.getCode(), id,
							PaymentDef.State.COMPLETED.getCode(),
							List.of(PaymentDef.CostType.GOODS_PAYMENT
									.getCode()),
							false, null, null);
			if (CollectionUtils.isNotEmpty(customerPreAmounts)) {
				customerPreAmount = customerPreAmounts.stream()
						.map(Payment::getAmount).filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			// 预付货款余额：资金管理收款列表费用类型为货款状态为收款完成且未关联退款完成的退款单的金额之和-售管理对账列表的状态为对账完成的对账金额之和-销售管理对账列表的状态为对账完成的对账金额之和+项目销售期初货款收入总金额-项目销售期初对账总金额
			customerPreAmount = customerPreAmount.subtract(customerRecAmount)
					.add(totalReceiptAmount)
					.subtract(totalReconciledSalesAmount);
			projectVo.setCustomerPreAmount(customerPreAmount
					.divide(new BigDecimal(10000), 2, RoundingMode.HALF_UP));
			// 上游供应商预估可提货余额初始化
			BigDecimal supplierEstimatedGoodsAmount = orderService
					.findSupplierEstimatedGoodsAmount(id)
					.orElse(BigDecimal.ZERO);
			projectVo.setSupplierEstimatedGoodsAmount(
					supplierEstimatedGoodsAmount.divide(new BigDecimal(10000),
							2, RoundingMode.HALF_UP));
			// 下游采购方预估可提货余额初始化
			BigDecimal customerEstimatedGoodsAmount = orderService
					.findCustomerEstimatedGoodsAmount(id)
					.orElse(BigDecimal.ZERO);
			projectVo.setCustomerEstimatedGoodsAmount(
					customerEstimatedGoodsAmount.divide(new BigDecimal(10000),
							2, RoundingMode.HALF_UP));
			// 当前核库总数初始化
			BigDecimal currentVerifyQuantity = BigDecimal.ZERO;
			List<InventoryVerifyReport> reports = inventoryVerifyReportService
					.findByProjectIdAndContractId(id, null, null);
			if (CollectionUtils.isNotEmpty(reports)) {
				currentVerifyQuantity = reports.stream()
						.map(InventoryVerifyReport::getTotalTodayVerifyQuantity)
						.filter(Objects::nonNull)
						.reduce(BigDecimal.ZERO, BigDecimal::add);
			}
			projectVo.setCurrentVerifyQuantity(currentVerifyQuantity);
			// 质押总数初始化
			BigDecimal pledge = BigDecimal.ZERO;
			// 质押金额初始化
			BigDecimal pledgeAmount = BigDecimal.ZERO;
			List<Pledge> pledges = pledgeService.findByProjectIds(List.of(id));
			if (CollectionUtils.isNotEmpty(pledges)) {
				for (Pledge p : pledges) {
					// 质押信息列表
					List<PledgeInfo> pledgeInfoList = this
							.convertPledgeInfo(p.getPledgeInfo());
					if (CollectionUtils.isNotEmpty(pledgeInfoList)) {
						// 质押信息最后一条
						PledgeInfo lastPledgeInfo = pledgeInfoList
								.get(pledgeInfoList.size() - 1);
						BigDecimal pledgeNum = BigDecimal.ZERO;
						BigDecimal pledgePrice = BigDecimal.ZERO;
						if (Objects.nonNull(lastPledgeInfo.getPledgeNum())) {
							pledgeNum = lastPledgeInfo.getPledgeNum();
						}
						if (Objects.nonNull(lastPledgeInfo.getPledgePrice())) {
							pledgePrice = lastPledgeInfo.getPledgePrice();
						}
						pledge = pledge.add(pledgeNum);
						pledgeAmount = pledgeAmount.add(pledgePrice);
					}
				}
			}
			// 按金额时设置 质押金额
			if (ProjectDef.ControlRatioWay.BY_AMOUNT
					.match(project.getControlRatioWay())) {
				// 设置质押金额
				projectVo.setPledgeAmount(pledgeAmount);
			} else {
				// 设置质押总量
				projectVo.setPledge(pledge);
			}
			projectVo.setCanOutboundQuantity(this
					.findCanSaleQuantity(id, pledge).orElse(BigDecimal.ZERO));
		}
		List<ProjectItem> projectItems = projectItemService
				.findByProjectIds(List.of(id));
		projectVo.setProjectItems(projectItems);
		return Optional.of(projectVo);
	}

	@Override
	public Optional<BigDecimal> findCanSaleQuantity(String projectId,
			BigDecimal pledge) {
		Project project = super.findOne(projectId).orElse(null);
		// 根据项目id查询仓储期初入库信息
		List<StorageInceptionInboundDetail> storageInboundList = storageInceptionInboundDetailService
				.findByProjectIds(List.of(projectId));
		// 根据项目id查询仓储期初出库信息
		List<StorageInceptionOutboundDetail> storagOutnboundList = storageInceptionOutboundDetailService
				.findByProjectIds(List.of(projectId));
		// 根据项目id查询已入库的数据
		List<Inbound> inboundList = inboundService.findByProjectIds(
				List.of(projectId),
				List.of(InboundDef.Status.INBOUNDED.getCode()),
				InboundDef.Type.JXC.getCode());
		// 根据项目id查询已出库的数据
		List<Outbound> outboundList = outboundService.findByProjectIds(
				List.of(projectId),
				List.of(OutboundDef.Status.OUTBOUND.getCode()),
				InboundDef.Type.JXC.getCode());
		// 期初入库数量/重量
		BigDecimal inboundWeight1 = BigDecimal.ZERO;
		// 期初出库数量/重量
		BigDecimal outboundWeight1 = BigDecimal.ZERO;
		// 已完成入库数量/重量
		BigDecimal inboundWeight2 = BigDecimal.ZERO;
		// 已完成出库数量/重量
		BigDecimal outboundWeight2 = BigDecimal.ZERO;
		// 库存数量/重量
		BigDecimal stockWeight = BigDecimal.ZERO;
		// 可销售出库数量/重量
		BigDecimal canSaleQuantity;
		// 期初入库数量
		if (CollectionUtils.isNotEmpty(storageInboundList)) {
			inboundWeight1 = storageInboundList.stream()
					.map(StorageInceptionInboundDetail::getQuantity)
					.filter(Objects::nonNull)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		// 期初出库数量
		if (CollectionUtils.isNotEmpty(storagOutnboundList)) {
			outboundWeight1 = storagOutnboundList.stream()
					.map(StorageInceptionOutboundDetail::getQuantity)
					.filter(Objects::nonNull)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		// 已完成入库数量
		if (CollectionUtils.isNotEmpty(inboundList)) {
			inboundWeight2 = inboundList.stream().map(Inbound::getInboundWeight)
					.filter(Objects::nonNull)
					.reduce(BigDecimal.ZERO, BigDecimal::add);

		}
		// 已完成出库数量/重量
		if (CollectionUtils.isNotEmpty(outboundList)) {
			outboundWeight2 = outboundList.stream()
					.map(Outbound::getOutboundWeight).filter(Objects::nonNull)
					.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		// 库存数量/重量= 期初库存（期初入库-期初出库) + 有效入库数量 - 有效出库数量
		stockWeight = stockWeight.add(inboundWeight1).add(inboundWeight2)
				.subtract(outboundWeight1).subtract(outboundWeight2);
		// 待出库的重量
		BigDecimal toOutboundWeight = outboundService
				.find(projectId, null, null,
						List.of(OutboundDef.Status.CONFIRMING.getCode(),
								OutboundDef.Status.TO_BE_INITIATED.getCode(),
								OutboundDef.Status.DRAFT.getCode(),
								OutboundDef.Status.REJECTED.getCode(),
								OutboundDef.Status.INVALIDING.getCode(),
								OutboundDef.Status.SIGNING.getCode()),
						InboundDef.Type.JXC.getCode())
				.stream().map(Outbound::getOutboundWeight)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		// 质押数量/重量*库存控货比
		BigDecimal pledgeWeight1 = BigDecimal.ZERO;
		if (Objects.nonNull(project)) {
			// 质押数量/重量*库存控货比
			pledgeWeight1 = Objects.nonNull(project.getInventoryControlRatio())
					? pledge.multiply(project.getInventoryControlRatio())
							.divide(new BigDecimal(100), 2,
									RoundingMode.HALF_UP)
					: pledge;
		}
		canSaleQuantity = stockWeight.subtract(pledgeWeight1)
				.subtract(toOutboundWeight);
		return Optional.of(canSaleQuantity);
	}

	@Override
	public List<Project> findByCreatedTime(LocalDateTime beginTime,
			LocalDateTime endTime) {
		LambdaQueryWrapper<Project> wrapper = Wrappers
				.lambdaQuery(Project.class);
		this.filterDeleted(wrapper);
		wrapper.ge(Objects.nonNull(beginTime), Project::getCreatedTime,
				beginTime);
		wrapper.le(Objects.nonNull(endTime), Project::getCreatedTime, endTime);
		return repository.selectList(wrapper);
	}

	@Override
	public List<ProjectSpecialVo> projectSpecialsSelector(String name) {
		// 根据名字模糊搜索已启用的后台用户
		List<User> attaches = userService.selectorUser(name,
				CustomerDef.State.ACTIVE.getCode());
		// 组装数据
		if (attaches.isEmpty()) {
			return List.of();
		}
		List<ProjectSpecialVo> projectSpecials = new ArrayList<>();
		for (User user : attaches) {
			ProjectSpecialVo projectSpecial = new ProjectSpecialVo();
			projectSpecial.setUser(user);
			// 设置后台用户履约中的项目
			projectSpecial
					.setCount((long) this
							.findByUserId(user.getId(),
									ProjectDef.State.PROCESSING.getCode())
							.size());
			projectSpecials.add(projectSpecial);
		}
		return projectSpecials;
	}

	@Override
	public List<String> findByUserId(Long userId, Integer state) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		queryWrapper.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Objects.nonNull(state), Project::getState, state);
		// 动态添加 JSON_CONTAINS 条件
		if (Objects.nonNull(userId)) {
			queryWrapper.and(wrapper -> wrapper.apply(
					"JSON_CONTAINS(project_manager_ids, {0}) OR supervisor_id = {0}",
					String.valueOf(userId)));
		}
		return repository.selectList(queryWrapper).stream().map(Project::getId)
				.toList();
	}

	@Override
	public List<String> findByUserIdAndIsExistStorage(Long userId,
			Integer state, Integer isExistStorage) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		queryWrapper.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Objects.nonNull(state), Project::getState, state);
		queryWrapper.eq(Objects.nonNull(isExistStorage),
				Project::getIsExistStorage, isExistStorage);
		// 动态添加 JSON_CONTAINS 条件
		if (Objects.nonNull(userId)) {
			queryWrapper.and(wrapper -> wrapper.apply(
					"JSON_CONTAINS(project_manager_ids, {0}) OR supervisor_id = {0}",
					String.valueOf(userId)));
		}
		return repository.selectList(queryWrapper).stream().map(Project::getId)
				.toList();
	}

	@Override
	public List<Project> findByNameAndIds(List<String> projectIds,
			String projectName) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		queryWrapper.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.like(StringUtils.isNotBlank(projectName), Project::getName,
				projectName);
		queryWrapper.in(CollectionUtils.isNotEmpty(projectIds), Project::getId,
				projectIds);
		return repository.selectList(queryWrapper);
	}

	@Override
	public Page<Project> allSelector(Integer page, Integer size, String name) {
		LambdaQueryWrapper<Project> wrapper = Wrappers
				.lambdaQuery(Project.class);
		this.filterDeleted(wrapper);
		wrapper.like(StringUtils.isNotBlank(name), Project::getName, name);
		// 默认按合同待签约、履约中、已结清状态排序状态相同按更新时间倒序排序。
		wrapper.last(
				"ORDER BY FIELD(state, 1,2,3) ASC, updated_time DESC, id DESC");
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	public Page<ProjectVo> selector(Integer page, Integer size,
			List<Integer> states, String name, String projectId,
			Integer isExistStorage, Long userId, Integer recordedParam) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		queryWrapper.in(CollectionUtils.isNotEmpty(states), Project::getState,
				states);
		queryWrapper.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.like(StringUtils.isNotBlank(name), Project::getName, name);
		queryWrapper.eq(Objects.nonNull(projectId), Project::getId, projectId);
		queryWrapper.eq(Objects.nonNull(isExistStorage),
				Project::getIsExistStorage, isExistStorage);
		// 处理人是自己的项目
		List<String> projectIdList = this.findByUserId(userId, null);
		if (CollectionUtils.isNotEmpty(projectIdList)) {
			queryWrapper.in(Project::getId, projectIdList);
		} else {
			return Page.of(page, size, 0);
		}
		if (Objects.nonNull(recordedParam)) {
			if (ProjectDef.RecordedParam.UP.match(recordedParam)) {
				queryWrapper.eq(Project::getSupplierIsRecorded,
						CommonDef.Symbol.YES.getCode());
			} else if (ProjectDef.RecordedParam.DOWN.match(recordedParam)) {
				queryWrapper.eq(Project::getCustomerIsRecorded,
						CommonDef.Symbol.YES.getCode());
			}
		}
		// 默认按更新时间倒序
		queryWrapper.orderByDesc(Project::getUpdatedTime);
		Page<Project> pageResult = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		List<ProjectVo> projectVoList = this.packVo(pageResult.getRecords());
		return PageUtil.getRecordsInfoPage(pageResult, projectVoList);
	}

	@History(success = HistoryDef.INVENTORY_PROJECT_ADD, bizNo = "{{#project.getId()}}", module = HistoryDef.INVENTORY_PROJECT)
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Project create(Project project, List<ProjectItem> projectItems) {
		project.setCode(supplierChainProperties.getBizCode()
				+ AutoCodeDef.PROJECT_CREATE_AUTO_CODE.apply(redisClient,
						RedisKeys.Cache.PROJECT_CODE_GENERATOR, "", 3,
						AutoCodeDef.DATE_TYPE.yyMM));
		project.setId(project.getCode());
		List<DealingsEnterprise> suppliers = dealingsEnterpriseService
				.findByIds(project.getSupplierIds());
		List<String> supplierNames = suppliers.stream()
				.map(DealingsEnterprise::getInstitutionName).toList();
		project.setSupplierNames(new ArrayString(supplierNames));
		List<DealingsEnterprise> dealingsEnterprises = dealingsEnterpriseService
				.findByIds(project.getCustomerIds());
		List<String> customerNames = dealingsEnterprises.stream()
				.map(DealingsEnterprise::getInstitutionName).toList();
		project.setCustomerNames(new ArrayString(customerNames));
		//
		List<User> users = userService
				.findByIds(project.getProjectManagerIds());
		List<String> userNames = users.stream().map(User::getName).toList();
		project.setProjectManagerNames(new ArrayString(userNames));
		Project project1 = super.create(project);
		HistoryContext.putVariable("project", project);
		if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
				.isNotEmpty(projectItems)) {
			for (ProjectItem projectItem : projectItems) {
				// 设置项目id
				projectItem.setProjectId(project1.getId());
			}
			// 批量创建记录
			projectItemService.batchCreate(projectItems);
		}
		return project1;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Project updateProject(Project project,
			List<ProjectItem> projectItems,
			List<ProjectItem> projectItemUpdateList) {
		// 之前数据库的可提货额
		List<ProjectItem> oldProjectItemList = projectItemService
				.findByProjectIds(List.of(project.getId()));
		Project oldProject = super.findOne(project.getId()).orElse(null);
		// 根据项目id查询预估可提货额公式项
		List<ProjectItem> exitsProjectItems = projectItemService
				.findByProjectIds(List.of(project.getId()));
		// 需要删除的列表
		List<Long> deleteIds = new ArrayList<>(
				exitsProjectItems.stream().map(ProjectItem::getId).toList());
		// 需要更新的列表
		if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
				.isNotEmpty(projectItemUpdateList)) {
			for (ProjectItem projectItem : projectItemUpdateList) {
				deleteIds.remove(projectItem.getId());
			}
			// 批量更新项目期初明细记录
			projectItemService.batchUpdate(projectItemUpdateList);
		}
		// 需要新增的列表
		if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
				.isNotEmpty(projectItems)) {
			for (ProjectItem projectItem : projectItems) {
				// 设置项目id
				projectItem.setProjectId(project.getId());
			}
			// 批量创建记录
			projectItemService.batchCreate(projectItems);
		}
		if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
				.isNotEmpty(deleteIds)) {
			// 需要删除的列表
			projectItemService.batchDelete(deleteIds);
		}
		// 修改之后数据库的可提货额
		List<ProjectItem> newProjectItemList = projectItemService
				.findByProjectIds(List.of(project.getId()));
		List<DealingsEnterprise> suppliers = dealingsEnterpriseService
				.findByIds(project.getSupplierIds());
		List<String> supplierNames = suppliers.stream()
				.map(DealingsEnterprise::getInstitutionName).toList();
		project.setSupplierNames(new ArrayString(supplierNames));
		List<DealingsEnterprise> dealingsEnterprises = dealingsEnterpriseService
				.findByIds(project.getCustomerIds());
		List<String> customerNames = dealingsEnterprises.stream()
				.map(DealingsEnterprise::getInstitutionName).toList();
		project.setCustomerNames(new ArrayString(customerNames));
		List<User> users = userService
				.findByIds(project.getProjectManagerIds());
		// 创建一个 Map，将 User 的 id 映射到 User 对象
		Map<Long, User> userMap = users.stream()
				.collect(Collectors.toMap(User::getId, user -> user));
		// 创建一个 List 来存储用户名称，并保持与 ProjectManagerIds 的顺序一致
		List<String> userNames = project.getProjectManagerIds().stream()
				.map(id -> userMap.get(id).getName()).toList();
		project.setProjectManagerNames(new ArrayString(userNames));
		if (Objects.nonNull(oldProject)) {
			this.handleHistory(oldProject, project, projectItems,
					oldProjectItemList, newProjectItemList);
		}
		return super.updateAllProperties(project);
	}

	@Override
	public Integer validateParam(String projectId, Long supplierId,
			Long customerId, Long goodsId, Integer type) {
		Project project = super.findOne(projectId).orElse(null);
		if (Objects.nonNull(project)) {
			// 校验该项目是否被合同关联了
			List<Contract> contracts = contractService
					.findByProjectId(List.of(projectId));
			if (CollectionUtils.isNotEmpty(contracts)) {
				// 传入的货物id不为空且和项目的货物id相同时则已经被合同关联了
				if (Objects.nonNull(goodsId)
						&& goodsId.equals(project.getGoodsId())) {
					return CommonDef.Symbol.YES.getCode();
				}
				// 上游供应商id不为空
				if (Objects.nonNull(supplierId)) {
					// 找出合同里的所有上游供应商id
					List<Long> supplierIds = contracts.stream()
							.map(Contract::getUpstreamId)
							.filter(Objects::nonNull).distinct().toList();
					// 合同里面的供应商包含该供应商 则被关联了
					if (supplierIds.contains(supplierId)) {
						return CommonDef.Symbol.YES.getCode();
					}
				}
				// 下游采购方id不为空
				if (Objects.nonNull(customerId)) {
					// 找出关联该项目合同里的下游采购方id
					List<Long> customerIds = contracts.stream()
							.map(Contract::getDownstreamId)
							.filter(Objects::nonNull).distinct().toList();
					// 合同里面的采购方包含该采购方 则被关联了
					if (customerIds.contains(customerId)) {
						return CommonDef.Symbol.YES.getCode();
					}
				}
				// 校验上下游是否自录数据 是否可以修改
				if (Objects.nonNull(type)) {
					// 校验下游采购方是否自录数据可以修改
					if (ProjectDef.IsRecordedType.DOWN.match(type)) {
						// 涉及到下游采购方的数据合同时 排除采购合同
						List<Contract> downContracts = contracts.stream()
								.filter(contract -> !contract.getContractType()
										.equals(ContractDef.ContractType.PURCHASE
												.getCode()))
								.toList();
						if (CollectionUtils.isNotEmpty(downContracts)) {
							return CommonDef.Symbol.YES.getCode();
						}
					} else {
						// 涉及到上游供应商的数据合同时 排除销售合同和借款合同
						List<Contract> upContracts = contracts.stream()
								.filter(contract -> !contract.getContractType()
										.equals(ContractDef.ContractType.SALES
												.getCode())
										&& !contract.getContractType().equals(
												ContractDef.ContractType.LOAN_AGREEMENT
														.getCode()))
								.toList();
						if (CollectionUtils.isNotEmpty(upContracts)) {
							return CommonDef.Symbol.YES.getCode();
						}
					}
				}
			}
		}
		return CommonDef.Symbol.NO.getCode();
	}

	@Override
	public void updateState(String projectId, Integer state) {
		LambdaUpdateWrapper<Project> wrapper = Wrappers
				.lambdaUpdate(Project.class).set(Project::getState, state)
				.eq(Project::getId, projectId)
				.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		repository.update(wrapper);
	}

	@Override
	public Optional<ProjectInventoryStatisticsVo> projectInventoryStatistics(
			String projectId) {
		ProjectInventoryStatisticsVo vo = new ProjectInventoryStatisticsVo();
		BigDecimal inboundWeight = BigDecimal.ZERO;
		BigDecimal outboundWeight = BigDecimal.ZERO;

		warehouseGoodsInfoService.staticsByProjectId(projectId)
				.ifPresent(warehouseGoodsInfoVo -> vo.setCurrentInventory(
						Objects.nonNull(warehouseGoodsInfoVo.getBoundWeight())
								? warehouseGoodsInfoVo.getBoundWeight()
								: BigDecimal.ZERO));

		List<Inbound> inboundList = inboundService.findByProjectIds(
				List.of(projectId),
				List.of(InboundDef.Status.INBOUNDED.getCode()),
				InboundDef.Type.JXC.getCode());
		List<StorageInceptionInboundDetail> inboundDetails = storageInceptionInboundDetailService
				.findByProjectIds(List.of(projectId));

		if (CollectionUtils.isNotEmpty(inboundList)) {
			inboundWeight = inboundWeight
					.add(inboundList.stream().map(Inbound::getInboundWeight)
							.reduce(BigDecimal.ZERO, BigDecimal::add));
		}

		if (CollectionUtils.isNotEmpty(inboundDetails)) {
			inboundWeight = inboundWeight.add(inboundDetails.stream()
					.map(StorageInceptionInboundDetail::getQuantity)
					.reduce(BigDecimal.ZERO, BigDecimal::add));
		}

		List<Outbound> outboundList = outboundService.findByProjectIds(
				List.of(projectId),
				List.of(OutboundDef.Status.OUTBOUND.getCode()),
				InboundDef.Type.JXC.getCode());
		List<StorageInceptionOutboundDetail> outboundDetails = storageInceptionOutboundDetailService
				.findByProjectIds(List.of(projectId));

		if (CollectionUtils.isNotEmpty(outboundDetails)) {
			outboundWeight = outboundWeight.add(outboundDetails.stream()
					.map(StorageInceptionOutboundDetail::getQuantity)
					.reduce(BigDecimal.ZERO, BigDecimal::add));
		}

		if (CollectionUtils.isNotEmpty(outboundList)) {
			outboundWeight = outboundWeight
					.add(outboundList.stream().map(Outbound::getOutboundWeight)
							.reduce(BigDecimal.ZERO, BigDecimal::add));
		}

		vo.setCumulativeInbound(
				inboundWeight.setScale(2, RoundingMode.HALF_UP));
		vo.setCumulativeOutbound(
				outboundWeight.setScale(2, RoundingMode.HALF_UP));
		return Optional.of(vo);
	}

	@Override
	public List<Project> findByCustomerId(Long customerId) {
		List<DealingsEnterprise> dealingsEnterprise = dealingsEnterpriseService
				.findByCustomerIdAndEnterpriseType(customerId,
						DealingsEnterpriseDef.EnterpriseType.REGISTERED_BUSINESS
								.getCode());

		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		this.filterDeleted(queryWrapper);

		List<Long> ids = dealingsEnterprise.stream()
				.map(DealingsEnterprise::getCustomerId).filter(Objects::nonNull)
				.distinct().toList();

		if (!ids.isEmpty()) {
			String condition = ids.stream().map(id -> String.format(
					"JSON_CONTAINS(customer_ids, '\"%d\"', '$') OR JSON_CONTAINS(supplier_ids, '\"%d\"', '$')",
					id, id)).collect(Collectors.joining(" OR "));
			queryWrapper.apply("(" + condition + ")");
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Project> findBySellerId(Long sellerId) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		queryWrapper.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		// 动态添加 JSON_CONTAINS 条件
		if (Objects.nonNull(sellerId)) {
			queryWrapper.and(
					wrapper -> wrapper.apply("JSON_CONTAINS(supplier_ids, {0})",
							String.valueOf(sellerId)));

		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Project> findByCustomerIdAndState(Long customerId,
			Integer state) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		queryWrapper.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Objects.nonNull(state), Project::getState, state);
		// 动态添加 JSON_CONTAINS 条件
		if (Objects.nonNull(customerId)) {
			queryWrapper.and(
					wrapper -> wrapper.apply("JSON_CONTAINS(customer_ids, {0})",
							String.valueOf(customerId)));
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Project> findByCustomerIdsAndState(List<Long> customerIds,
			Integer state) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		queryWrapper.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Objects.nonNull(state), Project::getState, state);
		// 动态添加 JSON_CONTAINS 条件
		if (CollectionUtils.isNotEmpty(customerIds)) {
			if (customerIds.size() > 1) {
				queryWrapper.and(wrapper -> {
					for (Long id : customerIds) {
						wrapper.or().apply("JSON_CONTAINS(customer_ids, {0})",
								id.toString());
					}
				});
			} else {
				queryWrapper.and(wrapper -> wrapper.apply(
						"JSON_CONTAINS(customer_ids, {0})",
						customerIds.get(0).toString()));
			}
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Project> findBySupplierIdsAndState(List<Long> supplierIds,
			Integer state) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		queryWrapper.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Objects.nonNull(state), Project::getState, state);
		// 动态添加 JSON_CONTAINS 条件
		// 动态添加 JSON_CONTAINS 条件
		if (CollectionUtils.isNotEmpty(supplierIds)) {
			if (supplierIds.size() > 1) {
				queryWrapper.and(wrapper -> {
					for (Long id : supplierIds) {
						wrapper.or().apply("JSON_CONTAINS(supplier_ids, {0})",
								id.toString());
					}
				});
			} else {
				queryWrapper.and(wrapper -> wrapper.apply(
						"JSON_CONTAINS(supplier_ids, {0})",
						supplierIds.get(0).toString()));
			}
		}
		return repository.selectList(queryWrapper);
	}

	@Override
	public List<Project> findByCustomerIdOrSupplierId(Long id) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		queryWrapper.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.and(x -> x
				.apply("JSON_CONTAINS(supplier_ids, {0})", String.valueOf(id))
				.or()
				.apply("JSON_CONTAINS(customer_ids, {0})", String.valueOf(id)));

		return repository.selectList(queryWrapper);
	}

	@Override
	public com.zhihaoscm.common.bean.page.Page<ProjectVo> selectorMytask(
			Integer page, Integer size, String keyword) {
		if (!UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.PROJECT_DEAL
						.getPermission())) {
			return new com.zhihaoscm.common.bean.page.Page<>();
		}
		List<ProjectVo> projectVoList = new ArrayList<>();
		List<Repayment> repaymentList = repaymentService.findAll();
		if (CollectionUtils.isNotEmpty(repaymentList)) {
			List<String> projectIds = repaymentList.stream()
					.filter(repayment -> RepaymentDef.State.DEDUCTION_COMPLETED
							.getCode().equals(repayment.getState()))
					.map(Repayment::getProjectId).distinct().toList();
			if (CollectionUtils.isNotEmpty(projectIds)) {
				projectIds = this
						.findByUserId(
								Objects.requireNonNull(
										UserContextHolder.getUser()).getId(),
								ProjectDef.State.PROCESSING.getCode())
						.stream().filter(projectIds::contains).toList();
				if (CollectionUtils.isNotEmpty(projectIds)) {
					List<Project> projects = this.findByNameAndIds(projectIds,
							keyword);
					projects.sort((p1, p2) -> p2.getUpdatedTime()
							.compareTo(p1.getUpdatedTime()));
					for (Project project : projects) {
						ProjectVo projectVo = new ProjectVo();
						projectVo.setProject(project);
						projectVo.setAmountReleased(repaymentService
								.findByProjectId(project.getId(),
										RepaymentDef.State.DEDUCTION_COMPLETED
												.getCode())
								.orElse(BigDecimal.ZERO)
								.setScale(0, RoundingMode.HALF_UP));
						projectVoList.add(projectVo);
					}
				}
			}
		}

		return PaginationUtils.handelPage(page, size, projectVoList);
	}

	@Override
	public Page<Project> selectorInception(Integer page, Integer size,
			String keyword, Long userId) {
		// 项目期初数据
		List<ProjectInception> projectInceptions = projectInceptionService
				.findAll();
		// 项目期初的项目id
		List<String> projectIds = projectInceptions.stream()
				.map(ProjectInception::getProjectId).distinct().toList();
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		// 履约中的项目
		queryWrapper.eq(Project::getState,
				ProjectDef.State.PROCESSING.getCode());
		queryWrapper.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.like(StringUtils.isNotBlank(keyword), Project::getName,
				keyword);
		// 处理人是自己的项目
		List<String> projectIdList = this.findByUserId(userId, null);
		if (CollectionUtils.isNotEmpty(projectIdList)) {
			queryWrapper.in(Project::getId, projectIdList);
		} else {
			return Page.of(page, size, 0);
		}
		// 维护了项目期初的项目，不能被选到
		if (CollectionUtils.isNotEmpty(projectIds)) {
			queryWrapper.notIn(Project::getId, projectIds);
		}
		// 默认按更新时间倒序
		queryWrapper.orderByDesc(Project::getUpdatedTime);
		return repository.selectPage(new Page<>(page, size), queryWrapper);
	}

	@Override
	public Page<ProjectVo> selectorStorageInception(Integer page, Integer size,
			String keyword, Long id) {
		// 仓储期初数据
		List<StorageInception> storageInceptionList = storageInceptionService
				.findAll();
		// 仓储期初的项目id
		List<String> projectIds = storageInceptionList.stream()
				.map(StorageInception::getProjectId).distinct().toList();
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		this.filterDeleted(queryWrapper);
		// 履约中的项目
		queryWrapper.eq(Project::getState,
				ProjectDef.State.PROCESSING.getCode());
		queryWrapper.like(StringUtils.isNotBlank(keyword), Project::getName,
				keyword);
		// 处理人是自己的项目
		List<String> projectIdList = this.findByUserId(
				Objects.requireNonNull(UserContextHolder.getUser()).getId(),
				null);
		if (CollectionUtils.isNotEmpty(projectIdList)) {
			queryWrapper.in(Project::getId, projectIdList);
		} else {
			return Page.of(page, size, 0);
		}
		// 维护了仓储期初的项目，不能被选到
		if (CollectionUtils.isNotEmpty(projectIds)) {
			queryWrapper.notIn(Project::getId, projectIds);
		}
		// 默认按更新时间倒序
		queryWrapper.orderByDesc(Project::getUpdatedTime);
		Page<Project> pageResult = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		List<ProjectVo> projectVoList = this.packVo(pageResult.getRecords());
		return PageUtil.getRecordsInfoPage(pageResult, projectVoList);
	}

	@Override
	public List<ProjectRevenueAmountStatisticsVo> projectRevenueAmountStatistics(
			Integer scope, LocalDateTime month, String projectId) {
		if (Objects.isNull(scope) && Objects.isNull(month)) {
			return List.of();
		}

		// 获取日期范围
		List<DashboardScopeDto> dateScope = DashboardDateUtils
				.getDateScope(scope, month);

		List<ProjectRevenueAmountStatisticsVo> result = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(dateScope)) {
			for (DashboardScopeDto dto : dateScope) {
				LocalDateTime beginTime = dto.getBeginTime();
				LocalDateTime endTime = dto.getEndTime();
				ProjectRevenueAmountStatisticsVo projectRevenueAmountStatisticsVo = new ProjectRevenueAmountStatisticsVo();
				List<Payment> paymentList = paymentService
						.findByTypeAndProjectId(PaymentDef.Type.SELL.getCode(),
								projectId, PaymentDef.State.COMPLETED.getCode(),
								List.of(PaymentDef.CostType.GOODS_PAYMENT
										.getCode()),
								Boolean.FALSE, beginTime, endTime);

				projectRevenueAmountStatisticsVo.setDate(
						beginTime.toString() + " " + endTime.toString());
				projectRevenueAmountStatisticsVo
						.setAmount(paymentList.stream().map(Payment::getAmount)
								.reduce(BigDecimal.ZERO, BigDecimal::add)
								.divide(new BigDecimal(10000), 2,
										RoundingMode.HALF_UP));
				result.add(projectRevenueAmountStatisticsVo);

			}
		}
		return result;
	}

	@Override
	public List<ProjectSaleProfitStatisticsVo> projectSaleProfitStatistics(
			Integer scope, LocalDateTime month, String projectId) {
		if (Objects.isNull(scope) && Objects.isNull(month)) {
			return List.of();
		}

		// 获取日期范围
		List<DashboardScopeDto> dateScope = DashboardDateUtils
				.getDateScope(scope, month);

		List<ProjectSaleProfitStatisticsVo> result = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(dateScope)) {
			for (DashboardScopeDto dto : dateScope) {
				LocalDateTime beginTime = dto.getBeginTime();
				LocalDateTime endTime = dto.getEndTime();
				ProjectSaleProfitStatisticsVo projectSaleProfitStatisticsVo = new ProjectSaleProfitStatisticsVo();

				BigDecimal buyAmount = new BigDecimal(0);
				List<Reconciliation> buyReconciliation = reconciliationService
						.findByProjectIdsAndType(List.of(projectId),
								ReconciliationDef.Type.BUY.getCode(), beginTime,
								endTime);
				if (CollectionUtils.isNotEmpty(buyReconciliation)) {
					for (Reconciliation reconciliation : buyReconciliation) {
						buyAmount = buyAmount.add(Objects.nonNull(
								reconciliation.getReconciliationAmount())
										? reconciliation
												.getReconciliationAmount()
										: BigDecimal.ZERO);
					}
				}

				BigDecimal saleAmount = new BigDecimal(0);
				List<Reconciliation> saleReconciliation = reconciliationService
						.findByProjectIdsAndType(List.of(projectId),
								ReconciliationDef.Type.SELL.getCode(),
								beginTime, endTime);
				if (CollectionUtils.isNotEmpty(saleReconciliation)) {
					for (Reconciliation reconciliation : saleReconciliation) {
						saleAmount = saleAmount.add(Objects.nonNull(
								reconciliation.getReconciliationAmount())
										? reconciliation
												.getReconciliationAmount()
										: BigDecimal.ZERO);
					}
				}

				projectSaleProfitStatisticsVo.setDate(
						beginTime.toString() + " " + endTime.toString());
				projectSaleProfitStatisticsVo.setAmount(saleAmount
						.subtract(buyAmount).divide(new BigDecimal(10000), 2,
								RoundingMode.HALF_UP));
				result.add(projectSaleProfitStatisticsVo);

			}
		}
		return result;
	}

	@Override
	public List<ProjectInventoryDistributionStatisticsVo> projectInventoryDistributionStatistics(
			String projectId) {
		List<ProjectInventoryDistributionStatisticsVo> vos = new ArrayList<>();
		this.findOne(projectId).ifPresent(project -> {
			List<WarehouseGoodsInfo> warehouseGoodsInfoList = warehouseGoodsInfoService
					.findByProjectIds(List.of(projectId), List.of(
							InboundDef.InfoType.INBOUND.getCode(),
							InboundDef.InfoType.OUTBOUND.getCode(),
							InboundDef.InfoType.STORAGE_INBOUND.getCode(),
							InboundDef.InfoType.STORAGE_OUTBOUND.getCode()),
							InboundDef.Type.JXC.getCode());

			if (CollectionUtils.isNotEmpty(warehouseGoodsInfoList)) {
				List<String> warehouseIds = warehouseGoodsInfoList.stream()
						.map(WarehouseGoodsInfo::getWarehouseId).distinct()
						.toList();

				Map<String, List<WarehouseGoodsInfo>> map = warehouseGoodsInfoList
						.stream().collect(Collectors.groupingBy(
								WarehouseGoodsInfo::getWarehouseId));

				if (CollectionUtils.isNotEmpty(warehouseIds)) {
					// 仓库map
					Map<String, Warehouse> warehouseMap = warehouseService
							.getIdMap(warehouseIds);

					for (String warehouseId : warehouseIds) {
						BigDecimal inventoryAmount = BigDecimal.ZERO;
						ProjectInventoryDistributionStatisticsVo vo = new ProjectInventoryDistributionStatisticsVo();
						Warehouse warehouse = warehouseMap.get(warehouseId);
						if (Objects.nonNull(warehouse)) {
							vo.setWarehouse(warehouse);
						}

						List<WarehouseGoodsInfo> warehouseGoodsInfos = map
								.get(warehouseId);
						if (CollectionUtils.isNotEmpty(warehouseGoodsInfos)) {
							for (WarehouseGoodsInfo warehouseGoodsInfo : warehouseGoodsInfos) {
								BigDecimal inboundWeight = warehouseGoodsInfo
										.getInboundWeight();
								if (Objects.nonNull(inboundWeight)) {
									switch (InboundDef.InfoType.from(
											warehouseGoodsInfo.getType())) {
										case INBOUND, STORAGE_INBOUND ->
											inventoryAmount = inventoryAmount
													.add(inboundWeight);

										case OUTBOUND, STORAGE_OUTBOUND ->
											inventoryAmount = inventoryAmount
													.subtract(inboundWeight);
									}
								}
							}
						}
						vo.setInventoryAmount(inventoryAmount);
						vos.add(vo);
					}
				}
			}
		});
		vos.sort(Comparator.comparing(
				ProjectInventoryDistributionStatisticsVo::getInventoryAmount)
				.reversed());
		return vos;
	}

	@Override
	public List<ProjectModelInventoryDistributionStatisticsVo> projectModelInventoryDistributionStatistics(
			String projectId) {
		List<ProjectModelInventoryDistributionStatisticsVo> vos = new ArrayList<>();
		this.findOne(projectId).ifPresent(project -> {

			List<WarehouseGoodsInfo> warehouseGoodsInfoList = warehouseGoodsInfoService
					.findByProjectIds(List.of(projectId), List.of(
							InboundDef.InfoType.INBOUND.getCode(),
							InboundDef.InfoType.OUTBOUND.getCode(),
							InboundDef.InfoType.STORAGE_INBOUND.getCode(),
							InboundDef.InfoType.STORAGE_OUTBOUND.getCode()),
							InboundDef.Type.JXC.getCode());

			if (CollectionUtils.isNotEmpty(warehouseGoodsInfoList)) {
				List<String> modelList = warehouseGoodsInfoList.stream()
						.map(WarehouseGoodsInfo::getModel).distinct().toList();

				Map<String, List<WarehouseGoodsInfo>> map = warehouseGoodsInfoList
						.stream().collect(Collectors
								.groupingBy(WarehouseGoodsInfo::getModel));

				if (CollectionUtils.isNotEmpty(modelList)) {
					for (String model : modelList) {
						BigDecimal inventoryAmount = BigDecimal.ZERO;
						ProjectModelInventoryDistributionStatisticsVo vo = new ProjectModelInventoryDistributionStatisticsVo();
						vo.setModel(model);

						List<WarehouseGoodsInfo> warehouseGoodsInfos = map
								.get(model);
						if (CollectionUtils.isNotEmpty(warehouseGoodsInfos)) {
							for (WarehouseGoodsInfo warehouseGoodsInfo : warehouseGoodsInfos) {
								BigDecimal inboundWeight = warehouseGoodsInfo
										.getInboundWeight();
								if (Objects.nonNull(inboundWeight)) {
									switch (InboundDef.InfoType.from(
											warehouseGoodsInfo.getType())) {
										case INBOUND, STORAGE_INBOUND ->
											inventoryAmount = inventoryAmount
													.add(inboundWeight);

										case OUTBOUND, STORAGE_OUTBOUND ->
											inventoryAmount = inventoryAmount
													.subtract(inboundWeight);
									}
								}
							}
						}
						vo.setInventoryAmount(inventoryAmount.setScale(2,
								RoundingMode.HALF_UP));
						vos.add(vo);
					}
				}
			}
		});

		vos.sort(Comparator.comparing(
				ProjectModelInventoryDistributionStatisticsVo::getInventoryAmount)
				.reversed());
		return vos;
	}

	@Override
	public Optional<ProjectCountVo> staticsProject(boolean isDeal,
			boolean isManage) {
		ProjectCountVo projectCountVo = new ProjectCountVo();
		projectCountVo.setExpected(0L);
		projectCountVo.setToBeFinished(0L);
		if (isDeal) {
			// 统计逾期项目
			List<Accounts> accountsList = accountsService
					.findByStatus(AccountsDef.Status.DELAY.getCode());
			List<String> projectIds = accountsList.stream()
					.map(Accounts::getProjectId).distinct().toList();
			List<Project> projects = this.findByIds(projectIds);
			projectCountVo.setExpected(projects.stream()
					.filter(project -> project.getSupervisorId()
							.equals(Objects
									.requireNonNull(UserContextHolder.getUser())
									.getId())
							|| project.getProjectManagerIds()
									.contains(Objects
											.requireNonNull(
													UserContextHolder.getUser())
											.getId()))
					.count());
		}
		if (isManage) {
			// 统计项目待完结
			LambdaQueryWrapper<Project> queryWrapper = Wrappers
					.lambdaQuery(Project.class);
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(Project::getState,
					ProjectDef.State.PROCESSING.getCode());
			projectCountVo
					.setToBeFinished(repository.selectCount(queryWrapper));
		}
		return Optional.of(projectCountVo);
	}

	@Override
	public List<Project> findByTenantId(Long tenantId) {
		LambdaQueryWrapper<Project> queryWrapper = Wrappers
				.lambdaQuery(Project.class);
		queryWrapper.eq(Project::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(Objects.nonNull(tenantId), Project::getTenantId,
				tenantId);
		return repository.selectList(queryWrapper);
	}

	@History(success = HistoryDef.INVENTORY_PROJECT_APPLY_FINISH, bizNo = "{{#resource.getId()}}", module = HistoryDef.INVENTORY_PROJECT)
	@Override
	public void applyFinish(Project resource) {
		super.updateAllProperties(resource);
	}

	@History(success = HistoryDef.INVENTORY_PROJECT_CONFIRM_FINISH, bizNo = "{{#resource.getId()}}", module = HistoryDef.INVENTORY_PROJECT)
	@Override
	public void finish(Project resource) {
		super.updateAllProperties(resource);
	}

	// 封装vo
	private List<ProjectVo> packVo(List<Project> records) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		List<ProjectVo> projectVoList = new ArrayList<>();
		// 获取项目的id列表
		List<String> ids = records.stream().map(Project::getId).toList();
		// 根据项目id列表查询项目已逾期的应收款数据列表
		List<Accounts> accountsList = accountsService.findByProjectIdsAndStatus(
				ids, AccountsDef.Status.DELAY.getCode());
		// 将应收款数据根据项目id进行分组
		Map<String, List<Accounts>> acccountsMap = accountsList.stream()
				.collect(Collectors.groupingBy(Accounts::getProjectId));
		// 获取项目的id列表
		List<Long> goodsIds = records.stream().map(Project::getGoodsId)
				.toList();
		// 货物集合 key-货物ID
		Map<Long, Goods> goodsMap = goodsService.getIdMap(goodsIds);
		for (Project project : records) {
			ProjectVo projectVo = new ProjectVo();
			projectVo.setProject(project);
			List<Accounts> accounts = acccountsMap.get(project.getId());
			if (ProjectDef.State.PROCESSING.match(project.getState())) {
				// 状态为履约中才需要显示 是否逾期
				if (CollectionUtils.isNotEmpty(accounts)) {
					projectVo.setDelay(CommonDef.Symbol.YES.getCode());
				} else {
					projectVo.setDelay(CommonDef.Symbol.NO.getCode());
				}
			}
			List<DealingsEnterprise> suppliers = dealingsEnterpriseService
					.findByIds(project.getSupplierIds());
			projectVo.setSuppliers(suppliers);
			List<DealingsEnterprise> customers = dealingsEnterpriseService
					.findByIds(project.getCustomerIds());
			projectVo.setCustomers(customers);
			// 货物信息
			if (Objects.nonNull(project.getGoodsId())) {
				Goods goods = goodsMap.get(project.getGoodsId());
				if (Objects.nonNull(goods)) {
					projectVo.setGoods(goods);
				}
			}
			projectVoList.add(projectVo);
		}
		return projectVoList;
	}

	// 封装负责人vo
	private ProjectSpecialVo packSupervisor(Long supervisorId) {
		ProjectSpecialVo projectSpecialVo = new ProjectSpecialVo();
		userService.findOne(supervisorId).ifPresent(projectSpecialVo::setUser);
		projectSpecialVo
				.setCount(
						(long) this
								.findByUserId(supervisorId,
										ProjectDef.State.PROCESSING.getCode())
								.size());
		return projectSpecialVo;
	}

	// 封装项目经理vo
	private List<ProjectSpecialVo> packProjectManagers(
			List<Long> projectManagerIds) {
		List<ProjectSpecialVo> projectSpecialVoList = new ArrayList<>();
		List<User> users = userService.findByIds(projectManagerIds);
		for (User user : users) {
			ProjectSpecialVo projectSpecialVo = new ProjectSpecialVo();
			projectSpecialVo.setUser(user);
			projectSpecialVo
					.setCount((long) this
							.findByUserId(user.getId(),
									ProjectDef.State.PROCESSING.getCode())
							.size());
			projectSpecialVoList.add(projectSpecialVo);
		}
		return projectSpecialVoList;
	}

	/**
	 *
	 * @param records
	 * @return
	 */
	private List<ProjectProfitRankVo> packRankVo(List<Project> records) {
		if (CollectionUtils.isEmpty(records)) {
			return List.of();
		}
		// 项目ID集合
		List<String> projectIds = records.stream().map(Project::getId).toList();
		// 根据项目id查询所有期初数据
		List<ProjectInception> projectInceptions = projectInceptionService
				.findByProjectIds(projectIds);
		// 根据 projectId 进行分组，并累加项目采购期初的对账总金额
		Map<String, BigDecimal> projectInceptionPurchaseMap = projectInceptions
				.stream()
				.filter(item -> Objects
						.nonNull(item.getTotalReconciledPurchaseAmount()))
				.collect(Collectors.groupingBy(ProjectInception::getProjectId,
						Collectors.reducing(BigDecimal.ZERO,
								ProjectInception::getTotalReconciledPurchaseAmount,
								BigDecimal::add)));
		// 根据 projectId 进行分组，并累加项目销售期初的对账总金额
		Map<String, BigDecimal> projectInceptionSaleMap = projectInceptions
				.stream()
				.filter(item -> Objects
						.nonNull(item.getTotalReconciledSalesAmount()))
				.collect(Collectors.groupingBy(ProjectInception::getProjectId,
						Collectors.reducing(BigDecimal.ZERO,
								ProjectInception::getTotalReconciledSalesAmount,
								BigDecimal::add)));
		// 累计采购成本：该项目采购管理中对账单对账金额之和。
		List<Reconciliation> buyPaymentList = reconciliationService
				.findByProjectIdsAndType(projectIds,
						ReconciliationDef.Type.BUY.getCode());
		// 根据 projectId 进行分组，并累加金额
		Map<String, BigDecimal> buyPaymentMap = buyPaymentList.stream()
				.filter(item -> Objects.nonNull(item.getReconciliationAmount()))
				.collect(Collectors.groupingBy(Reconciliation::getProjectId,
						Collectors.reducing(BigDecimal.ZERO,
								Reconciliation::getReconciliationAmount,
								BigDecimal::add)));

		// 累计销售金额：该项目销售管理中对账状态为对账完成的对账金额之和
		List<Reconciliation> sellPaymentList = reconciliationService
				.findByProjectIdsAndType(projectIds,
						ReconciliationDef.Type.SELL.getCode());
		Map<String, BigDecimal> sellPaymentMap = sellPaymentList.stream()
				.filter(item -> ReconciliationDef.State.FINISHED
						.match(item.getState()))
				.filter(item -> Objects.nonNull(item.getReconciliationAmount()))
				.collect(Collectors.groupingBy(Reconciliation::getProjectId,
						Collectors.reducing(BigDecimal.ZERO,
								Reconciliation::getReconciliationAmount,
								BigDecimal::add)));

		return records.stream().map(item -> {
			ProjectProfitRankVo vo = new ProjectProfitRankVo();
			vo.setProject(item);
			// 该项目采购管理中对账单对账金额之和。
			BigDecimal accumulatedProcurementCost = Objects
					.isNull(buyPaymentMap.get(item.getId())) ? BigDecimal.ZERO
							: buyPaymentMap.get(item.getId());
			// 累计 项目采购期初的对账总金额
			BigDecimal totalReconciledPurchaseAmount = Objects
					.isNull(projectInceptionPurchaseMap.get(item.getId()))
							? BigDecimal.ZERO
							: projectInceptionPurchaseMap.get(item.getId());
			// 累计采购成本=该项目采购期初的对账总金额+该项目采购管理中对账单对账金额之和。
			accumulatedProcurementCost = accumulatedProcurementCost
					.add(totalReconciledPurchaseAmount);
			vo.setAccumulatedProcurementCost(accumulatedProcurementCost);
			// 该项目销售管理中对账状态为对账完成的对账金额之和
			BigDecimal accumulatedSalesAmount = Objects
					.isNull(sellPaymentMap.get(item.getId())) ? BigDecimal.ZERO
							: sellPaymentMap.get(item.getId());
			// 累计 该项目销售期初的对账总金额
			BigDecimal totalReconciledSalesAmount = Objects.isNull(
					projectInceptionSaleMap.get(item.getId())) ? BigDecimal.ZERO
							: projectInceptionSaleMap.get(item.getId());
			// 累计销售金额：该项目销售期初的对账总金额+该项目销售管理中对账状态为对账完成的对账金额之和
			accumulatedSalesAmount = accumulatedSalesAmount
					.add(totalReconciledSalesAmount);
			vo.setAccumulatedSalesAmount(accumulatedSalesAmount);
			// 累计销售毛利=累计销售金额-累计采购成本
			vo.setAccumulatedSalesProfit(accumulatedSalesAmount
					.subtract(accumulatedProcurementCost));
			return vo;
		}).sorted(Comparator
				.comparing(ProjectProfitRankVo::getAccumulatedSalesProfit)
				.reversed()).toList();
	}

	/**
	 * 组装vo
	 *
	 * @param warehouseList
	 * @return
	 */
	private List<WarehouseVo> packWarehouseVo(List<Warehouse> warehouseList,
			List<Storage> storages) {
		// 库位
		Map<String, List<Storage>> StorageMap;
		// 找出所有仓库id
		List<String> warehouseIds = warehouseList.stream().map(Warehouse::getId)
				.toList();
		if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
				.isEmpty(warehouseIds)) {
			StorageMap = new HashMap<>();
		} else {
			// 将库位根据仓库id进行分组
			StorageMap = storages.stream().collect(Collectors
					.groupingBy(Storage::getWarehouseId, Collectors.toList()));
		}

		return warehouseList.stream().map(warehouse -> {
			WarehouseVo vo = new WarehouseVo();
			vo.setWarehouse(warehouse);
			if (Objects.nonNull(warehouse.getId())) {
				// 设置仓库的库位信息
				List<Storage> storageList = StorageMap.get(warehouse.getId());
				if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils
						.isNotEmpty(storageList)) {
					storageList = storageList.stream().sorted(Comparator
							.comparing(Storage::getCreatedTime).reversed())
							.collect(Collectors.toList());
					vo.setStorageList(storageList);
				}
			}
			return vo;
		}).toList();
	}

	private List<PledgeInfo> convertPledgeInfo(String data) {
		Gson gson = new GsonBuilder().registerTypeAdapter(LocalDateTime.class,
				new LocalDateTimeAdapter()).create();
		Type listType = new TypeToken<List<PledgeInfo>>() {
		}.getType();
		return gson.fromJson(data, listType);
	}

	@Transactional(rollbackFor = Exception.class)
	protected void handleHistory(Project oldProject, Project newProject,
			List<ProjectItem> projectItems,
			List<ProjectItem> oldProjectItemList,
			List<ProjectItem> newProjectItemList) {
		// 记录到 操作历史 表中去
		OperationHistory operationHistory = new OperationHistory();
		ArrayChangeInfo list = new ArrayChangeInfo();
		// 项目名称
		if (!Objects.equals(oldProject.getName(), newProject.getName())) {
			String oldValue = "--";
			if (StringUtils.isNotBlank(oldProject.getName())) {
				oldValue = oldProject.getName();
			}
			String newValue = "--";
			if (StringUtils.isNotBlank(newProject.getName())) {
				newValue = newProject.getName();
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("项目名称");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 项目有效期
		if ((!Objects.equals(oldProject.getValidBeginTime(),
				newProject.getValidBeginTime()))
				|| (!Objects.equals(oldProject.getValidEndTime(),
						newProject.getValidEndTime()))) {
			String oldFormatStr1 = "--";
			String oldFormatStr2 = "--";
			String newFormatStr1 = "--";
			String newFormatStr2 = "--";
			DateTimeFormatter formatter = DateTimeFormatter
					.ofPattern("yyyy-MM-dd");
			if (Objects.nonNull(oldProject.getValidBeginTime())) {
				LocalDate date1 = oldProject.getValidBeginTime().toLocalDate();
				oldFormatStr1 = date1.format(formatter);
			}
			if (Objects.nonNull(oldProject.getValidEndTime())) {
				LocalDate date1 = oldProject.getValidEndTime().toLocalDate();
				oldFormatStr2 = date1.format(formatter);
			}
			if (Objects.nonNull(newProject.getValidBeginTime())) {
				LocalDate date1 = newProject.getValidBeginTime().toLocalDate();
				newFormatStr1 = date1.format(formatter);
			}
			if (Objects.nonNull(newProject.getValidEndTime())) {
				LocalDate date1 = newProject.getValidEndTime().toLocalDate();
				newFormatStr2 = date1.format(formatter);
			}

			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("项目有效期");
			changeInfo.setOldValue(oldFormatStr1 + " 至 " + oldFormatStr2);
			changeInfo.setNewValue(newFormatStr1 + " 至 " + newFormatStr2);
			list.add(changeInfo);
		}
		// 项目资金敞口上限
		BigDecimal oldProjectMaxLimit = this
				.processBigDecimal(oldProject.getProjectMaxLimit());
		if (!Objects.equals(oldProjectMaxLimit,
				newProject.getProjectMaxLimit())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getProjectMaxLimit())) {
				oldValue = oldProjectMaxLimit + "元";
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getProjectMaxLimit())) {
				newValue = newProject.getProjectMaxLimit() + "元";
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("项目资金敞口上限");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 项目预估年化收益
		BigDecimal oldProjectReturnRate = this
				.processBigDecimal(oldProject.getProjectReturnRate());
		if (!Objects.equals(oldProjectReturnRate,
				newProject.getProjectReturnRate())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getProjectReturnRate())) {
				oldValue = oldProjectReturnRate + "%";
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getProjectReturnRate())) {
				newValue = newProject.getProjectReturnRate() + "%";
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("项目预估年化收益");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 上游供应商
		if (!Objects.equals(oldProject.getSupplierIds(),
				newProject.getSupplierIds())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getSupplierIds())) {
				oldValue = String.join("、", oldProject.getSupplierNames());
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getSupplierIds())) {
				newValue = String.join("、", newProject.getSupplierNames());
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("上游供应商");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 上游供应商结算方式
		if (!Objects.equals(oldProject.getSupplierSettleWay(),
				newProject.getSupplierSettleWay())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getSupplierSettleWay())) {
				oldValue = ProjectDef.SettleWay
						.from(oldProject.getSupplierSettleWay()).getName();
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getSupplierSettleWay())) {
				newValue = ProjectDef.SettleWay
						.from(newProject.getSupplierSettleWay()).getName();
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("上游供应商结算方式");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 上游预付货款
		BigDecimal oldSupplierPrePayment = this
				.processBigDecimal(oldProject.getSupplierPrePayment());
		if (!Objects.equals(oldSupplierPrePayment,
				newProject.getSupplierPrePayment())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getSupplierPrePayment())) {
				oldValue = oldSupplierPrePayment + "元";
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getSupplierPrePayment())) {
				newValue = newProject.getSupplierPrePayment() + "元";
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("上游预付货款");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 上游预付货款比例
		BigDecimal oldSupplierPrePaymentRatio = this
				.processBigDecimal(oldProject.getSupplierPrePaymentRatio());
		if (!Objects.equals(oldSupplierPrePaymentRatio,
				newProject.getSupplierPrePaymentRatio())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getSupplierPrePaymentRatio())) {
				oldValue = oldSupplierPrePaymentRatio + "%";
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getSupplierPrePaymentRatio())) {
				newValue = newProject.getSupplierPrePaymentRatio() + "%";
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("上游预付货款比例");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 上游账期
		if (!Objects.equals(oldProject.getSupplierSettlePeriod(),
				newProject.getSupplierSettlePeriod())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getSupplierSettlePeriod())) {
				oldValue = oldProject.getSupplierSettlePeriod() + "天";
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getSupplierSettlePeriod())) {
				newValue = newProject.getSupplierSettlePeriod() + "天";
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("上游账期");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 下游采购方
		if (!Objects.equals(oldProject.getCustomerIds(),
				newProject.getCustomerIds())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getCustomerIds())) {
				oldValue = String.join("、", oldProject.getCustomerNames());
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getCustomerIds())) {
				newValue = String.join("、", newProject.getCustomerNames());
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("下游采购方");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 下游采购方结算方式
		if (!Objects.equals(oldProject.getCustomerSettleWay(),
				newProject.getCustomerSettleWay())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getCustomerSettleWay())) {
				oldValue = ProjectDef.SettleWay
						.from(oldProject.getCustomerSettleWay()).getName();
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getCustomerSettleWay())) {
				newValue = ProjectDef.SettleWay
						.from(newProject.getCustomerSettleWay()).getName();
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("下游采购方结算方式");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 是否存在仓储
		if (!Objects.equals(oldProject.getIsExistStorage(),
				newProject.getIsExistStorage())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getIsExistStorage())) {
				oldValue = ProjectDef.IsExistStorage
						.from(oldProject.getIsExistStorage()).getName();
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getCustomerSettleWay())) {
				newValue = ProjectDef.IsExistStorage
						.from(newProject.getIsExistStorage()).getName();
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("是否存在仓储");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 下游预付货款
		BigDecimal oldCustomerPrePayment = this
				.processBigDecimal(oldProject.getCustomerPrePayment());
		if (!Objects.equals(oldCustomerPrePayment,
				newProject.getCustomerPrePayment())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getCustomerPrePayment())) {
				oldValue = oldCustomerPrePayment + "元";
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getCustomerPrePayment())) {
				newValue = newProject.getCustomerPrePayment() + "元";
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("下游预付货款");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 下游预付货款比例
		BigDecimal oldCustomerPrePaymentRatio = this
				.processBigDecimal(oldProject.getCustomerPrePaymentRatio());
		if (!Objects.equals(oldCustomerPrePaymentRatio,
				newProject.getCustomerPrePaymentRatio())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getCustomerPrePaymentRatio())) {
				oldValue = oldCustomerPrePaymentRatio + "%";
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getCustomerPrePaymentRatio())) {
				newValue = newProject.getCustomerPrePaymentRatio() + "%";
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("下游预付货款比例");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 下游账期
		if (!Objects.equals(oldProject.getCustomerSettlePeriod(),
				newProject.getCustomerSettlePeriod())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getCustomerSettlePeriod())) {
				oldValue = oldProject.getCustomerSettlePeriod() + "天";
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getCustomerSettlePeriod())) {
				newValue = newProject.getCustomerSettlePeriod() + "天";
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("下游账期");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 仓库/库位
		if ((!Objects.equals(oldProject.getWarehouseIds(),
				newProject.getWarehouseIds()))
				|| (!Objects.equals(oldProject.getStorageIds(),
						newProject.getStorageIds()))) {
			// 设置仓库信息
			List<Warehouse> oldWarehouseList = warehouseService
					.findByIds(oldProject.getWarehouseIds());
			List<Storage> oldStorageList = storageService
					.findByIds(oldProject.getStorageIds());
			// 封装仓库Vo
			List<WarehouseVo> oldWarehouseVoList = this
					.packWarehouseVo(oldWarehouseList, oldStorageList);
			StringBuilder sb = new StringBuilder();
			for (WarehouseVo warehouseVo : oldWarehouseVoList) {
				if (Objects.nonNull(warehouseVo.getWarehouse())) {
					sb.append(warehouseVo.getWarehouse().getName());
				}
				if (CollectionUtils.isNotEmpty(warehouseVo.getStorageList())) {
					sb.append(" | ");
					for (Storage storage : warehouseVo.getStorageList()) {
						sb.append(storage.getName());
						if (!warehouseVo.getStorageList()
								.get(warehouseVo.getStorageList().size() - 1)
								.equals(storage)) {
							sb.append("、");
						}
					}
				}
				if (!oldWarehouseVoList.get(oldWarehouseVoList.size() - 1)
						.equals(warehouseVo)) {
					sb.append("；");
				}
			}
			String oldValue;
			oldValue = sb.toString();
			// 设置仓库信息
			List<Warehouse> newWarehouseList = warehouseService
					.findByIds(newProject.getWarehouseIds());
			List<Storage> newStorageList = storageService
					.findByIds(newProject.getStorageIds());
			// 封装仓库Vo
			List<WarehouseVo> newWarehouseVoList = this
					.packWarehouseVo(newWarehouseList, newStorageList);
			StringBuilder sb1 = new StringBuilder();
			for (WarehouseVo warehouseVo : newWarehouseVoList) {
				if (Objects.nonNull(warehouseVo.getWarehouse())) {
					sb1.append(warehouseVo.getWarehouse().getName());
				}
				if (CollectionUtils.isNotEmpty(warehouseVo.getStorageList())) {
					sb1.append(" | ");
					for (Storage storage : warehouseVo.getStorageList()) {
						sb1.append(storage.getName());
						if (!warehouseVo.getStorageList()
								.get(warehouseVo.getStorageList().size() - 1)
								.equals(storage)) {
							sb1.append("、");
						}
					}
				}
				if (!newWarehouseVoList.get(newWarehouseVoList.size() - 1)
						.equals(warehouseVo)) {
					sb1.append("；");
				}
			}
			String newValue;
			newValue = sb1.toString();
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("仓库/库位");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 出库条件
		if (!Objects.equals(oldProject.getOutCondition(),
				newProject.getOutCondition())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getOutCondition())) {
				oldValue = ProjectDef.OutCondition
						.from(oldProject.getOutCondition()).getName();
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getOutCondition())) {
				newValue = ProjectDef.OutCondition
						.from(newProject.getOutCondition()).getName();
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("出库条件");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 付款比例
		BigDecimal oldPaymentRatio = this
				.processBigDecimal(oldProject.getPaymentRatio());
		if (!Objects.equals(oldPaymentRatio, newProject.getPaymentRatio())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getPaymentRatio())) {
				oldValue = oldPaymentRatio + "%";
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getPaymentRatio())) {
				newValue = newProject.getPaymentRatio() + "%";
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("付款比例");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 余款账期
		if (!Objects.equals(oldProject.getRemainPaymentPeriod(),
				newProject.getRemainPaymentPeriod())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getRemainPaymentPeriod())) {
				oldValue = oldProject.getRemainPaymentPeriod() + "天";
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getRemainPaymentPeriod())) {
				newValue = newProject.getRemainPaymentPeriod() + "天";
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("余款账期");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		BigDecimal oldInventoryControlRatio = this
				.processBigDecimal(oldProject.getInventoryControlRatio());
		BigDecimal oldInventoryAmount = this
				.processBigDecimal(oldProject.getInventoryAmount());
		// 控货比要求
		if ((!Objects.equals(oldProject.getControlRatioWay(),
				newProject.getControlRatioWay()))
				|| (!Objects.equals(oldInventoryControlRatio,
						newProject.getInventoryControlRatio()))
				|| (!Objects.equals(oldInventoryAmount,
						newProject.getInventoryAmount()))) {

			String oldStr1 = "--";
			String oldStr2 = "--";
			String newStr1 = "--";
			String newStr2 = "--";
			if (Objects.nonNull(oldProject.getControlRatioWay())) {
				oldStr1 = ProjectDef.ControlRatioWay
						.from(oldProject.getControlRatioWay()).getName();
				if (ProjectDef.ControlRatioWay.BY_QUANTITY
						.match(oldProject.getControlRatioWay())) {
					if (Objects.nonNull(oldInventoryControlRatio)) {
						oldStr2 = oldInventoryControlRatio + "%";
					} else {
						oldStr2 = "--";
					}
				} else {
					if (Objects.nonNull(oldInventoryAmount)) {
						oldStr2 = oldInventoryAmount + "元";
					} else {
						oldStr2 = "--";
					}
				}
			}
			if (Objects.nonNull(newProject.getControlRatioWay())) {
				newStr1 = ProjectDef.ControlRatioWay
						.from(newProject.getControlRatioWay()).getName();
				if (ProjectDef.ControlRatioWay.BY_QUANTITY
						.match(newProject.getControlRatioWay())) {
					if (Objects
							.nonNull(newProject.getInventoryControlRatio())) {
						newStr2 = newProject.getInventoryControlRatio() + "%";
					} else {
						newStr2 = "--";
					}
				} else {
					if (Objects.nonNull(newProject.getInventoryAmount())) {
						newStr2 = newProject.getInventoryAmount() + "元";
					} else {
						newStr2 = "--";
					}
				}
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("控货比要求");
			changeInfo.setOldValue(oldStr1 + "≥" + oldStr2);
			changeInfo.setNewValue(newStr1 + "≥" + newStr2);
			list.add(changeInfo);
		}
		// 项目备注
		if (!Objects.equals(oldProject.getIntroduction(),
				newProject.getIntroduction())) {
			String oldValue = "--";
			if (StringUtils.isNotBlank(oldProject.getIntroduction())) {
				oldValue = oldProject.getIntroduction();
			}
			String newValue = "--";
			if (StringUtils.isNotBlank(newProject.getIntroduction())) {
				newValue = newProject.getIntroduction();
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("项目备注");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 项目负责人
		if (!Objects.equals(oldProject.getSupervisorId(),
				newProject.getSupervisorId())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getSupervisorId())) {
				oldValue = oldProject.getSupervisorName();
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getSupervisorId())) {
				newValue = newProject.getSupervisorName();
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("项目负责人");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 项目经理
		if (!Objects.equals(oldProject.getProjectManagerIds(),
				newProject.getProjectManagerIds())) {
			String oldValue = "--";
			if (Objects.nonNull(oldProject.getProjectManagerIds())) {
				oldValue = String.join("、",
						oldProject.getProjectManagerNames());
			}
			String newValue = "--";
			if (Objects.nonNull(newProject.getProjectManagerIds())) {
				newValue = String.join("、",
						newProject.getProjectManagerNames());
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("项目经理");
			changeInfo.setOldValue(oldValue);
			changeInfo.setNewValue(newValue);
			list.add(changeInfo);
		}
		// 可提货额
		// 创建Gson实例
		Gson gson = getGson();
		// 将List转换为JSON字符串
		String json = gson.toJson(oldProjectItemList);
		String json2 = gson.toJson(newProjectItemList);
		System.out.println(json);
		System.out.println(json2);
		if (!Objects.equals(oldProject.getFormula(), newProject.getFormula())
				|| (!json.equals(json2))
				|| CollectionUtils.isNotEmpty(projectItems)) {
			StringBuilder oldValue1 = new StringBuilder();
			StringBuilder newValue1 = new StringBuilder();
			if (CollectionUtils.isNotEmpty(oldProjectItemList)) {
				StringBuilder oldValue = new StringBuilder();
				for (ProjectItem projectItem : oldProjectItemList) {
					StringBuilder oldPaymentValue = new StringBuilder("货款");
					StringBuilder oldOrderAmountValue = new StringBuilder(
							"订单金额");
					StringBuilder oldRecAmountValue = new StringBuilder("对账金额");
					StringBuilder oldOrderDepositValue = new StringBuilder(
							"订单保证金");
					StringBuilder oldDepositValue = new StringBuilder("履约保证金");
					StringBuilder oldFrozenAmountValue = new StringBuilder(
							"冻结金额");
					StringBuilder oldPledgeGoodsValue = new StringBuilder(
							"抵质押物");
					StringBuilder oldDefinitionValue = new StringBuilder("自定义");
					// 货款
					if (ProjectDef.Item.PAYMENT.match(projectItem.getItem())) {
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldPaymentValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldPaymentValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldPaymentValue);
					}
					// 订单金额
					if (ProjectDef.Item.ORDER_AMOUNT
							.match(projectItem.getItem())) {
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldOrderAmountValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldOrderAmountValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldOrderAmountValue);
					}
					if (ProjectDef.Item.REC_AMOUNT
							.match(projectItem.getItem())) {
						// 订单金额
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldRecAmountValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldRecAmountValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldRecAmountValue);
					}
					if (ProjectDef.Item.ORDER_DEPOSIT
							.match(projectItem.getItem())) {
						// 订单金额
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (Objects.nonNull(projectItem.getAmount())) {
							oldOrderDepositValue.append(projectItem.getAmount())
									.append("元");
						}
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldOrderDepositValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldOrderDepositValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldOrderDepositValue);
					}
					if (ProjectDef.Item.DEPOSIT.match(projectItem.getItem())) {
						// 订单金额
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (Objects.nonNull(projectItem.getAmount())) {
							oldDepositValue.append(projectItem.getAmount())
									.append("元");
						}
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldDepositValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldDepositValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldDepositValue);
					}
					if (ProjectDef.Item.FROZEN_AMOUNT
							.match(projectItem.getItem())) {
						// 订单金额
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (Objects.nonNull(projectItem.getAmount())) {
							oldFrozenAmountValue.append(projectItem.getAmount())
									.append("元");
						}
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldFrozenAmountValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldFrozenAmountValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldFrozenAmountValue);
					}
					if (ProjectDef.Item.PLEDGE_GOODS
							.match(projectItem.getItem())) {
						// 订单金额
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (Objects.nonNull(projectItem.getAmount())) {
							oldPledgeGoodsValue.append(projectItem.getAmount())
									.append("元");
						}
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldPledgeGoodsValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldPledgeGoodsValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldPledgeGoodsValue);
					}
					if (ProjectDef.Item.DEFINITION
							.match(projectItem.getItem())) {
						// 订单金额
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (Objects.nonNull(projectItem.getAmount())) {
							oldDefinitionValue.append(projectItem.getAmount())
									.append("元");
						}
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldDefinitionValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldDefinitionValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldDefinitionValue);
					}
				}
				oldValue1 = new StringBuilder(oldValue.toString());
			}
			// 循环新的可提货公式
			if (CollectionUtils.isNotEmpty(newProjectItemList)) {
				StringBuilder oldValue = new StringBuilder();
				for (ProjectItem projectItem : newProjectItemList) {
					StringBuilder oldPaymentValue = new StringBuilder("货款");
					StringBuilder oldOrderAmountValue = new StringBuilder(
							"订单金额");
					StringBuilder oldRecAmountValue = new StringBuilder("对账金额");
					StringBuilder oldOrderDepositValue = new StringBuilder(
							"订单保证金");
					StringBuilder oldDepositValue = new StringBuilder("履约保证金");
					StringBuilder oldFrozenAmountValue = new StringBuilder(
							"冻结金额");
					StringBuilder oldPledgeGoodsValue = new StringBuilder(
							"抵质押物");
					StringBuilder oldDefinitionValue = new StringBuilder("自定义");
					// 货款
					if (ProjectDef.Item.PAYMENT.match(projectItem.getItem())) {
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldPaymentValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldPaymentValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldPaymentValue);
					}
					// 订单金额
					if (ProjectDef.Item.ORDER_AMOUNT
							.match(projectItem.getItem())) {
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldOrderAmountValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldOrderAmountValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldOrderAmountValue);
					}
					if (ProjectDef.Item.REC_AMOUNT
							.match(projectItem.getItem())) {
						// 订单金额
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldRecAmountValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldRecAmountValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldRecAmountValue);
					}
					if (ProjectDef.Item.ORDER_DEPOSIT
							.match(projectItem.getItem())) {
						// 订单金额
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (Objects.nonNull(projectItem.getAmount())) {
							oldOrderDepositValue.append(projectItem.getAmount())
									.append("元");
						}
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldOrderDepositValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldOrderDepositValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldOrderDepositValue);
					}
					if (ProjectDef.Item.DEPOSIT.match(projectItem.getItem())) {
						// 订单金额
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (Objects.nonNull(projectItem.getAmount())) {
							oldDepositValue.append(projectItem.getAmount())
									.append("元");
						}
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldDepositValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldDepositValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldDepositValue);
					}
					if (ProjectDef.Item.FROZEN_AMOUNT
							.match(projectItem.getItem())) {
						// 订单金额
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (Objects.nonNull(projectItem.getAmount())) {
							oldFrozenAmountValue.append(projectItem.getAmount())
									.append("元");
						}
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldFrozenAmountValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldFrozenAmountValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldFrozenAmountValue);
					}
					if (ProjectDef.Item.PLEDGE_GOODS
							.match(projectItem.getItem())) {
						// 订单金额
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (Objects.nonNull(projectItem.getAmount())) {
							oldPledgeGoodsValue.append(projectItem.getAmount())
									.append("元");
						}
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldPledgeGoodsValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldPledgeGoodsValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldPledgeGoodsValue);
					}
					if (ProjectDef.Item.DEFINITION
							.match(projectItem.getItem())) {
						// 订单金额
						oldValue.append(ProjectDef.Direction
								.from(projectItem.getDirection()).getSymbol());
						if (Objects.nonNull(projectItem.getAmount())) {
							oldDefinitionValue.append(projectItem.getAmount())
									.append("元");
						}
						if (StringUtils.isNotBlank(projectItem.getRemark())) {
							oldDefinitionValue.append("（备注")
									.append(projectItem.getRemark())
									.append("）");
						}
						if (Objects.nonNull(projectItem.getFileId())) {
							oldDefinitionValue.append("<")
									.append(projectItem.getFileId())
									.append(">");
						}
						oldValue.append(oldDefinitionValue);
					}
				}
				newValue1 = oldValue;
			}
			ChangeInfo changeInfo = new ChangeInfo();
			changeInfo.setKey("可提货额");
			changeInfo.setOldValue(oldValue1.toString());
			changeInfo.setNewValue(newValue1.toString());
			list.add(changeInfo);
		}
		DateTimeFormatter formatter = DateTimeFormatter
				.ofPattern("yyyy-MM-dd HH:mm");
		String format = LocalDateTime.now().format(formatter);
		AtomicReference<String> name = new AtomicReference<>();
		// 管理后台
		if (Objects.nonNull(UserContextHolder.getUser())) {
			userService.findOne(UserContextHolder.getUser().getId())
					.ifPresent(user -> {
						operationHistory.setOperatorId(user.getId());
						name.set(user.getName());
					});
		}
		Set<String> disposes = new HashSet<>();
		String action = "";
		if (Objects.nonNull(name.get())) {
			action = format + " " + "由" + " " + name.get() + " " + "修改";
			if (StringUtils.isNotBlank(name.get())) {
				disposes.add(name.get());
			}
		}
		operationHistory.setOperationAction(action);
		operationHistory.setOperationModule(
				Integer.valueOf(HistoryDef.INVENTORY_PROJECT));
		operationHistory.setDisposes(String.join(",", disposes));
		if (!list.isEmpty()) {
			operationHistory.setOperationContent(list);
		}
		// 业务ID
		operationHistory.setBizNo(newProject.getId());
		operationHistoryService.create(operationHistory);
	}

	// 处理小数点后面末尾的0
	private BigDecimal processBigDecimal(BigDecimal value) {
		// 检查是否为空
		if (value != null) {
			// 去掉末尾的零，并转换为整数
			return value.stripTrailingZeros().setScale(0, RoundingMode.DOWN);
		} else {
			return null;
		}
	}

	/**
	 * 创建gson对象
	 *
	 * @return
	 */
	private Gson getGson() {
		return new GsonBuilder()
				.registerTypeAdapter(LocalDate.class, new LocalDateAdapter())
				.registerTypeAdapter(LocalDateTime.class,
						new LocalDateTimeAdapter())
				.create();
	}
}
