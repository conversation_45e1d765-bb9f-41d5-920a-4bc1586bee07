package com.zhihaoscm.service.resource.admin.signreceipt;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.ContractPageResponse;
import com.zhihaoscm.domain.bean.entity.SignReceipt;
import com.zhihaoscm.domain.bean.vo.SignReceiptVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.biz.CertificationDef;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.core.service.SignReceiptService;
import com.zhihaoscm.service.resource.form.signreceipt.SignReceiptBuyCreateForm;
import com.zhihaoscm.service.resource.form.signreceipt.SignReceiptBuyUpdateForm;
import com.zhihaoscm.service.resource.validator.signreceipt.SignReceiptValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;

/**
 * @description: 签收管理API
 * @author: 彭湃
 * @date: 2025/1/16 19:33
 **/
@Tag(name = "采购签收管理", description = "采购签收管理API")
@RestController
@RequestMapping("/sign/receipt/buy")
public class SignReceiptBuyResource {

	private static final Logger log = LoggerFactory
			.getLogger(SignReceiptBuyResource.class);
	@Autowired
	private SignReceiptService signReceiptService;

	@Autowired
	private SignReceiptValidator validator;

	@Operation(summary = "分页查询签收单列表")
	@GetMapping("/paging")
	@Secured({ AdminPermissionDef.PROJECT_R, AdminPermissionDef.PROJECT_ALL_R,
			AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<Page<SignReceiptVo>> paging(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "签收编号或者项目名称") @RequestParam(required = false) String key,
			@Parameter(description = "订单id") @RequestParam(required = false) String orderId,
			@Parameter(description = "销售方名称") @RequestParam(required = false) String sellerName,
			@Parameter(description = "货物名称") @RequestParam(required = false) String goodsName,
			@Parameter(description = "状态") @RequestParam(required = false) List<Integer> status,
			@Parameter(description = "对账状态") @RequestParam(required = false) List<Integer> reconciliationStatus,
			@Parameter(description = "签收开始日期") @RequestParam(required = false) String startTime,
			@Parameter(description = "签收结束日期") @RequestParam(required = false) String endTime,
			@Parameter(description = "项目id") @RequestParam(required = false) String projectId,
			@Parameter(description = "排序key") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序") @RequestParam(required = false) String sortOrder) {
		// 是否有查看所有权限
		boolean hasAll = UserContextHolder.getPermissions()
				.contains(AdminPermissionDef.AdminPermission.PROJECT_ALL_R
						.getPermission());
		return new ApiResponse<>(PageUtil.convert(
				signReceiptService.adminBuyPaging(page, size, key, sellerName,
						goodsName, status, reconciliationStatus, startTime,
						endTime, sortKey, sortOrder, orderId, projectId, hasAll,
						Objects.requireNonNull(UserContextHolder.getUser())
								.getId())));
	}

	@Operation(summary = "分页查询签收单签收下拉列表")
	@GetMapping("/page-selector")
	@Secured({ AdminPermissionDef.PROJECT_R, AdminPermissionDef.PROJECT_ALL_R,
			AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<Page<SignReceiptVo>> pageSelector(
			@Parameter(description = "页码") @RequestParam(defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "id") @RequestParam(required = false) String key,
			@Parameter(description = "状态") @RequestParam(required = false) List<Integer> status,
			@Parameter(description = "签收单ids") @RequestParam(required = false) List<String> signReceiptIds,
			@Parameter(description = "修改时原先关联的签收单") @RequestParam(required = false) String receiptId,
			@Parameter(description = "类型：1 入库 2 出库") @RequestParam Integer type,
			@Parameter(description = "合同id") @RequestParam(required = false) String contractId) {
		return new ApiResponse<>(
				PageUtil.convert(signReceiptService.pageSelector(page, size,
						key, contractId, status, signReceiptIds, type,
						receiptId, CommonDef.AccountSource.CUSTOM.getCode())));
	}

	@Operation(summary = "根据id查询签收信息")
	@GetMapping("/vo/{id}")
	@Secured({ AdminPermissionDef.PROJECT_R, AdminPermissionDef.PROJECT_ALL_R,
			AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<SignReceiptVo> findVoById(
			@PathVariable(value = "id") String id) {
		return new ApiResponse<>(signReceiptService.findVoById(id));
	}

	/**
	 * @author: 许晶
	 * @param contractId
	 * @param status
	 * @param reconciliationStatus
	 * @return
	 */
	@Operation(summary = "合同关联的签收下拉列表")
	@GetMapping("/selector")
	@Secured({ AdminPermissionDef.PROJECT_R, AdminPermissionDef.PROJECT_ALL_R,
			AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<List<SignReceiptVo>> selector(
			@Parameter(description = "对账单id") @RequestParam(required = false) String reconciliationId,
			@Parameter(description = "合同id") @RequestParam String contractId,
			@Parameter(description = "签收状态") @RequestParam(required = false) List<Integer> status,
			@Parameter(description = "对账状态") @RequestParam(required = false) List<Integer> reconciliationStatus,
			@Parameter(description = "签收单id") @RequestParam(required = false) List<String> signReceiptIds) {
		return new ApiResponse<>(signReceiptService.selector(reconciliationId,
				contractId, status, reconciliationStatus, signReceiptIds));
	}

	@Operation(summary = "查询项目下的签收总重量")
	@GetMapping("/find/total-quantity/{projectId}")
	@Secured({ AdminPermissionDef.PROJECT_R, AdminPermissionDef.PROJECT_ALL_R,
			AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<BigDecimal> findTotalQuantity(
			@PathVariable("projectId") String projectId,
			@Parameter(description = "状态") @RequestParam(required = false) List<Integer> states,
			@Parameter(description = "签收开始日期") @RequestParam(required = false) LocalDateTime startTime,
			@Parameter(description = "签收结束日期") @RequestParam(required = false) LocalDateTime endTime,
			@Parameter(description = "签收编号或者合同名称") @RequestParam(required = false) String param,
			@Parameter(description = "销售方名称") @RequestParam(required = false) String sellerName,
			@Parameter(description = "状态") @RequestParam(required = false) List<Integer> status,
			@Parameter(description = "对账状态") @RequestParam(required = false) List<Integer> reconciliationStatus) {
		return new ApiResponse<>(signReceiptService
				.findTotalQuantityBuy(projectId, states, startTime, endTime,
						param, sellerName, status, reconciliationStatus)
				.orElse(BigDecimal.ZERO));
	}

	@Operation(summary = "查询是否可以快速出入库")
	@GetMapping("/check-quick-bound/{id}")
	@Secured({ AdminPermissionDef.PROJECT_R, AdminPermissionDef.PROJECT_ALL_R,
			AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<Boolean> checkQuickBound(@PathVariable("id") String id,
			@Parameter(description = "类型 1：入库 2：出库") @RequestParam(required = false) Integer type) {
		SignReceipt signReceipt = validator.validateExist(id);
		if (Objects.isNull(signReceipt)) {
			return new ApiResponse<>(false);
		}
		return new ApiResponse<>(
				signReceiptService.checkQuickBound(id, type).orElse(false));
	}

	@Operation(summary = "查询订单下的签收总重量")
	@GetMapping("/find/total-quantity-order/{projectId}")
	@Secured({ AdminPermissionDef.PROJECT_R, AdminPermissionDef.PROJECT_ALL_R,
			AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<BigDecimal> findTotalQuantityOfOrder(
			@PathVariable("projectId") String projectId,
			@Parameter(description = "状态") @RequestParam(required = false) Integer state,
			@Parameter(description = "订单id") @RequestParam(required = false) String orderId,
			@Parameter(description = "签收开始日期") @RequestParam(required = false) LocalDateTime startTime,
			@Parameter(description = "签收结束日期") @RequestParam(required = false) LocalDateTime endTime,
			@Parameter(description = "合同类型（采购或者销售）") @RequestParam Integer type) {
		return new ApiResponse<>(signReceiptService
				.findTotalQuantityOfOrder(projectId, orderId, startTime,
						endTime, state, type)
				.orElse(BigDecimal.ZERO));
	}

	@Operation(summary = "新增签收单")
	@PostMapping("/create")
	@Secured({ AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<SignReceipt> create(
			@Validated @RequestBody SignReceiptBuyCreateForm form) {
		validator.validateCreateBuySignReceipt(form);
		SignReceipt signReceipt = form.convertEntity();
		signReceipt.setInitiator(CommonDef.AccountSource.INNER.getCode());
		return new ApiResponse<>(signReceiptService
				.createAdmin(signReceipt, form.getDeliverGoodsList())
				.orElseThrow());
	}

	@Operation(summary = "修改签收单")
	@PutMapping("/update/{id}")
	@Secured({ AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<SignReceipt> update(
			@Validated @RequestBody SignReceiptBuyUpdateForm form,
			@PathVariable(value = "id") String id) {
		SignReceipt signReceipt = validator.validateUpdateBuy(id, form);
		return new ApiResponse<>(
				signReceiptService.updateAdmin(form.convertEntity(signReceipt),
						form.getDeliverGoodsList()).orElseThrow());
	}

	@Operation(summary = "提交签收单")
	@PutMapping("/submit/{id}")
	@Secured({ AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<SignReceipt> submit(
			@PathVariable(value = "id") String id) {
		return new ApiResponse<>(signReceiptService
				.updateAllProperties(validator.validateSubmit(id)));
	}

	@Operation(summary = "删除签收单")
	@DeleteMapping("/delete/{id}")
	@Secured({ AdminPermissionDef.PROJECT_DEAL })
	public void deleteBuy(@PathVariable(value = "id") String id) {
		SignReceipt signReceipt = validator.validateDelete(id);
		signReceiptService.deleteBuy(signReceipt);
	}

	@Operation(summary = "驳回签收单")
	@PutMapping("/revert/{id}")
	@Secured({ AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<SignReceipt> revert(
			@PathVariable(value = "id") String id,
			@Parameter(description = "驳回原因") @RequestParam(required = false) String reason) {
		SignReceipt signReceipt = validator.validateRevert(id);
		signReceipt.setRevokeReason(reason);
		return new ApiResponse<>(signReceiptService
				.revert(signReceipt, Boolean.TRUE).orElseThrow());
	}

	@Operation(summary = "确认签收单")
	@PutMapping("/confirm/{id}")
	@Secured({ AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<SignReceipt> confirm(
			@PathVariable(value = "id") String id) {
		SignReceipt signReceipt = validator.validatePeople(id);
		return new ApiResponse<>(
				signReceiptService.confirm(signReceipt).orElseThrow());
	}

	@Operation(summary = "待发起状态撤回")
	@PutMapping("/revoke/{id}")
	@Secured(AdminPermissionDef.PROJECT_SALE_DEAL)
	public ApiResponse<SignReceipt> revoke(
			@PathVariable(value = "id") String id) {
		return new ApiResponse<>(signReceiptService
				.revoke(validator.validatePeople(id)).orElseThrow());
	}

	@Operation(summary = "发起签署")
	@PutMapping("/initiate-sign/{id}")
	public ApiResponse<ContractPageResponse> initiateSign(
			@PathVariable(value = "id") String id) {
		SignReceipt signReceipt = validator.validateInitiateSign(id);
		return new ApiResponse<>(signReceiptService
				.initiateSign(signReceipt, CertificationDef.Origin.PC.getCode(),
						CommonDef.AccountSource.CUSTOM.getCode())
				.orElse(null));
	}

	@Operation(summary = "获取签署页面链接")
	@GetMapping(value = "/sign/{id}")
	public ApiResponse<ContractPageResponse> sign(
			@PathVariable(value = "id") String id) {
		SignReceipt signReceipt = validator.validateExist(id);
		return new ApiResponse<>(signReceiptService
				.signing(signReceipt, CertificationDef.Origin.PC.getCode())
				.orElse(null));
	}

	@Operation(summary = "线上发起作废")
	@PutMapping("/invalid/{id}")
	public ApiResponse<ContractPageResponse> invalid(@PathVariable String id,
			@Parameter(description = "作废原因") @RequestParam String reasons) {
		log.info("签收作废原因:{}", reasons);
		SignReceipt signReceipt = validator.validateInvalid(id,
				CommonDef.UserType.OUTER.getCode(), false);
		signReceipt.setInvalidReason(reasons);
		signReceipt.setInvalidInitiator(CommonDef.UserType.OUTER.getCode());
		signReceiptService.update(signReceipt);
		return new ApiResponse<>(signReceiptService
				.invalid(signReceipt, CommonDef.UserType.OUTER.getCode())
				.orElse(null));
	}

	@Operation(summary = "线下发起作废")
	@PutMapping("/invalid-off-line/{id}")
	@Secured({ AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<SignReceipt> invalidOffLine(@PathVariable String id,
			@Parameter(description = "作废原因") @RequestParam String reasons,
			@Parameter(description = "作废文件") @RequestParam(required = false) Long fileId) {
		log.info("签收作废原因:{}", reasons);
		SignReceipt signReceipt = validator.validateInvalid(id,
				CommonDef.UserType.OUTER.getCode(), true);
		signReceipt.setInvalidReason(reasons);
		signReceipt.setInvalidInitiator(CommonDef.UserType.OUTER.getCode());
		signReceipt.setInvalidFileId(fileId);
		signReceiptService.update(signReceipt);
		return new ApiResponse<>(signReceiptService
				.invalidOffLine(signReceipt, CommonDef.UserType.OUTER.getCode())
				.orElse(null));
	}

	@Operation(summary = "确认作废")
	@PutMapping("/confirm-invalid/{id}")
	@Secured({ AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<SignReceipt> confirmInvalid(@PathVariable String id) {
		SignReceipt signReceipt = validator.validateInvalidConfirm(id,
				CommonDef.UserType.OUTER.getCode());
		return new ApiResponse<>(
				signReceiptService.confirmInvalid(signReceipt).orElse(null));
	}

	@Operation(summary = "驳回作废")
	@PutMapping("/revert-invalid/{id}")
	@Secured({ AdminPermissionDef.PROJECT_DEAL })
	public ApiResponse<SignReceipt> revertInvalid(@PathVariable String id,
			@Parameter(description = "驳回原因") @RequestParam String reasons) {
		SignReceipt signReceipt = validator.validateInvalidConfirm(id,
				CommonDef.UserType.OUTER.getCode());
		signReceipt.setInvalidRevokeReason(reasons);
		return new ApiResponse<>(
				signReceiptService.revertInvalid(signReceipt).orElse(null));
	}

	/**
	 * <AUTHOR>
	 * @param response
	 * @param id
	 * @return
	 * @throws IOException
	 */
	@Operation(summary = "下载/预览文件")
	@GetMapping("/download/{id}")
	public void download(HttpServletResponse response,
			@PathVariable("id") String id) throws IOException {
		validator.validateExist(id);
		signReceiptService.downloadPdf(response, id);
	}

}
