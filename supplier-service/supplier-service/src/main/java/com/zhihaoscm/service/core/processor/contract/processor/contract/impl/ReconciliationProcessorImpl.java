package com.zhihaoscm.service.core.processor.contract.processor.contract.impl;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.DealingsEnterprise;
import com.zhihaoscm.domain.bean.entity.Order;
import com.zhihaoscm.domain.bean.entity.Reconciliation;
import com.zhihaoscm.domain.bean.json.notice.AliMessage;
import com.zhihaoscm.domain.bean.json.notice.UserMessage;
import com.zhihaoscm.domain.bean.vo.DeliverGoodsVo;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.service.config.properties.SMSProperties;
import com.zhihaoscm.service.core.processor.contract.processor.contract.ContractProcessor;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.core.service.usercenter.CustomerService;

@Service
public class ReconciliationProcessorImpl implements ContractProcessor {

	@Autowired
	private ReconciliationService reconciliationService;
	@Autowired
	private FileService fileService;
	@Autowired
	private ContractRecordService contractRecordService;
	@Autowired
	private DeliverGoodsService deliverGoodsService;
	@Autowired
	private MessageService messageService;
	@Autowired
	private SMSProperties wxSubscriptionProperties;
	@Autowired
	private OrderService orderService;
	@Autowired
	private CustomerService customerService;
	@Autowired
	private DealingsEnterpriseService dealingsEnterpriseService;

	@Override
	public Boolean support(Integer type) {
		return PurchaseContractDef.CorrelationTable.RECONCILIATION.match(type);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void signComplete(String tableId) {
		reconciliationService.findOne(tableId).ifPresent(reconciliation -> {
			if (ReconciliationDef.State.FINISHED
					.match(reconciliation.getState())) {
				return;
			}
			// 修改状态 签署状态双方已完成
			reconciliation.setSignStatus(
					BusinessContractDef.CommonSignState.COMPLETED.getCode());
			// 对账状态改为已完成
			reconciliation.setState(ReconciliationDef.State.FINISHED.getCode());
			// 开票状态设为未开票
			reconciliation.setInvoiceState(
					ReconciliationDef.InvoiceState.NOT_INVOICED.getCode());
			// 重新获取新的签署文件
			Long newFileId = contractRecordService.download(tableId,
					PurchaseContractDef.CorrelationTable.RECONCILIATION);
			reconciliation.setReconciliationFileId(newFileId);
			reconciliationService.update(reconciliation);
			// 更新关联的订单的对账状态为已完成
			List<Order> orderList = orderService
					.findByIds(reconciliation.getOrderIds());
			List<String> orderIds = orderList.stream().map(Order::getId)
					.toList();
			// 根据签收单id找到所有的发货信息
			List<DeliverGoodsVo> deliverGoodsVoList = deliverGoodsService
					.findVosBySignReceiptIds(reconciliation.getReceiptIds());
			// 根据发货信息 被关联的签收单ids
			List<String> signReceiptIds = reconciliation.getReceiptIds();
			// 对账单完成后签收单中的对账状态改为已完成状态
			reconciliationService
					.completedChangeSignReceiptStatus(signReceiptIds);
			// 处理对账单的订单所关联的支付记录
			reconciliationService.handlePayment(reconciliation, orderIds);
			// 销售对账生成应收应付
			if (ReconciliationDef.Type.SELL.match(reconciliation.getType())) {
				// 处理应收/付款
				reconciliationService.handleAccounts(reconciliation, orderList,
						deliverGoodsVoList);
			} else {
				// 采购对账生成应收应付
				if (ReconciliationDef.Type.BUY
						.match(reconciliation.getType())) {
					// 处理应收/付款
					reconciliationService.handleBuyAccounts(reconciliation,
							orderList, deliverGoodsVoList);
				}

			}

			Customer customer = null;
			if (ReconciliationDef.Type.SELL.match(reconciliation.getType())) {
				DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
						.findOne(reconciliation.getPurchaserBusinessId())
						.orElse(null);
				if (Objects.nonNull(dealingsEnterprise) && Objects
						.nonNull(dealingsEnterprise.getCustomerId())) {
					customer = customerService
							.findOne(dealingsEnterprise.getCustomerId())
							.orElse(null);
				}
			} else {
				DealingsEnterprise dealingsEnterprise = dealingsEnterpriseService
						.findOne(reconciliation.getSellerBusinessId())
						.orElse(null);
				if (Objects.nonNull(dealingsEnterprise) && Objects
						.nonNull(dealingsEnterprise.getCustomerId())) {
					customer = customerService
							.findOne(dealingsEnterprise.getCustomerId())
							.orElse(null);
				}
			}

			if (Objects.nonNull(customer)) {
				messageService.sendNotice(AliMessage.builder()
						.receiptors(List.of(String.valueOf(customer.getId())))
						.templateCode(wxSubscriptionProperties
								.getReconciliationFinishCode())
						.params(Map.of("settle_id", reconciliation.getId()))
						.mobile(customer.getMobile()).build());

				messageService.sendNotice(UserMessage.builder()
						.type(UserMessageDef.MessageType.ORDER.getCode())
						.title(MessageFormat.format(
								UserMessageConstants.RECONCILIATION_FINISHED_TEMPLATE,
								reconciliation.getId()))
						.receiptors(List.of(String.valueOf(customer.getId())))
						.url(UserMessageConstants.RECONCILIATION_DETAIL_PAGE)
						.detailId(String.valueOf(reconciliation.getId()))
						.initiator(UserMessageDef.BusinessInitiator.receipt
								.getCode())
						.build());
			}
			reconciliationService.notice(reconciliation, 4);
		});
	}

	@Override
	public void signReject(String tableId, String contact) {
		reconciliationService.findOne(tableId)
				.ifPresent(reconciliation -> reconciliationService
						.reject(reconciliation, Boolean.FALSE));
	}

	@Override
	public void signing(String correlationId, String name,
			String callbackType) {
		reconciliationService.findOne(correlationId)
				.ifPresent(reconciliation -> {
					// 防止契约锁回调顺序有问题的处理
					Integer completeStatus = ContractDef.State.COMPLETED
							.getCode();
					if (Objects.equals(completeStatus,
							reconciliation.getState())) {
						return;
					}
					Long newFileId = contractRecordService.download(
							correlationId,
							PurchaseContractDef.CorrelationTable.RECONCILIATION);
					fileService.batchUnActive(
							List.of(reconciliation.getReconciliationFileId()));
					reconciliation.setReconciliationFileId(newFileId);

					boolean isBuyer = false;
					if (PurchaseContractDef.CallbackType.SEAL.name()
							.equals(callbackType)) {
						isBuyer = name.equals(reconciliation
								.getPurchaserEnterprise().getName());
					} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
							.equals(callbackType)) {
						isBuyer = name.equals(reconciliation
								.getPurchaserEnterprise().getRealName());
					}
					boolean isSupplier = false;
					if (PurchaseContractDef.CallbackType.SEAL.name()
							.equals(callbackType)) {
						isSupplier = name.equals(
								reconciliation.getSellerEnterprise().getName());
					} else if (PurchaseContractDef.CallbackType.PERSONAL.name()
							.equals(callbackType)) {
						isSupplier = name.equals(reconciliation
								.getSellerEnterprise().getRealName());
					}
					if (isBuyer) {
						if (BusinessContractDef.CommonSignState.SUPPLY_SIGNED
								.match(reconciliation.getSignStatus())) {
							reconciliation.setSignStatus(
									BusinessContractDef.CommonSignState.COMPLETED
											.getCode());
						} else {
							reconciliation.setSignStatus(
									BusinessContractDef.CommonSignState.BUYER_SIGNED
											.getCode());
						}
					} else if (isSupplier) {
						if (BusinessContractDef.CommonSignState.BUYER_SIGNED
								.match(reconciliation.getSignStatus())) {
							reconciliation.setSignStatus(
									BusinessContractDef.CommonSignState.COMPLETED
											.getCode());
						} else {
							reconciliation.setSignStatus(
									BusinessContractDef.CommonSignState.SUPPLY_SIGNED
											.getCode());
						}
					}
					reconciliationService.update(reconciliation);
				});

	}

	@Override
	public void sendInvalid(String tableId, String name) {
		reconciliationService.findOne(tableId).ifPresent(reconciliation -> {
			if (ReconciliationDef.State.INVALIDING
					.match(reconciliation.getState())
					&& Objects.nonNull(reconciliation.getInvalidFileId())) {
				return;
			}

			// 作废后获取作废合同id
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.RECONCILIATION);
			Reconciliation reconciliation1 = new Reconciliation();
			reconciliation1.setInvalidFileId(fileId);
			reconciliation1
					.setState(ReconciliationDef.State.INVALIDING.getCode());
			reconciliation1.setInvalidSignState(
					PurchaseContractDef.CommonSignState.UNSIGNED.getCode());
			reconciliationService.updateNotNull(reconciliation1);
		});
	}

	@Override
	public void invaliding(String tableId, String name) {
		reconciliationService.findOne(tableId).ifPresent(reconciliation -> {

			if (CommonDef.Symbol.YES
					.match(reconciliation.getIsPreReconciliation())
					&& CommonDef.Symbol.YES.match(
							reconciliation.getIsConductReconciliation())) {
				// 存在预对账并且在对账阶段时 作废后变成预对账完成状态
				// 防止契约锁回调顺序有问题的处理
				if (Objects.equals(
						ReconciliationDef.State.PRE_FINISHED.getCode(),
						reconciliation.getState())) {
					return;
				}
			} else {
				// 防止契约锁回调顺序有问题的处理
				if (Objects.equals(ReconciliationDef.State.INVALID.getCode(),
						reconciliation.getState())) {
					return;
				}
			}
			boolean isBuyer;
			boolean isSupplier;
			isBuyer = name
					.equals(reconciliation.getPurchaserEnterprise().getName())
					|| name.equals(reconciliation.getPurchaserEnterprise()
							.getRealName());

			isSupplier = name
					.equals(reconciliation.getSellerEnterprise().getName())
					|| name.equals(
							reconciliation.getSellerEnterprise().getRealName());

			if (isBuyer) {
				reconciliation.setInvalidSignState(
						PurchaseContractDef.CommonSignState.BUYER_SIGNED
								.getCode());
				reconciliation.setPurchaseInvalidTime(LocalDateTime.now());
			} else if (isSupplier) {
				reconciliation.setInvalidSignState(
						PurchaseContractDef.CommonSignState.SUPPLY_SIGNED
								.getCode());
				reconciliation.setSellerInvalidTime(LocalDateTime.now());
			}

			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.RECONCILIATION);
			reconciliation.setInvalidFileId(fileId);
			reconciliationService.update(reconciliation);
		});
	}

	@Override
	public void invalided(String tableId, String name) {
		reconciliationService.findOne(tableId).ifPresent(reconciliation -> {
			if (CommonDef.Symbol.YES
					.match(reconciliation.getIsPreReconciliation())
					&& CommonDef.Symbol.YES.match(
							reconciliation.getIsConductReconciliation())) {
				// 存在预对账并且在对账阶段时 作废后变成预对账完成状态
				reconciliation.setState(
						ReconciliationDef.State.PRE_FINISHED.getCode());
				// 是否进入对账设置为否
				reconciliation.setIsConductReconciliation(
						CommonDef.Symbol.NO.getCode());
			} else {
				// 其他情况变成已作废状态
				reconciliation
						.setState(ReconciliationDef.State.INVALID.getCode());
			}
			reconciliation.setInvalidSignState(
					PurchaseContractDef.CommonSignState.COMPLETED.getCode());
			Long fileId = contractRecordService.detail(tableId,
					PurchaseContractDef.CorrelationTable.RECONCILIATION);
			reconciliation.setInvalidFileId(fileId);
			reconciliationService.update(reconciliation);
			if (CommonDef.Symbol.NO
					.match(reconciliation.getIsPreReconciliation())
					|| CommonDef.Symbol.NO.match(
							reconciliation.getIsConductReconciliation())) {
				// 不存在预对账或不是在对账阶段作废后删除应收应付款
				// 处理应收/付款
				reconciliationService.handleDelAccounts(reconciliation);
			}
			reconciliationService.changeRelate(reconciliation);
		});
	}

	@Override
	public void rejectInvalid(String tableId, String name) {
		reconciliationService.findOne(tableId).ifPresent(reconciliation -> {
			reconciliation.setState(ReconciliationDef.State.FINISHED.getCode());
			reconciliation.setInvalidSignState(null);
			reconciliation.setInvalidRevokeTime(LocalDateTime.now());
			reconciliationService.changeRejectInvalid(reconciliation);
			reconciliationService.updateAllProperties(reconciliation);
		});
	}
}
