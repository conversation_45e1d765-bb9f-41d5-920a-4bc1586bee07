package com.zhihaoscm.service.resource.form.signreceipt;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import org.hibernate.validator.constraints.Length;

import com.zhihaoscm.domain.bean.dto.DeliverDto;
import com.zhihaoscm.domain.bean.entity.SignReceipt;
import com.zhihaoscm.domain.meta.biz.SignReceiptDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SignReceiptCreateForm {

	/**
	 * 项目id
	 */
	@Schema(description = "项目id")
	@NotNull(message = ErrorCode.CODE_30151002)
	private String projectId;

	/**
	 * 项目名称
	 */
	@Schema(description = "项目名称")
	private String projectName;

	/**
	 * 合同id
	 */
	@Schema(description = "合同id")
	@NotNull(message = ErrorCode.CODE_30149003)
	private String contractId;

	/**
	 * 签收重量
	 */
	@Schema(description = "签收重量")
	@NotNull(message = ErrorCode.CODE_30097028)
	private BigDecimal receiptWeight;

	/**
	 * 签收确认日期
	 */
	@Schema(description = "签收确认日期")
	@NotNull(message = ErrorCode.CODE_30097005)
	private LocalDateTime signConfirmDate;

	/**
	 * 签收单据文件id
	 */
	@Schema(description = "签收单据文件id")
	private Long signReceiptFileId;

	/**
	 * 备注
	 */
	@Schema(description = "备注")
	@Length(max = 200, message = ErrorCode.CODE_30097026)
	private String remark;

	/**
	 * 签署方式
	 */
	@Schema(description = "签署方式")
	private Integer signType;

	/**
	 * 发货单列表
	 */
	@Schema(description = "发货单列表")
	private List<DeliverDto> deliverDtoList;

	/**
	 * 保存类型
	 */
	@Schema(description = "保存类型")
	private Integer saveType;

	public SignReceipt convertEntity() {
		SignReceipt signReceipt = new SignReceipt();
		signReceipt.setProjectId(this.projectId);
		signReceipt.setProjectName(this.getProjectName());
		signReceipt.setContractId(this.getContractId());
		signReceipt.setType(SignReceiptDef.Type.SELL.getCode());
		signReceipt.setSignConfirmDate(this.signConfirmDate);
		signReceipt.setSignReceiptFileId(this.signReceiptFileId);
		signReceipt.setRemark(this.remark);
		signReceipt.setSignType(this.signType);
		return signReceipt;
	}

}
