package com.zhihaoscm.service.resource.validator.project;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.zhihaoscm.service.resource.validator.goods.GoodsValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.domain.bean.dto.ProjectDto;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.meta.biz.AccountsDef;
import com.zhihaoscm.domain.meta.biz.ProjectDef;
import com.zhihaoscm.domain.meta.biz.SystemDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.service.client.usercenter.SupplierClient;
import com.zhihaoscm.service.config.properties.SupplierChainProperties;
import com.zhihaoscm.service.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.service.core.service.*;
import com.zhihaoscm.service.resource.form.project.ProjectCreateForm;
import com.zhihaoscm.service.resource.form.project.ProjectItemForm;
import com.zhihaoscm.service.resource.form.project.ProjectUpdateForm;

@Component
public class ProjectValidator {

	@Autowired
	private ProjectService projectService;
	@Autowired
	private AccountsService accountsService;
	@Autowired
	private ContractService contractService;
	@Autowired
	private OrderService orderService;
	@Autowired
	private DeliverGoodsService deliverGoodsService;
	@Autowired
	private SignReceiptService signReceiptService;
	@Autowired
	private ReconciliationService reconciliationService;
	@Autowired
	private PaymentService paymentService;
	@Autowired
	private BillPaymentService billPaymentService;
	@Autowired
	private RefundService refundService;
	@Autowired
	private QuotaChangeService quotaChangeService;
	@Autowired
	private ServiceFeeService serviceFeeService;
	@Autowired
	private ProjectItemService projectItemService;
	@Autowired
	private SupplierChainProperties supplierChainProperties;
	@Autowired
	private SupplierClient supplierClient;
	@Autowired
	private GoodsValidator goodsValidator;

	/**
	 * 校验新增采购项目
	 *
	 * @param projectCreateForm
	 */

	public ProjectDto validateCreateProject(
			ProjectCreateForm projectCreateForm) {
		// 如果是SAAS点击新增需要校验所属供应链有没有完成组织机构认证
		if (SystemDef.SystemType.SAAS
				.match(supplierChainProperties.getType())) {
			// saas 供应链id 判断当前供应链是否完成组织机构认证
			Long supplierChainId = Objects
					.requireNonNull(UserContextHolder.getUser()).getTenantId();
			if (Objects.isNull(supplierChainId)) {
				// 如果用户的供应链id为空 则提示
				throw new BadRequestException(ErrorCode.CODE_30151068);
			}
			Supplier supplier = supplierClient.findByTenantId(supplierChainId)
					.orElse(null);
			if (Objects.nonNull(supplier)) {
				// 没有完成组织机构认证 请先完成组织机构认证
				if (!CommonDef.Symbol.YES.match(supplier.getApplyState())) {
					throw new BadRequestException(ErrorCode.CODE_30152047);
				}
			} else {
				throw new BadRequestException(ErrorCode.CODE_30152048);
			}
		}
		ProjectDto projectDto = new ProjectDto();
		List<ProjectItemForm> projectItemForms = projectCreateForm
				.getProjectItemForms();
		List<ProjectItem> projectItemList = new ArrayList<>();
		// 处理项目预估可提货额公式项
		this.handleProjectItem(projectItemForms, projectItemList);
		// 校验项目名称是否重复
		this.validateProjectName(projectCreateForm.getName());
		// 项目经理的上限为10个
		if (projectCreateForm.getProjectManagerIds().size() > 10) {
			throw new BadRequestException(ErrorCode.CODE_30152014);
		}
		// 上游供应商的上限为10个
		if (projectCreateForm.getSupplierIds().size() > 10) {
			throw new BadRequestException(ErrorCode.CODE_30152024);
		}
		// 下游采购方的上限为10个
		if (projectCreateForm.getCustomerIds().size() > 10) {
			throw new BadRequestException(ErrorCode.CODE_30152025);
		}
		// 校验上游供应商和下游采购方是否有相同id
		if (projectCreateForm.getSupplierIds().stream().anyMatch(
				id -> projectCreateForm.getCustomerIds().contains(id))) {
			throw new BadRequestException(ErrorCode.CODE_30152046);
		}
		// 查询货物
		Goods goods = goodsValidator
				.validateExist(projectCreateForm.getGoodsId());
		Project project = projectCreateForm.convertToEntity(goods);
		projectDto.setProject(project);
		projectDto.setProjectItemList(projectItemList);
		return projectDto;
	}

	/**
	 * 校验修改项目
	 *
	 * @param projectUpdateForm
	 */

	public ProjectDto validateUpdateProject(ProjectUpdateForm projectUpdateForm,
			String id) {
		ProjectDto projectDto = new ProjectDto();
		Project project = this.validateExistProject(id);
		List<ProjectItemForm> projectItemForms = projectUpdateForm
				.getProjectItemForms();
		// 校验 大于0小于1亿的数字，保留两位小数。
		String regex = "^([1-9]\\d{0,7}(\\.\\d{1,2})?|0\\.([1-9]\\d?)|(0\\.0[1-9]))$";
		// 需要修改的数据
		List<ProjectItem> projectItemUpdateList = new ArrayList<>();
		// 需要新增的明细
		List<ProjectItem> projectItemList = new ArrayList<>();
		if (org.apache.commons.collections4.CollectionUtils
				.isNotEmpty(projectItemForms)) {
			for (ProjectItemForm projectItemForm : projectItemForms) {
				ProjectItem projectItem = this.getProjectItem(project.getId(),
						projectItemForm, regex);
				if (Objects.nonNull(projectItemForm.getId())) {
					projectItemUpdateList.add(projectItem);
				} else {
					projectItemList.add(projectItem);
				}
			}
		}
		// 合同履约中 上游供应商和下游采购方可增加不可删除原有选项。货物名称不可修改。。
		if (ProjectDef.State.PROCESSING.match(project.getState())) {
			// 上游供应商id不能删除原有选选项
			if (!projectUpdateForm.getSupplierIds()
					.containsAll(project.getSupplierIds())) {
				throw new BadRequestException(ErrorCode.CODE_30152018);
			}
			// 下游采购方id不能删除原有选项
			if (!projectUpdateForm.getCustomerIds()
					.containsAll(project.getCustomerIds())) {
				throw new BadRequestException(ErrorCode.CODE_30152019);
			}
		}
		// 当传过来的项目名称和数据库的不一样时需要校验和其他项目的名称是否一样
		if (!Objects.equals(projectUpdateForm.getName(), project.getName())) {
			this.validateProjectName(projectUpdateForm.getName());
		}
		// 项目经理的上限为10个
		if (projectUpdateForm.getProjectManagerIds().size() > 10) {
			throw new BadRequestException(ErrorCode.CODE_30152014);
		}
		// 查询货物
		Goods goods = goodsValidator
				.validateExist(projectUpdateForm.getGoodsId());
		Project project1 = projectUpdateForm.convertToEntity(project, goods);
		projectDto.setProject(project1);
		projectDto.setProjectItemList(projectItemList);
		projectDto.setProjectItemUpdateList(projectItemUpdateList);
		return projectDto;
	}

	/**
	 * 校验删除
	 *
	 * @param
	 */
	public void validateDelete(String id) {
		this.validateExistProject(id);
		// 校验该项目是否被合同关联了
		List<Contract> contracts = contractService.findByProjectId(List.of(id));
		if (CollectionUtils.isNotEmpty(contracts)) {
			throw new BadRequestException(ErrorCode.CODE_30152016);
		}
	}

	/**
	 * 校验申请完结
	 *
	 * @param
	 */
	public Project validateApplyFinish(String id, Long userId) {
		Project project = this.validateExistProject(id);
		// 申请完结标志设置为是
		project.setIsApplyFinish(CommonDef.Symbol.YES.getCode());
		// 需要校验不存在状态为逾期的应收款
		// 根据项目id查询项目已逾期的应收款数据列表
		List<Accounts> accountsList = accountsService.findByProjectIdsAndStatus(
				List.of(id), AccountsDef.Status.DELAY.getCode());
		if (CollectionUtils.isNotEmpty(accountsList)) {
			throw new BadRequestException(ErrorCode.CODE_30152011);
		}
		// 该项目相关的所有单据状态都已完成（合同管理销售管理、资金管理、服务费管理、发票管理等）。
		// 1合同管理未完成的数据
		List<Contract> contracts = contractService.findUnfinished(id);
		// 2订单未完成的数据
		List<Order> orders = orderService.findUnfinished(id);
		// 3发货单未完成的数据
		List<DeliverGoods> deliverGoodsList = deliverGoodsService
				.findUnfinished(id);
		// 4签收未完成的数据
		List<SignReceipt> signReceipts = signReceiptService.findUnfinished(id);
		// 5对账未完成的数据
		List<Reconciliation> reconciliations = reconciliationService
				.findUnfinished(id);
		// 6发票未完成的数据
		List<BillPayment> invoices = billPaymentService.findUnfinished(id);
		// 7收款未完成的数据
		List<Payment> payments = paymentService.findUnfinished(id);
		// 8退款未完成的数据
		List<Refund> refunds = refundService.findUnfinished(id);
		// 9额度变更未完成的数据
		List<QuotaChange> quotaChanges = quotaChangeService.findUnfinished(id);
		// 10服务费未完成的数据
		List<ServiceFee> serviceFees = serviceFeeService.findUnfinished(id);
		// 该项目存在单剧有未完成的数据则不能申请完结
		if (CollectionUtils.isNotEmpty(contracts)
				|| CollectionUtils.isNotEmpty(orders)
				|| CollectionUtils.isNotEmpty(deliverGoodsList)
				|| CollectionUtils.isNotEmpty(signReceipts)
				|| CollectionUtils.isNotEmpty(reconciliations)
				|| CollectionUtils.isNotEmpty(invoices)
				|| CollectionUtils.isNotEmpty(payments)
				|| CollectionUtils.isNotEmpty(refunds)
				|| CollectionUtils.isNotEmpty(quotaChanges)
				|| CollectionUtils.isNotEmpty(serviceFees)) {
			throw new BadRequestException(ErrorCode.CODE_30152023);
		}
		Long supervisorId = project.getSupervisorId();
		List<Long> userIds = project.getProjectManagerIds();
		// 申请完结既不是项目负责人也不是项目经理时
		if (!userIds.contains(userId) && !userId.equals(supervisorId)) {
			throw new BadRequestException(ErrorCode.CODE_30152020);
		}
		return project;
	}

	/**
	 * 校验确认完结
	 *
	 * @param
	 */
	public Project validateFinish(String id) {
		Project project = this.validateExistProject(id);
		// 判断是否申请了完结
		if (!CommonDef.Symbol.YES.match(project.getIsApplyFinish())) {
			throw new BadRequestException(ErrorCode.CODE_30152010);
		}
		project.setState(ProjectDef.State.FINISHED.getCode());
		return project;
	}

	/**
	 * 根据项目id校验项目是否存在
	 *
	 * @param id
	 */
	public Project validateExistProject(String id) {
		Project project = projectService.findOne(id).orElse(null);
		if (Objects.isNull(project)) {
			throw new BadRequestException(ErrorCode.CODE_30152013);
		}
		return project;
	}

	/**
	 * 校验项目名称是否已经存在
	 *
	 * @param name
	 */
	private void validateProjectName(String name) {
		List<Project> projectList = projectService.findByName(name);
		if (!projectList.isEmpty()) {
			throw new BadRequestException(ErrorCode.CODE_30152012);
		}
	}

	/**
	 * 校验管理后台当前操作人是否是该项目指派人员
	 *
	 */
	public void validateProjectPeople(String projectId) {
		// 客户端上下文可能为空
		if (Objects.isNull(UserContextHolder.getUser())) {
			return;
		}
		Long userId = UserContextHolder.getUser().getId();
		Project project = this.validateExistProject(projectId);
		Long supervisorId = project.getSupervisorId();
		List<Long> userIds = project.getProjectManagerIds();
		// 既不是项目负责人也不是项目经理时
		if (!userIds.contains(userId) && !userId.equals(supervisorId)) {
			throw new BadRequestException(ErrorCode.CODE_30152020);
		}
	}

	/**
	 * 处理项目的预估可提货额公式项
	 *
	 * @param projectItemForms
	 * @param projectItemList
	 */
	private void handleProjectItem(List<ProjectItemForm> projectItemForms,
			List<ProjectItem> projectItemList) {
		// 校验 大于0小于1亿的数字，保留两位小数。
		String regex = "^([1-9]\\d{0,7}(\\.\\d{1,2})?|0\\.([1-9]\\d?)|(0\\.0[1-9]))$";
		if (org.apache.commons.collections4.CollectionUtils
				.isNotEmpty(projectItemForms)) {
			for (ProjectItemForm projectItemForm : projectItemForms) {
				ProjectItem projectItem = getProjectItem(null, projectItemForm,
						regex);
				projectItemList.add(projectItem);
			}
		}
	}

	private ProjectItem getProjectItem(String projectId,
			ProjectItemForm projectItemForm, String regex) {
		if (Objects.isNull(projectItemForm.getItem())) {
			throw new BadRequestException(ErrorCode.CODE_30152038);
		}
		if (Objects.isNull(projectItemForm.getDirection())) {
			throw new BadRequestException(ErrorCode.CODE_30152039);
		}
		if (!ProjectDef.Item.PAYMENT.match(projectItemForm.getItem())
				&& !ProjectDef.Item.ORDER_AMOUNT
						.match(projectItemForm.getItem())
				&& !ProjectDef.Item.REC_AMOUNT
						.match(projectItemForm.getItem())) {
			// 增减项不是 货款/订单金额/对账金额 时 填写的金额不能为空且为大于0小于1亿的数字，保留两位小数
			if (Objects.nonNull(projectItemForm.getAmount())) {
				this.validatePrice(projectItemForm.getAmount(), regex);
			} else {
				throw new BadRequestException(ErrorCode.CODE_30152041);
			}
		}
		// 备注不为空时 校验不超过32个字符
		if (Objects.nonNull(projectItemForm.getRemark())) {
			if (projectItemForm.getRemark().length() > 32) {
				throw new BadRequestException(ErrorCode.CODE_30151066);
			}
		}
		if (Objects.nonNull(projectItemForm.getId())) {
			ProjectItem projectItem = this
					.validateExitsItem(projectItemForm.getId());
			return projectItemForm.convertUpEntity(projectItem);
		} else {
			return projectItemForm.convertEntity(projectId);
		}
	}

	/**
	 * 校验数字
	 *
	 * @param price
	 * @param regex
	 */
	private void validatePrice(BigDecimal price, String regex) {
		String numberStr = price.stripTrailingZeros().toPlainString();
		if (!numberStr.matches(regex)) {
			throw new BadRequestException(ErrorCode.CODE_30152041);
		}
	}

	/**
	 * 校验公式项是否存在
	 *
	 * @param id
	 */
	public ProjectItem validateExitsItem(Long id) {
		ProjectItem projectItem = projectItemService.findOne(id).orElse(null);
		if (Objects.isNull(projectItem)) {
			throw new BadRequestException(ErrorCode.CODE_30152042);
		}
		return projectItem;
	}

}
