package com.zhihaoscm.service.util;

import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;

import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <p>租户上下文工具类</p>
 * <p>用于在执行特定方法时临时清空或修改租户ID</p>
 * 
 * <AUTHOR>
 * @since 2024-07-31
 */
public class TenantContextUtil {

    /**
     * 在无租户上下文中执行操作（将tenantId临时设置为null）
     * 
     * @param supplier 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public static <T> T executeWithoutTenant(Supplier<T> supplier) {
        return executeWithTenant(null, supplier);
    }

    /**
     * 在指定租户上下文中执行操作
     * 
     * @param tenantId 临时设置的租户ID
     * @param supplier 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public static <T> T executeWithTenant(Long tenantId, Supplier<T> supplier) {
        UserInfoContext originalContext = UserInfoContextHolder.getContext();
        Long originalTenantId = null;
        
        // 保存原始租户ID
        if (originalContext != null) {
            originalTenantId = originalContext.getTenantId();
        }
        
        try {
            // 设置临时租户ID
            if (originalContext != null) {
                originalContext.setTenantId(tenantId);
            } else if (tenantId != null) {
                // 如果原来没有上下文但需要设置租户ID，创建新的上下文
                UserInfoContext newContext = new UserInfoContext();
                newContext.setTenantId(tenantId);
                UserInfoContextHolder.setContextHolder(newContext);
            }
            
            // 执行业务操作
            return supplier.get();
        } finally {
            // 恢复原始租户ID
            if (originalContext != null) {
                originalContext.setTenantId(originalTenantId);
            } else {
                // 如果原来没有上下文，清理临时创建的上下文
                UserInfoContextHolder.removeContext();
            }
        }
    }

    /**
     * 在无租户上下文中执行操作（无返回值）
     * 
     * @param runnable 要执行的操作
     */
    public static void executeWithoutTenant(Runnable runnable) {
        executeWithTenant(null, runnable);
    }

    /**
     * 在指定租户上下文中执行操作（无返回值）
     *
     * @param tenantId 临时设置的租户ID
     * @param runnable 要执行的操作
     */
    public static void executeWithTenant(Long tenantId, Runnable runnable) {
        executeWithTenant(tenantId, () -> {
            runnable.run();
            return null;
        });
    }

    /**
     * 在无租户上下文中执行带参数的操作
     *
     * @param function 要执行的操作
     * @param parameter 参数
     * @param <T> 参数类型
     * @param <R> 返回值类型
     * @return 操作结果
     */
    public static <T, R> R executeWithoutTenant(Function<T, R> function, T parameter) {
        return executeWithTenant(null, function, parameter);
    }

    /**
     * 在指定租户上下文中执行带参数的操作
     *
     * @param tenantId 临时设置的租户ID
     * @param function 要执行的操作
     * @param parameter 参数
     * @param <T> 参数类型
     * @param <R> 返回值类型
     * @return 操作结果
     */
    public static <T, R> R executeWithTenant(Long tenantId, Function<T, R> function, T parameter) {
        return executeWithTenant(tenantId, () -> function.apply(parameter));
    }
}
