package com.zhihaoscm.usercenter.resource.backend.customer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import com.zhihaoscm.common.api.api.ApiResponse;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.page.Page;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.vo.CustomerVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.usercenter.core.backendservice.BackendCustomerService;
import com.zhihaoscm.usercenter.resource.validator.backend.BackendCustomerValidator;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Tag(name = "客户管理", description = "部门管理API")
@RestController
@RequestMapping("/customer")
public class BackendCustomerResource {

	@Autowired
	private BackendCustomerService service;

	@Autowired
	private BackendCustomerValidator validator;

	@Operation(summary = "查询客户列表")
	@GetMapping(value = "/paging")
	public ApiResponse<Page<CustomerVo>> paging(
			@Parameter(description = "页码") @RequestParam(value = "page", defaultValue = CommonDef.DEFAULT_CURRENT_PAGE_STR) Integer page,
			@Parameter(description = "每页条数") @RequestParam(value = "size", defaultValue = CommonDef.DEFAULT_PAGE_SIZE_STR) Integer size,
			@Parameter(description = "排序关键字") @RequestParam(required = false) String sortKey,
			@Parameter(description = "排序顺序 asc desc") @RequestParam(required = false) String sortOrder,
			@Parameter(description = "状态") @RequestParam(value = "state", required = false) Integer state,
			@Parameter(description = "身份 1：采购客户 2：仓储客户") @RequestParam(value = "roles", required = false) Integer role,
			@Parameter(description = "查询参数") @RequestParam(value = "param", required = false) String param) {
		return new ApiResponse<>(PageUtil.convert(service.paging(page, size,
				sortKey, sortOrder, state, role, param)));
	}

	@Operation(summary = "查看详情")
	@GetMapping("/vo/{id}")
	@Secured(AdminPermissionDef.CUSTOMER_W)
	public ApiResponse<CustomerVo> findVoById(@PathVariable Long id) {
		validator.validateId(id);
		return new ApiResponse<>(service.findVoById(id).orElse(null));
	}

	@Operation(summary = "取消认证")
	@PutMapping("/cancel-apply/{id}")
	@Secured(AdminPermissionDef.CUSTOMER_W)
	public ApiResponse<Void> cancelApply(@PathVariable Long id) {
		Customer customer = validator.validateCancelApply(id);
		service.cancelApply(customer);
		return new ApiResponse<>();
	}

	@Operation(summary = "修改系统信息")
	@PutMapping("/update-system-info/{id}")
	@Secured(AdminPermissionDef.CUSTOMER_W)
	public ApiResponse<Customer> updateSystemInfo(@PathVariable Long id,
			@Parameter(description = "系统名称") @RequestParam(required = false) String systemName,
			@Parameter(description = "系统logo") @RequestParam(required = false) Long systemLogo) {
		Customer customer = validator.validateId(id);
		return new ApiResponse<>(service.updateSystemInfo(customer, systemName, systemLogo).orElse(null));
	}

}
