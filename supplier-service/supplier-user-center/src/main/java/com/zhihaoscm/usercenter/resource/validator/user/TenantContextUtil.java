package com.zhihaoscm.usercenter.resource.validator.user;

import com.alibaba.nacos.shaded.com.google.common.base.Supplier;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;

public class TenantContextUtil {
 


    /**
	 * 在无租户上下文中执行操作（将tenantId临时设置为null）
	 * 
	 * @param supplier
	 *            要执行的操作
	 * @param <T>
	 *            返回值类型
	 * @return 操作结果
	 */
	public static <T> T executeWithoutTenant(Supplier<T> supplier) {
		return executeWithTenant(null, supplier);
	}

	/**
	 * 在指定租户上下文中执行操作
	 * 
	 * @param tenantId
	 *            临时设置的租户ID
	 * @param supplier
	 *            要执行的操作
	 * @param <T>
	 *            返回值类型
	 * @return 操作结果
	 */
	public static <T> T executeWithTenant(Long tenantId, Supplier<T> supplier) {
		UserInfoContext originalContext = UserInfoContextHolder.getContext();
		Long originalTenantId = null;

		// 保存原始租户ID
		if (originalContext != null) {
			originalTenantId = originalContext.getTenantId();
		}

		try {
			// 设置临时租户ID
			if (originalContext != null) {
				originalContext.setTenantId(tenantId);
			} else if (tenantId != null) {
				// 如果原来没有上下文但需要设置租户ID，创建新的上下文
				UserInfoContext newContext = new UserInfoContext();
				newContext.setTenantId(tenantId);
				UserInfoContextHolder.setContextHolder(newContext);
			}

			// 执行业务操作
			return supplier.get();
		} finally {
			// 恢复原始租户ID
			if (originalContext != null) {
				originalContext.setTenantId(originalTenantId);
			} else {
				// 如果原来没有上下文，清理临时创建的上下文
				UserInfoContextHolder.removeContext();
			}
		}
	}
}
