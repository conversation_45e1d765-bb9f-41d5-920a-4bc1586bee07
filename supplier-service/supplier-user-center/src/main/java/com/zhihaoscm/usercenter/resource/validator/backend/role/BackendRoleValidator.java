package com.zhihaoscm.usercenter.resource.validator.backend.role;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.domain.bean.entity.Role;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.core.backendservice.BackendRoleService;
import com.zhihaoscm.usercenter.core.backendservice.BackendUserService;
import com.zhihaoscm.usercenter.resource.form.role.RoleForm;

@Component
public class BackendRoleValidator {

	@Autowired
	private BackendRoleService roleService;

	@Autowired
	private BackendUserService userService;

	/**
	 * 新增验证
	 *
	 * @param form
	 */
	public void validateCreate(RoleForm form) {
		this.validateNameExist(form.getName(), null);
		this.validatePermissionExist(form.getPermissions());
	}

	/**
	 * 验证权限是否存在
	 *
	 * @param permissions
	 */
	private void validatePermissionExist(ArrayString permissions) {
		permissions.forEach(permission -> {
			if (Objects.isNull(
					AdminPermissionDef.AdminPermission.from(permission))) {
				throw new BadRequestException(ErrorCode.CODE_30147008);
			}
		});

	}

	/**
	 * 修改验证
	 *
	 * @param id
	 * @param form
	 */
	public Role validateUpdate(Long id, RoleForm form) {
		Role role = this.validateExist(id);
		this.validateNameExist(form.getName(), role.getId());
		this.validatePermissionExist(form.getPermissions());
		return role;
	}

	/**
	 * 验证角色是否存在
	 *
	 * @param id
	 * @return
	 */
	public Role validateExist(Long id) {
		Role role = roleService.findOne(id).orElse(null);
		if (Objects.isNull(role)) {
			throw new BadRequestException(ErrorCode.CODE_30147007);
		}
		return role;
	}

	/**
	 * 验证角色名称是否存在
	 *
	 * @param name
	 * @param id
	 */
	private void validateNameExist(String name, Long id) {
		Role role = roleService.findByName(name, id).orElse(null);
		if (Objects.nonNull(role)) {
			throw new BadRequestException(ErrorCode.CODE_30147001);
		}
	}

	/**
	 * 删除校验
	 *
	 * @param id
	 */
	public Role validateDelete(Long id) {
		if (userService.verifyWhetherRoleUsed(id)) {
			throw new BadRequestException(ErrorCode.CODE_30147006);
		}
		return this.validateExist(id);
	}
}
