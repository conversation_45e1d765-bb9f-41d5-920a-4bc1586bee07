package com.zhihaoscm.usercenter.core.service.impl;

import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.CustomerBank;
import com.zhihaoscm.domain.meta.biz.CustomerBankDef;
import com.zhihaoscm.usercenter.core.mapper.CustomerBankMapper;
import com.zhihaoscm.usercenter.core.service.CustomerBankService;

/**
 * <p>
 * 客户-银行账户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Service
public class CustomerBankServiceImpl
		extends MpLongIdBaseServiceImpl<CustomerBank, CustomerBankMapper>
		implements CustomerBankService {

	public CustomerBankServiceImpl(CustomerBankMapper repository) {
		super(repository);
	}

	@Override
	public List<CustomerBank> findByCustomerId(Long id, Integer type,
			String name) {
		LambdaQueryWrapper<CustomerBank> wrapper = Wrappers
				.lambdaQuery(CustomerBank.class)
				.eq(CustomerBank::getCustomerId, id)
				.eq(CustomerBank::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.eq(Objects.nonNull(type), CustomerBank::getType, type);
		wrapper.like(Objects.nonNull(name), CustomerBank::getName, name);
		wrapper.eq(CustomerBank::getBizType,
				CustomerBankDef.BizType.CUSTOM.getCode());
		wrapper.orderByDesc(CustomerBank::getIsDefault);
		wrapper.orderByAsc(CustomerBank::getCreatedTime);
		return repository.selectList(wrapper);
	}

	@Override
	public List<CustomerBank> findByCustomerIds(List<Long> ids,
			Integer bizType) {
		return repository.selectList(Wrappers.lambdaQuery(CustomerBank.class)
				.in(CustomerBank::getCustomerId, ids)
				.eq(Objects.nonNull(bizType), CustomerBank::getBizType, bizType)
				.eq(CustomerBank::getDel, CommonDef.Symbol.NO.getCode()));
	}

	@Override
	public List<CustomerBank> findByCustomerIdAndBizType(Long customerId,
			Integer bizType) {
		LambdaQueryWrapper<CustomerBank> wrapper = Wrappers
				.lambdaQuery(CustomerBank.class);
		this.filterDeleted(wrapper);
		wrapper.eq(CustomerBank::getCustomerId, customerId);
		wrapper.eq(CustomerBank::getBizType, bizType);
		return repository.selectList(wrapper);
	}

	@Override
	public Page<CustomerBank> selector(Integer page, Integer size,
			String keyword, Long customerId, Integer bizType) {
		LambdaQueryWrapper<CustomerBank> wrapper = Wrappers
				.lambdaQuery(CustomerBank.class)
				.eq(CustomerBank::getCustomerId, customerId)
				.eq(Objects.nonNull(bizType), CustomerBank::getBizType, bizType)
				.eq(CustomerBank::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.and(StringUtils.isNotBlank(keyword),
				w -> w.like(CustomerBank::getBank, keyword).or()
						.like(CustomerBank::getAccount, keyword));
		// 如果查询客户银行账号不需要
		if (CustomerBankDef.BizType.CUSTOM.match(bizType)) {
			UserInfoContext context = UserInfoContextHolder.getContext();
			context.setTenantId(null);
			UserInfoContextHolder.setContextHolder(context);
		}
		// 默认按更新时间倒序
		wrapper.orderByDesc(CustomerBank::getUpdatedTime);
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void setDefault(CustomerBank customerBank) {
		this.updateNotDefaultByCustomerId(customerBank.getCustomerId());
		customerBank.setIsDefault(CommonDef.Symbol.YES.getCode());
		super.updateAllProperties(customerBank);
	}

	@Override
	public void deleteByCustomerIdAndBizType(Long customerId, Integer bizType) {
		LambdaUpdateWrapper<CustomerBank> wrapper = Wrappers
				.lambdaUpdate(CustomerBank.class)
				.eq(CustomerBank::getDel, CommonDef.Symbol.NO.getCode())
				.eq(CustomerBank::getCustomerId, customerId)
				.eq(CustomerBank::getBizType, bizType)
				.set(CustomerBank::getDel, CommonDef.Symbol.YES.getCode());
		repository.update(wrapper);
	}

	/**
	 * 根据客户ID取消默认
	 *
	 * @param customerId
	 */
	public void updateNotDefaultByCustomerId(Long customerId) {
		LambdaUpdateWrapper<CustomerBank> wrapper = Wrappers
				.lambdaUpdate(CustomerBank.class);
		wrapper.eq(CustomerBank::getCustomerId, customerId);
		wrapper.eq(CustomerBank::getBizType,
				CustomerBankDef.BizType.CUSTOM.getCode());
		wrapper.eq(CustomerBank::getDel, CommonDef.Symbol.NO.getCode());
		wrapper.set(CustomerBank::getIsDefault, CommonDef.Symbol.NO.getCode());
		repository.update(wrapper);
	}

}
