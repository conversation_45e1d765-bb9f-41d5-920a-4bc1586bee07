package com.zhihaoscm.usercenter.resource.validator.user;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.context.UserInfoContext;
import com.zhihaoscm.common.bean.context.UserInfoContextHolder;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.Supplier;
import com.zhihaoscm.domain.bean.entity.User;
import com.zhihaoscm.domain.bean.vo.UserVo;
import com.zhihaoscm.domain.meta.biz.SystemDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.properties.SupplierChainProperties;
import com.zhihaoscm.usercenter.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.usercenter.core.backendservice.BackendSupplierService;
import com.zhihaoscm.usercenter.core.service.UserService;
import com.zhihaoscm.usercenter.resource.form.user.UserForm;
import com.zhihaoscm.usercenter.resource.form.user.UserPasswordForm;
import com.zhihaoscm.usercenter.utils.RegexUtils;

@Component
public class UserValidator {

	@Autowired
	private UserService userService;

	@Autowired
	private SupplierChainProperties supplierChainProperties;

	@Autowired
	private BackendSupplierService supplierService;

	/**
	 * 修改校验
	 *
	 * @param id
	 */
	public User validateUpdate(Long id, UserForm form) {
		User user = this.validateIsExist(id);
		if (StringUtils.isNotBlank(form.getEmployeeId())) {
			// 校验工号唯一性
			User user1 = userService.findByEmployeeId(form.getEmployeeId())
					.orElse(null);
			if (Objects.nonNull(user1) && !user1.getId().equals(user.getId())) {
				throw new BadRequestException(ErrorCode.CODE_30148019);
			}
		}
		return user;
	}

	/**
	 * 校验用户存在
	 *
	 * @param id
	 */
	public void validateExist(Long id) {
		User user = userService.findOne(id).orElse(null);
		if (Objects.nonNull(user)) {
			throw new BadRequestException(ErrorCode.CODE_30148006);
		}
	}

	/**
	 * 校验用户是否存在
	 *
	 * @param id
	 */
	public User validateIsExist(Long id) {
		User user = userService.findOne(id).orElse(null);
		if (Objects.isNull(user)) {
			throw new BadRequestException(ErrorCode.CODE_30148005);
		}
		return user;
	}

	/**
	 * 校验新增
	 *
	 * @param form
	 */
	public void validateCreate(UserForm form) {
		if (SystemDef.SystemType.SAAS
				.match(supplierChainProperties.getType())) {
			// 判断有没有完成组织机构认证
			Long tenantId = Objects.requireNonNull(UserContextHolder.getUser())
					.getTenantId();
			Supplier supplier = supplierService.findByTenantId(tenantId)
					.orElse(new Supplier());
			if (!CommonDef.Symbol.YES.match(supplier.getApplyState())) {
				throw new BadRequestException(ErrorCode.CODE_30148020);
			}
		}

		UserVo userVo = TenantContextUtil.executeWithoutTenant(
				userService::findByMobile, form.getMobile()).orElse(null);

		// 校验手机号唯一性
		if (Objects.nonNull(userVo)) {
			throw new BadRequestException(ErrorCode.CODE_30148017);
		}
		if (StringUtils.isNotBlank(form.getEmployeeId())) {
			// 校验工号唯一性
			User user = userService.findByEmployeeId(form.getEmployeeId())
					.orElse(null);
			if (Objects.nonNull(user)) {
				throw new BadRequestException(ErrorCode.CODE_30148019);
			}
		}
	}

	/**
	 * 校验设置密码
	 *
	 * @param form
	 * @param user
	 */
	public User validatePassword(UserPasswordForm form, User user) {
		if (!RegexUtils.match(RegexUtils.PASSWORD_PATTERN,
				form.getNewPassword())) {
			throw new BadRequestException(ErrorCode.CODE_30059023);
		}

		if (form.getNewPassword().equals(user.getPassword())) {
			throw new BadRequestException(ErrorCode.CODE_30059022);
		}

		if (!form.getNewPassword().equals(form.getConfirmPassword())) {
			throw new BadRequestException(ErrorCode.CODE_30059021);
		}

		if (!CommonDef.Symbol.NO.match(user.getIsSelfSetPassword())) {
			throw new BadRequestException(ErrorCode.CODE_30059025);
		}

		user.setPassword(
				new BCryptPasswordEncoder().encode(form.getNewPassword()));
		user.setIsSelfSetPassword(CommonDef.Symbol.YES.getCode());
		return user;
	}
}
