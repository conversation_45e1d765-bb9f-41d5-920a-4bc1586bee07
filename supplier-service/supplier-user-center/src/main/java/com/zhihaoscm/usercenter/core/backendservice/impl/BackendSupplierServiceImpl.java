package com.zhihaoscm.usercenter.core.backendservice.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.common.bean.json.ArrayString;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.vo.SupplierVo;
import com.zhihaoscm.domain.bean.vo.UserVo;
import com.zhihaoscm.domain.meta.AdminPermissionDef;
import com.zhihaoscm.domain.meta.RedisKeys;
import com.zhihaoscm.domain.meta.biz.*;
import com.zhihaoscm.usercenter.config.properties.SupplierChainProperties;
import com.zhihaoscm.usercenter.core.backendservice.*;
import com.zhihaoscm.usercenter.core.mapper.backend.BackendSupplierMapper;
import com.zhihaoscm.usercenter.core.service.UserEnterpriseCertificationService;
import com.zhihaoscm.usercenter.core.service.UserService;
import com.zhihaoscm.usercenter.utils.TokenUtils;

/**
 * <p>
 * 供应商 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
public class BackendSupplierServiceImpl
		extends MpLongIdBaseServiceImpl<Supplier, BackendSupplierMapper>
		implements BackendSupplierService {

	@Autowired
	private StringRedisClient redisClient;

	@Autowired
	private BackendUserService backendUserService;

	@Autowired
	private SupplierChainProperties supplierChainProperties;

	@Autowired
	private BackendDealingsEnterpriseService dealingsEnterpriseService;

	@Autowired
	private BackendRoleService backendRoleService;

	@Autowired
	private UserEnterpriseCertificationService userEnterpriseCertificationService;

	@Autowired
	private BackendInstitutionApplyService backendInstitutionApplyService;

	@Autowired
	private BackendPlatformBankAccountService backendPlatformBankAccountService;

	@Autowired
	private UserService userService;

	@Autowired
	private BackendUserChangeService backendUserChangeService;

	public BackendSupplierServiceImpl(BackendSupplierMapper repository) {
		super(repository);
	}

	@Override
	public Page<SupplierVo> paging(Integer page, Integer size, String sortKey,
			String sortOrder, String searchParam, Integer role) {
		LambdaQueryWrapper<Supplier> queryWrapper = Wrappers
				.lambdaQuery(Supplier.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.and(StringUtils.isNotBlank(searchParam),
				wrapper -> wrapper.like(Supplier::getAccount, searchParam).or()
						.like(Supplier::getName, searchParam).or()
						.like(Supplier::getCode, searchParam).or()
						.like(Supplier::getInstitutionName, searchParam));
		if (Objects.nonNull(role)) {
			queryWrapper.apply("JSON_CONTAINS(roles, {0}, '$')",
					role.toString());
		}

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 默认按照创建时间降序排列
			queryWrapper.orderByDesc(Supplier::getCreatedTime);
		}
		Page<Supplier> supplierPage = repository
				.selectPage(new Page<>(page, size), queryWrapper);
		List<Supplier> records = supplierPage.getRecords();
		return PageUtil.getRecordsInfoPage(supplierPage, this.packVos(records));
	}

	@Override
	public Page<Supplier> selector(Integer page, Integer size,
			Integer applyState, String institutionName) {
		LambdaQueryWrapper<Supplier> wrapper = Wrappers
				.lambdaQuery(Supplier.class);
		this.filterDeleted(wrapper);
		wrapper.eq(Objects.nonNull(applyState), Supplier::getApplyState,
				applyState);
		wrapper.like(StringUtils.isNotBlank(institutionName),
				Supplier::getInstitutionName, institutionName);
		return repository.selectPage(new Page<>(page, size), wrapper);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Optional<Supplier> createSupplier(Supplier resource) {
		resource.setCode(AutoCodeDef.CREATE_AUTO_CODE.apply(redisClient, "",
				RedisKeys.Cache.SUPPLIER_ID_GENERATOR, "", 4,
				AutoCodeDef.DATE_TYPE.NONE));
		Supplier supplier = super.create(resource);
		supplier.setTenantId(supplier.getId());
		// 创建用户
		User user = fillUser(supplier,null);
		Role role = this.fillRole(supplier);
		user.setRoleIds(new ArrayLong(role.getId()));
		backendUserService.createUser(user);
		return Optional.of(super.update(supplier));
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public Optional<Supplier> updateSupplier(Supplier resource) {
		Supplier old = super.findOne(resource.getId()).orElse(new Supplier());
		UserVo userVo = backendUserService
				.findByAccountAndTenantId(old.getAccount(), resource.getId())
				.orElse(null);
		if (!old.getAccount().equals(resource.getAccount())
				|| !old.getPassword().equals(resource.getPassword())) {
			// 账号和密码修改了，需要更新用户信息
			assert userVo != null;
			User user = fillUser(resource,userVo.getUser().getId());
			user.setRoleIds(userVo.getUser().getRoleIds());
			backendUserService.updateUser(user);
		} else {
            assert userVo != null;
            User user = fillUser(resource,userVo.getUser().getId());
            user.setId(userVo.getUser().getId());
			backendUserService.updateUser(user);
		}
		Supplier supplier = super.updateAllProperties(resource);
		backendUserChangeService.supplierChange(supplier.getId());
		return Optional.of(supplier);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void delete(Long id) {
		super.delete(id);
	}

	@Override
	public Optional<Supplier> findByInstitutionName(String institutionName) {
		LambdaQueryWrapper<Supplier> queryWrapper = Wrappers
				.lambdaQuery(Supplier.class);
		queryWrapper.eq(Supplier::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.eq(StringUtils.isNotBlank(institutionName),
				Supplier::getInstitutionName, institutionName);
		return Optional.ofNullable(repository.selectOne(queryWrapper));
	}

	/**
	 * 根据企业名称模糊查询供应商
	 *
	 * @param institutionName
	 */
	@Override
	public Page<Supplier> findByName(String institutionName, Integer page,
			Integer size, String sortKey, String sortOrder) {
		LambdaQueryWrapper<Supplier> queryWrapper = Wrappers
				.lambdaQuery(Supplier.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Supplier::getDel, CommonDef.Symbol.NO.getCode());
		queryWrapper.like(StringUtils.isNotBlank(institutionName),
				Supplier::getInstitutionName, institutionName);
		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 默认按照更新时间降序排列
			queryWrapper.orderByDesc(Supplier::getUpdatedTime);
		}
		return repository.selectPage(new Page<>(page, size), queryWrapper);
	}

	@Override
	public Optional<SupplierVo> detail(Supplier supplier) {
		return Optional.of(this.packVo(supplier));
	}

	@Override
	public Optional<Supplier> findByTenantId(Long tenantId) {
		if (SystemDef.SystemType.SAAS
				.match(supplierChainProperties.getType())) {
			// SAAS模式下，直接查询
			LambdaQueryWrapper<Supplier> queryWrapper = Wrappers
					.lambdaQuery(Supplier.class);
			this.filterDeleted(queryWrapper);
			queryWrapper.eq(Supplier::getId, tenantId);
			Supplier supplier = repository.selectOne(queryWrapper);
			if (Objects.isNull(supplier)) {
				return Optional.empty();
			} else {
				return Optional.of(supplier);
			}
		} else {
			// 独立部署 返回配置信息
			Supplier supplier = new Supplier();
			supplier.setId(tenantId);
			supplier.setSystemName(supplierChainProperties.getName());
			return Optional.of(supplier);
		}
	}

	@Override
	public void cancelApply(Supplier supplier) {
		supplier.setApplyState(CommonDef.Symbol.NO.getCode());
		supplier.setRoles(null);
		super.updateAllProperties(supplier);
		// 将最新一条已认证的认证记录的是否取消设置成已取消
		backendInstitutionApplyService
				.findByCustomerIdAndStateLatest(supplier.getId(),
						InstitutionApplyDef.State.APPROVED.getCode())
				.ifPresent(apply -> {
					apply.setIsCancel(CommonDef.Symbol.YES.getCode());
					backendInstitutionApplyService.updateAllProperties(apply);
				});

		List<User> userList = userService
				.findByTenantId(supplier.getTenantId());

		if (CollectionUtils.isNotEmpty(userList)) {
			userList.forEach(user -> user.setRoleIds(null));
			userService.batchUpdate(userList);

			// 刷新上下文
			for (User user : userList) {
				TokenUtils.refreshUserContext(user, redisClient,
						30 * 24 * 60 * 60L);
			}
		}

	}

	@Override
	public Optional<Supplier> findByUnifiedSocialCreditCode(
			String unifiedSocialCreditCode) {
		LambdaQueryWrapper<Supplier> queryWrapper = Wrappers
				.lambdaQuery(Supplier.class)
				.eq(Supplier::getUnifiedSocialCreditCode,
						unifiedSocialCreditCode)
				.eq(Supplier::getDel, CommonDef.Symbol.NO.getCode());

		return Optional.ofNullable(repository.selectOne(queryWrapper));
	}

	@Override
	public Optional<Supplier> findByMobile(String mobile) {
		LambdaQueryWrapper<Supplier> queryWrapper = Wrappers
				.lambdaQuery(Supplier.class).eq(Supplier::getAccount, mobile)
				.eq(Supplier::getDel, CommonDef.Symbol.NO.getCode());
		return Optional.ofNullable(repository.selectOne(queryWrapper));
	}

	/**
	 * 详情
	 */
	private SupplierVo packVo(Supplier supplier) {
		SupplierVo supplierVo = new SupplierVo();
		supplierVo.setSupplier(supplier);
		supplierVo.setDealingsEnterpriseList(dealingsEnterpriseService
				.findByTenantId(List.of(supplier.getId())));
		supplierVo.setPlatformBankAccountList(backendPlatformBankAccountService
				.findBySupplierId(supplier.getId()));
		// 本系统已认证
		if (CommonDef.Symbol.YES.match(supplier.getApplyState())) {
			UserEnterpriseCertification userEnterpriseCertification = userEnterpriseCertificationService
					.findByUserIdAndState(supplier.getId(),
							CertificationDef.CertificationState.VALID.getCode())
					.orElse(null);
			// 为空
			if (Objects.isNull(userEnterpriseCertification)) {
				// 已认证 还没进入契约锁 可以取消
				supplierVo.setAuthorizationState(
						CustomerDef.AuthorizationState.NONE.getCode());
				supplierVo.setIsCancelInstitutionApply(
						CommonDef.Symbol.YES.getCode());
			} else {
				// 或者 不为空的时候 且状态等于-1或者2的的时候才能解绑
				if (CertificationDef.EnterpriseCallbackStatus.UNAUTHENTICATED
						.match(userEnterpriseCertification.getStatus())
						|| CertificationDef.EnterpriseCallbackStatus.INVALID
								.match(userEnterpriseCertification
										.getStatus())) {
					supplierVo.setAuthorizationState(
							CustomerDef.AuthorizationState.NONE.getCode());
					supplierVo.setIsCancelInstitutionApply(
							CommonDef.Symbol.YES.getCode());
				} else {
					supplierVo.setAuthorizationState(
							CustomerDef.AuthorizationState.UNAUTHORIZED
									.getCode());
					supplierVo.setIsCancelInstitutionApply(
							CommonDef.Symbol.NO.getCode());
				}
			}
			backendInstitutionApplyService
					.findByCustomerIdAndStateLatest(supplier.getId(),
							InstitutionApplyDef.State.APPROVED.getCode())
					.ifPresent(supplierVo::setInstitutionApply);
		} else {
			supplierVo.setAuthorizationState(
					CustomerDef.AuthorizationState.NONE.getCode());
			supplierVo
					.setIsCancelInstitutionApply(CommonDef.Symbol.NO.getCode());
		}
		// 已授权
		if (CommonDef.Symbol.YES.match(supplier.getSealAdmin())) {
			supplierVo.setAuthorizationState(
					CustomerDef.AuthorizationState.AUTHORIZED.getCode());
		}
		return supplierVo;
	}

	private List<SupplierVo> packVos(List<Supplier> suppliers) {
		if (CollectionUtils.isEmpty(suppliers)) {
			return List.of();
		}
		List<Long> tenantIds = suppliers.stream().map(Supplier::getTenantId)
				.toList();
		List<DealingsEnterprise> dealingsEnterprises = dealingsEnterpriseService
				.findByTenantId(tenantIds);
		Map<Long, List<DealingsEnterprise>> collect = dealingsEnterprises
				.stream().collect(
						Collectors.groupingBy(DealingsEnterprise::getTenantId));

		return suppliers.stream().map(e -> {
			SupplierVo supplierVo = new SupplierVo();
			supplierVo.setSupplier(e);
			List<DealingsEnterprise> dealingsEnterpriseList = collect
					.getOrDefault(e.getTenantId(), List.of());
			supplierVo.setDealingsEnterpriseList(dealingsEnterpriseList);
			return supplierVo;
		}).toList();
	}

	private User fillUser(Supplier supplier,Long userId) {
		User user;
		if (Objects.isNull(userId)) {
			user = new User();
		} else {
			user = backendUserService.findOne(userId).orElse(new User());
		}
		user.setName(supplier.getInstitutionName());
		user.setMobile(supplier.getAccount());
		user.setOrigin(supplier.getOrigin());
		user.setTenantId(supplier.getId());
		user.setPassword(supplier.getPassword());
		user.setMail(supplier.getEmail());
		user.setState(CommonDef.Symbol.YES.getCode());
		user.setIsMain(CommonDef.Symbol.YES.getCode());
		return user;
	}

	private Role fillRole(Supplier supplier) {
		Role role = new Role();
		role.setName(supplier.getInstitutionName());
		role.setRemark("管理员");
		role.setTenantId(supplier.getId());
		role.setPermissions(new ArrayString(
				Arrays.stream(AdminPermissionDef.AdminPermission.values())
						.map(AdminPermissionDef.AdminPermission::getPermission)
						.toList()));
		return backendRoleService.create(role);
	}
}
