package com.zhihaoscm.usercenter.core.backendservice.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.mybatis.plus.service.impl.MpLongIdBaseServiceImpl;
import com.zhihaoscm.common.mybatis.plus.util.PageUtil;
import com.zhihaoscm.common.redis.client.StringRedisClient;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.*;
import com.zhihaoscm.domain.bean.vo.CustomerVo;
import com.zhihaoscm.domain.meta.biz.CertificationDef;
import com.zhihaoscm.domain.meta.biz.CustomerBankDef;
import com.zhihaoscm.domain.meta.biz.CustomerDef;
import com.zhihaoscm.domain.meta.biz.InstitutionApplyDef;
import com.zhihaoscm.usercenter.core.backendservice.BackendCustomerService;
import com.zhihaoscm.usercenter.core.mapper.backend.BackendCustomerMapper;
import com.zhihaoscm.usercenter.core.service.*;
import com.zhihaoscm.usercenter.utils.TokenUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 客户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@Slf4j
@Service
public class BackendCustomerServiceImpl
		extends MpLongIdBaseServiceImpl<Customer, BackendCustomerMapper>
		implements BackendCustomerService {


	@Autowired
	private CustomerEnterpriseCertificationService enterpriseService;

	@Autowired
	private CustomerInvoiceHeaderService customerInvoiceHeaderService;

	@Autowired
	private CustomerBankService customerBankService;

	@Autowired
	private CustomerReceivingAddressService customerReceivingAddressService;

	@Autowired
	private InstitutionApplyService institutionApplyService;

	@Autowired
	private PersonalCertificationService personalCertificationService;

	@Autowired
	private RegularCustomersService regularCustomersService;

	@Autowired
	private StringRedisClient redisClient;


	public BackendCustomerServiceImpl(BackendCustomerMapper repository) {
		super(repository);
	}

	@Override
	public Page<CustomerVo> paging(Integer page, Integer size, String sortKey,
			String sortOrder, Integer state, Integer role, String param) {
		LambdaQueryWrapper<Customer> queryWrapper = Wrappers
				.lambdaQuery(Customer.class);
		this.filterDeleted(queryWrapper);
		queryWrapper.eq(Objects.nonNull(state), Customer::getState, state);

		if (Objects.nonNull(role)) {
			queryWrapper.apply("JSON_CONTAINS(roles, {0}, '$')",
					role.toString());
		}

		queryWrapper.and(StringUtils.isNotBlank(param),
				wrapper -> wrapper.eq(Customer::getMobile, param).or()
						.eq(Customer::getCode, param).or()
						.like(Customer::getInstitutionName, param).or()
						.like(Customer::getRealName, param).or());

		if (StringUtils.isNoneBlank(sortKey, sortOrder)) {
			queryWrapper.last("order by " + sortKey + " " + sortOrder);
		} else {
			// 默认按照创建时间降序排列
			queryWrapper.orderByDesc(Customer::getCreatedTime);
		}
		Page<Customer> paging = repository.selectPage(new Page<>(page, size),
				queryWrapper);
		return PageUtil.getRecordsInfoPage(paging,
				this.packVo(paging.getRecords()));
	}



	@Override
	public Optional<CustomerVo> findVoById(Long id) {
		return super.findOne(id).map(this::packVo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void cancelApply(Customer customer) {
		customer.setApplyState(CommonDef.Symbol.NO.getCode());
		customer.setRoles(null);
		super.updateAllProperties(customer);
		// 将最新一条已认证的认证记录的是否取消设置成已取消
		institutionApplyService
				.findByCustomerIdAndStateLatest(customer.getId(),
						InstitutionApplyDef.State.APPROVED.getCode(),
						InstitutionApplyDef.Source.PURCHASE_CUSTOMER.getCode())
				.ifPresent(apply -> {
					apply.setIsCancel(CommonDef.Symbol.YES.getCode());
					institutionApplyService.updateAllProperties(apply);
				});

		// 刷新上下文
		TokenUtils.refreshCustomerContext(customer, redisClient,
				30 * 24 * 60 * 60L, Boolean.TRUE);

		List<RegularCustomers> regularCustomersList = regularCustomersService
				.findByCustomerIds(List.of(customer.getId()));
		if (CollectionUtils.isNotEmpty(regularCustomersList)) {
			regularCustomersService.batchDelete(regularCustomersList.stream()
					.map(RegularCustomers::getId).distinct().toList());
		}

	}
	
	
	@Override
    public Optional<Customer> updateSystemInfo(Customer customer,
                                               String systemName, Long systemLogo) {
		customer.setSystemName(systemName);
		customer.setSystemLogo(systemLogo);
		Customer update = super.update(customer);
		// 刷新上下文
		TokenUtils.refreshCustomerContext(customer, redisClient,
				30 * 24 * 60 * 60L, Boolean.TRUE);
		return Optional.of(update);
	}



	/**
	 * 组装用户相关信息
	 *
	 * @param customers
	 * @return
	 */
	private List<CustomerVo> packVo(List<Customer> customers) {
		if (CollectionUtils.isEmpty(customers)) {
			return List.of();
		}
		// 获取所有不重复的客户id
		List<Long> ids = customers.stream().map(Customer::getId)
				.collect(Collectors.toList());
		// 统计客户的地址数量
		List<CustomerReceivingAddress> addresses = customerReceivingAddressService
				.findByCustomerIds(ids);
		Map<Long, Long> addressCountMap = addresses.stream()
				.collect(Collectors.groupingBy(
						CustomerReceivingAddress::getCustomerId,
						Collectors.counting()));
		// 统计客户的银行卡数量
		List<CustomerBank> banks = customerBankService.findByCustomerIds(ids,
				CustomerBankDef.BizType.CUSTOM.getCode());
		Map<Long, Long> bankCountMap = banks.stream()
				.collect(Collectors.groupingBy(CustomerBank::getCustomerId,
						Collectors.counting()));
		// 统计客户的发票抬头数量
		List<CustomerInvoiceHeader> invoiceHeaders = customerInvoiceHeaderService
				.findByCustomerIds(ids);
		Map<Long, Long> invoiceHeaderCountMap = invoiceHeaders.stream()
				.collect(Collectors.groupingBy(
						CustomerInvoiceHeader::getCustomerId,
						Collectors.counting()));
		// 获取客户的企业认证信息
		List<CustomerEnterpriseCertification> enterprises = enterpriseService
				.findByCustomerIds(ids,
						CertificationDef.CertificationState.VALID.getCode());
		Map<Long, CustomerEnterpriseCertification> enterpriseMap = enterprises
				.stream()
				.collect(Collectors.toMap(
						CustomerEnterpriseCertification::getCustomerId,
						Function.identity()));

		return customers.stream().map(customer -> {
			CustomerVo vo = new CustomerVo();
			vo.setCustomer(customer);
			// 本系统已认证
			if (CommonDef.Symbol.YES.match(customer.getApplyState())) {
				institutionApplyService
						.findByCustomerIdAndStateLatest(customer.getId(),
								InstitutionApplyDef.State.APPROVED.getCode(),
								InstitutionApplyDef.Source.PURCHASE_CUSTOMER
										.getCode())
						.ifPresent(vo::setInstitutionApply);
			}
			vo.setAddressCount(Math.toIntExact(
					addressCountMap.getOrDefault(customer.getId(), 0L)));
			vo.setBankCount(Math.toIntExact(
					bankCountMap.getOrDefault(customer.getId(), 0L)));
			vo.setInvoiceHeaderCount(Math.toIntExact(
					invoiceHeaderCountMap.getOrDefault(customer.getId(), 0L)));
			vo.setEnterpriseCertification(enterpriseMap.get(customer.getId()));
			return vo;
		}).collect(Collectors.toList());
	}

	/**
	 * 组装用户相关信息
	 *
	 * @param customer
	 * @return
	 */
	private CustomerVo packVo(Customer customer) {
		CustomerVo vo = new CustomerVo();
		vo.setCustomer(customer);

		enterpriseService
				.findByCustomerIdAndState(customer.getId(),
						CertificationDef.CertificationState.VALID.getCode())
				.ifPresent(enterprise -> {
					if (enterprise.getStatus().equals(
							CertificationDef.EnterpriseCallbackStatus.PASS
									.getCode())) {
						vo.setEnterpriseCertification(enterprise);
					}
				});

		personalCertificationService
				.findByCustomerIdLatest(customer.getId(),
						CertificationDef.CertificationState.VALID.getCode())
				.ifPresent(personal -> {
					if (personal.getStatus()
							.equals(CertificationDef.CertificationStatus.PASS
									.getCode())) {
						vo.setPersonalCertification(personal);
					}
				});

		List<CustomerInvoiceHeader> invoiceHeaders = customerInvoiceHeaderService
				.findByCustomerId(customer.getId(), null, null, null);
		vo.setInvoiceHeaders(invoiceHeaders);

		List<CustomerBank> banks = customerBankService
				.findByCustomerId(customer.getId(), null, null);
		vo.setBanks(banks);

		List<CustomerReceivingAddress> addresses = customerReceivingAddressService
				.findByCustomerId(customer.getId(), null);
		vo.setAddresses(addresses);
		// 本系统已认证
		if (CommonDef.Symbol.YES.match(customer.getApplyState())) {
			CustomerEnterpriseCertification customerEnterpriseCertification = enterpriseService
					.findByCustomerIdAndState(customer.getId(),
							CertificationDef.CertificationState.VALID.getCode())
					.orElse(null);
			// 为空
			if (Objects.isNull(customerEnterpriseCertification)) {
				// 已认证 还没进入契约锁 可以取消
				vo.setAuthorizationState(
						CustomerDef.AuthorizationState.NONE.getCode());
				vo.setIsCancelInstitutionApply(CommonDef.Symbol.YES.getCode());
			} else {
				// 或者 不为空的时候 且状态等于-1或者2的的时候才能解绑
				if (CertificationDef.EnterpriseCallbackStatus.UNAUTHENTICATED
						.match(customerEnterpriseCertification.getStatus())
						|| CertificationDef.EnterpriseCallbackStatus.INVALID
						.match(customerEnterpriseCertification
								.getStatus())) {
					vo.setAuthorizationState(
							CustomerDef.AuthorizationState.NONE.getCode());
					vo.setIsCancelInstitutionApply(
							CommonDef.Symbol.YES.getCode());
				} else {
					vo.setAuthorizationState(
							CustomerDef.AuthorizationState.UNAUTHORIZED
									.getCode());
					vo.setIsCancelInstitutionApply(
							CommonDef.Symbol.NO.getCode());
				}
			}
			institutionApplyService
					.findByCustomerIdAndStateLatest(customer.getId(),
							InstitutionApplyDef.State.APPROVED.getCode(),
							InstitutionApplyDef.Source.PURCHASE_CUSTOMER
									.getCode())
					.ifPresent(vo::setInstitutionApply);
		} else {
			vo.setAuthorizationState(
					CustomerDef.AuthorizationState.NONE.getCode());
			vo.setIsCancelInstitutionApply(CommonDef.Symbol.NO.getCode());
		}
		// 已授权
		if (CommonDef.Symbol.YES.match(customer.getSealAdmin())) {
			vo.setAuthorizationState(
					CustomerDef.AuthorizationState.AUTHORIZED.getCode());
		}
		return vo;
	}


}
