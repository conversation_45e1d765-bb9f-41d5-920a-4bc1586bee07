package com.zhihaoscm.usercenter.resource.validator.institution.apply;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zhihaoscm.common.api.exception.BadRequestException;
import com.zhihaoscm.common.bean.constants.CommonDef;
import com.zhihaoscm.common.util.utils.StringUtils;
import com.zhihaoscm.domain.bean.entity.Customer;
import com.zhihaoscm.domain.bean.entity.InstitutionApply;
import com.zhihaoscm.domain.bean.entity.Supplier;
import com.zhihaoscm.domain.meta.biz.InstitutionApplyDef;
import com.zhihaoscm.domain.meta.error.ErrorCode;
import com.zhihaoscm.usercenter.config.security.admin.filter.UserContextHolder;
import com.zhihaoscm.usercenter.config.security.custom.CustomerContextHolder;
import com.zhihaoscm.usercenter.core.backendservice.BackendSupplierService;
import com.zhihaoscm.usercenter.core.service.CustomerService;
import com.zhihaoscm.usercenter.core.service.InstitutionApplyService;
import com.zhihaoscm.usercenter.resource.form.institution.apply.ApplyForm;
import com.zhihaoscm.usercenter.resource.form.institution.apply.ChangeIdentityForm;
import com.zhihaoscm.usercenter.resource.form.institution.apply.InstitutionApplyForm;

/**
 * 机构认证校验器
 */
@Component
public class InstitutionApplyValidator {

	@Autowired
	private InstitutionApplyService service;
	@Autowired
	private CustomerService customerService;

	@Autowired
	private BackendSupplierService backendSupplierService;

	/**
	 * 校验是否存在
	 *
	 * @param id
	 * @return
	 */
	public InstitutionApply validateExist(Long id) {
		// 根据id查询机构认证信息，如果不存在则抛出异常
		return service.findOne(id).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30022001));
	}

	/**
	 * 校验审核
	 *
	 * @param id
	 * @param form
	 * @return
	 */
	public InstitutionApply validateApprove(Long id, ApplyForm form) {
		// 根据id查询机构认证信息
		InstitutionApply institutionApply = this.validateExist(id);
		// 如果审核状态不为待审核，则抛出异常
		if (!InstitutionApplyDef.State.PENDING_APPROVE
				.match(institutionApply.getState())) {
			throw new BadRequestException(ErrorCode.CODE_30022013);
		}
		// 如果是通过 则进行校验统一信息代码是否重复
		switch (InstitutionApplyDef.State.from(form.getState())) {
			case APPROVED -> {
				// 根据统一社会信用代码查询客户信息，如果存在且不是当前登录客户，则抛出异常
				this.validateExistUnifiedSocialCreditCode(
						institutionApply.getUnifiedSocialCreditCode(), id,
						institutionApply.getCustomerId());

				if (CollectionUtils.isEmpty(form.getRoles())) {
					throw new BadRequestException(ErrorCode.CODE_30022022);
				}
				institutionApply.setRoles(form.getRoles());
			}
			case REJECTED -> {
				if (StringUtils.isBlank(form.getRemark())) {
					throw new BadRequestException(ErrorCode.CODE_30022018);
				}
			}
		}
		return institutionApply;
	}

	/**
	 * 校验新增
	 *
	 * @param form
	 */
	public void validateCreate(InstitutionApplyForm form) {

		Customer customer2 = this.validateCustomerExist(CustomerContextHolder
				.getCustomerLoginVo().getActualAccount().getId());

		if (CommonDef.Symbol.YES.match(customer2.getApplyState())) {
			throw new BadRequestException(ErrorCode.CODE_30022016);
		} else {
			// 未认证，有认证记录但是最新记录状态是已通过，就是已解除的
			InstitutionApply institutionApply = service
					.findByCustomerIdAndStateLatest(customer2.getId(), null,
							InstitutionApplyDef.Source.PURCHASE_CUSTOMER
									.getCode())
					.orElse(null);

			if ((Objects.nonNull(institutionApply)
					&& !InstitutionApplyDef.State.APPROVED
							.match(institutionApply.getState()))) {
				throw new BadRequestException(ErrorCode.CODE_30022017);
			}
		}

		// 根据统一社会信用代码查询客户信息，如果存在则抛出异常
		Supplier supplier1 = backendSupplierService
				.findByUnifiedSocialCreditCode(
						form.getUnifiedSocialCreditCode())
				.orElse(null);
		Customer customer = customerService.findByUnifiedSocialCreditCode(
				form.getUnifiedSocialCreditCode()).orElse(null);
		if (Objects.nonNull(supplier1) || Objects.nonNull(customer)) {
			throw new BadRequestException(ErrorCode.CODE_30022012);
		}
		// 校验一下认证记录已通过的且没有取消的数据
		List<InstitutionApply> institutionApplies = service
				.findByUnifiedSocialCreditCodeAndStateAndIsCancel(
						form.getUnifiedSocialCreditCode(),
						InstitutionApplyDef.State.APPROVED.getCode(),
						CommonDef.Symbol.NO.getCode(), null);
		if (CollectionUtils.isNotEmpty(institutionApplies)) {
			throw new BadRequestException(ErrorCode.CODE_30022012);
		}
	}

	/**
	 * 校验修改
	 *
	 * @param id
	 * @param form
	 * @return
	 */
	public InstitutionApply validateUpdate(Long id, InstitutionApplyForm form) {
		// 根据id查询机构认证信息
		InstitutionApply institutionApply = this.validateExist(id);
		Customer customer = this.validateCustomerExist(CustomerContextHolder
				.getCustomerLoginVo().getActualAccount().getId());

		if (CommonDef.Symbol.NO.match(customer.getApplyState())) {
			// 未认证，有认证记录但是最新记录状态是已驳回的，修改
			InstitutionApply exist = service.findByCustomerIdAndStateLatest(
					customer.getId(), null,
					InstitutionApplyDef.Source.PURCHASE_CUSTOMER.getCode())
					.orElse(null);
			if (Objects.nonNull(exist) && !InstitutionApplyDef.State.REJECTED
					.match(exist.getState())) {
				throw new BadRequestException(ErrorCode.CODE_30022015);
			}
		} else {
			throw new BadRequestException(ErrorCode.CODE_30022016);
		}
		// 如果当前登录客户不是该机构认证信息的所有者，则抛出异常
		if (!institutionApply.getCustomerId().equals(CustomerContextHolder
				.getCustomerLoginVo().getActualAccount().getId())) {
			throw new BadRequestException(ErrorCode.CODE_30022014);
		}

		this.validateExistUnifiedSocialCreditCode(
				form.getUnifiedSocialCreditCode(), id, CustomerContextHolder
						.getCustomerLoginVo().getActualAccount().getId());
		return institutionApply;
	}

	/**
	 * 校验统一社会信用代码是否重复
	 *
	 * @param unifiedSocialCreditCode
	 * @param id
	 */
	private void validateExistUnifiedSocialCreditCode(
			String unifiedSocialCreditCode, Long id, Long customerId) {
		// 根据统一社会信用代码查询客户信息，如果存在且不是当前登录客户，则抛出异常
		Customer customer = customerService
				.findByUnifiedSocialCreditCode(unifiedSocialCreditCode)
				.orElse(null);

		Supplier supplier = backendSupplierService
				.findByUnifiedSocialCreditCode(unifiedSocialCreditCode)
				.orElse(null);
		if ((Objects.nonNull(customer) && !customer.getId().equals(customerId))
				|| (Objects.nonNull(supplier)
						&& !supplier.getId().equals(customerId))) {
			throw new BadRequestException(ErrorCode.CODE_30022012);
		}

		// 校验一下认证记录已通过的且没有取消的数据 排除自己
		List<InstitutionApply> institutionApplies = service
				.findByUnifiedSocialCreditCodeAndStateAndIsCancel(
						unifiedSocialCreditCode,
						InstitutionApplyDef.State.APPROVED.getCode(),
						CommonDef.Symbol.NO.getCode(), id);
		if (CollectionUtils.isNotEmpty(institutionApplies)) {
			throw new BadRequestException(ErrorCode.CODE_30022012);
		}
	}

	/**
	 * 校验客户是否存在
	 *
	 * @param customerId
	 * @return
	 */
	public Customer validateCustomerExist(Long customerId) {
		return customerService.findOne(customerId).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30051011));
	}

	/**
	 * 校验变更身份
	 * 
	 * @param id
	 * @param form
	 * @return
	 */
	public InstitutionApply validateChangeIdentity(Long id,
			ChangeIdentityForm form) {
		InstitutionApply institutionApply = this.validateExist(id);
		institutionApply.setRoles(form.getRoles());
		return institutionApply;
	}

	/**
	 * 校验供应链新增
	 *
	 * @param form
	 */
	public void validateAdminCreate(InstitutionApplyForm form) {
		Supplier supplier = this.validateUserExist(Objects
				.requireNonNull(UserContextHolder.getUser()).getTenantId());

		if (CommonDef.Symbol.YES.match(supplier.getApplyState())) {
			throw new BadRequestException(ErrorCode.CODE_30022016);
		} else {
			// 未认证，有认证记录但是最新记录状态是已通过，就是已解除的
			InstitutionApply institutionApply = service
					.findByCustomerIdAndStateLatest(supplier.getId(), null,
							InstitutionApplyDef.Source.WAREHOUSE_CUSTOMER
									.getCode())
					.orElse(null);

			if ((Objects.nonNull(institutionApply)
					&& !InstitutionApplyDef.State.APPROVED
							.match(institutionApply.getState()))) {
				throw new BadRequestException(ErrorCode.CODE_30022017);
			}
		}

		// 根据统一社会信用代码查询客户信息，如果存在则抛出异常
		Supplier supplier1 = backendSupplierService
				.findByUnifiedSocialCreditCode(
						form.getUnifiedSocialCreditCode())
				.orElse(null);
		Customer customer = customerService.findByUnifiedSocialCreditCode(
				form.getUnifiedSocialCreditCode()).orElse(null);
		if ((Objects.nonNull(supplier1) && !supplier1.getId()
				.equals(UserContextHolder.getUser().getTenantId()))
				|| Objects.nonNull(customer)) {
			throw new BadRequestException(ErrorCode.CODE_30022012);
		}
		// 校验一下认证记录已通过的且没有取消的数据
		List<InstitutionApply> institutionApplies = service
				.findByUnifiedSocialCreditCodeAndStateAndIsCancel(
						form.getUnifiedSocialCreditCode(),
						InstitutionApplyDef.State.APPROVED.getCode(),
						CommonDef.Symbol.NO.getCode(), null);
		if (CollectionUtils.isNotEmpty(institutionApplies)) {
			throw new BadRequestException(ErrorCode.CODE_30022012);
		}
	}

	/**
	 * 校验供应链修改
	 *
	 * @param id
	 * @param form
	 * @return
	 */
	public InstitutionApply validateAdminUpdate(Long id,
			InstitutionApplyForm form) {
		// 根据id查询机构认证信息
		InstitutionApply institutionApply = this.validateExist(id);
		Supplier supplier = this.validateUserExist(Objects
				.requireNonNull(UserContextHolder.getUser()).getTenantId());

		if (CommonDef.Symbol.NO.match(supplier.getApplyState())) {
			// 未认证，有认证记录但是最新记录状态是已驳回的，修改
			InstitutionApply exist = service.findByCustomerIdAndStateLatest(
					supplier.getId(), null,
					InstitutionApplyDef.Source.WAREHOUSE_CUSTOMER.getCode())
					.orElse(null);
			if (Objects.nonNull(exist) && !InstitutionApplyDef.State.REJECTED
					.match(exist.getState())) {
				throw new BadRequestException(ErrorCode.CODE_30022015);
			}
		} else {
			throw new BadRequestException(ErrorCode.CODE_30022016);
		}
		// 如果当前登录客户不是该机构认证信息的所有者，则抛出异常
		if (!institutionApply.getCustomerId().equals(supplier.getId())) {
			throw new BadRequestException(ErrorCode.CODE_30022014);
		}

		this.validateExistUnifiedSocialCreditCode(
				form.getUnifiedSocialCreditCode(), id,
				UserContextHolder.getUser().getTenantId());
		return institutionApply;
	}

	/**
	 * 校验用户是否存在
	 *
	 * @param userId
	 * @return
	 */
	public Supplier validateUserExist(Long userId) {
		return backendSupplierService.findOne(userId).orElseThrow(
				() -> new BadRequestException(ErrorCode.CODE_30096009));
	}
}
