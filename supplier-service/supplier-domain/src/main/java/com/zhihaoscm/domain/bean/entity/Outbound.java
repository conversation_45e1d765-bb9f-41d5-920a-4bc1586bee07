package com.zhihaoscm.domain.bean.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhihaoscm.common.bean.entity.BaseEntityWithStringId;
import com.zhihaoscm.common.bean.json.ArrayLong;
import com.zhihaoscm.domain.annotation.FileId;
import com.zhihaoscm.domain.bean.json.Enterprise;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 出库单
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Getter
@Setter
@TableName("t_outbound")
public class Outbound extends BaseEntityWithStringId {

	/**
	 * 归属类型：1 进销存 2 仓储
	 */
	private Integer type;

	/**
	 * 项目id
	 */
	private String projectId;

	/**
	 * 合同id
	 */
	private String contractId;

	/**
	 * 货物名称id
	 */
	private Long goodsNameId;

	/**
	 * 签收单id
	 */
	private String receiptId;

	/**
	 * 采购方往来企业id
	 */
	private Long purchaserBusinessId;

	/**
	 * 采购方id
	 */
	private Long purchaserId;

	/**
	 * 采购方信息
	 */
	private Enterprise purchaserEnterprise;

	/**
	 * 销售方往来企业id
	 */
	private Long sellerBusinessId;

	/**
	 * 销售方id
	 */
	private Long sellerId;

	/**
	 * 销售方信息
	 */
	private Enterprise sellerEnterprise;

	/**
	 * 出库类型
	 */
	private Integer outboundType;

	/**
	 * 签署类型（线上/线下）
	 */
	private Integer signType;

	/**
	 * 仓库id
	 */
	private String warehouseId;

	/**
	 * 库位id
	 */
	private Long storageId;

	/**
	 * 质押数量
	 */
	private BigDecimal pledgeWeight;

	/**
	 * 可销售数量
	 */
	private BigDecimal canSaleWeight;

	/**
	 * 是否计算出库金额
	 */
	private Integer calcOutboundAmount;

	/**
	 * 货物信息
	 */
	private String goodsInfo;

	/**
	 * 出库总重量
	 */
	private BigDecimal outboundWeight;

	/**
	 * 出库总金额
	 */
	private BigDecimal outboundAmount;

	/**
	 * 是否有检测
	 */
	private Integer detection;

	/**
	 * 检测信息
	 */
	private String detectionInfo;

	/**
	 * 出库检测表
	 */
	@FileId
	private Long outboundDetectionFileId;

	/**
	 * 出库单据id（线下）
	 */
	@FileId
	private Long outboundFileId;

	/**
	 * 检测人
	 */
	private String detector;

	/**
	 * 检测日期
	 */
	private LocalDateTime detectionDate;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 出库照片
	 */
	@FileId
	private ArrayLong outboundPic;

	/**
	 * 出库状态
	 */
	private Integer state;

	/**
	 * 出库人
	 */
	private Long outboundPerson;

	/**
	 * 出库人名称
	 */
	private String outboundPersonName;

	/**
	 * 出库时间
	 */
	private LocalDateTime outboundTime;

	/**
	 * 签署状态
	 */
	private Integer signState;

	/**
	 * 驳回时间
	 */
	private LocalDateTime rejectTime;

	/**
	 * 驳回原因
	 */
	private String rejectReason;

	/**
	 * 签署文件id
	 */
	@FileId
	private Long signFileId;

	/**
	 * 作废合同文件id
	 */
	@FileId
	private Long invalidFileId;

	/**
	 * 作废发起方
	 */
	private Integer invalidInitiator;

	/**
	 * 采购方作废时间
	 */
	private LocalDateTime purchaseInvalidTime;

	/**
	 * 销售方作废时间
	 */
	private LocalDateTime sellerInvalidTime;

	/**
	 * 作废驳回时间
	 */
	private LocalDateTime invalidRevokeTime;

	/**
	 * 作废签署状态
	 */
	private Integer invalidSignState;

	/**
	 * 作废原因
	 */
	private String invalidReason;

	/**
	 * 驳回原因
	 */
	private String invalidRevokeReason;

	/**
	 * 收费标准
	 */
	private BigDecimal feeStandards;

	/**
	 * 入库费用
	 */
	private BigDecimal outboundFee;

	/**
	 * 审核人
	 */
	private Long reviewer;

	/**
	 * 审核时间
	 */
	private LocalDateTime reviewTime;
}
