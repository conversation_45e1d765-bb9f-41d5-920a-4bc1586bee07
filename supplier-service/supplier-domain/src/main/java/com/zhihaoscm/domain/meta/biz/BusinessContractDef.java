package com.zhihaoscm.domain.meta.biz;

import lombok.Getter;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

public interface BusinessContractDef {

    /**
     * 设置业务状态和签署状态
     */
    BiFunction<Integer, Integer, Integer> SET_STATE = (highState,
                                                       lowState) -> (highState << 16) | lowState;

    Function<Integer, Integer> GET_HIGH_STATE = state -> state >> 16;

    @Getter
    enum CommonSignState {

        UNSIGNED(0, "双方未签署"), BUYER_SIGNED(1, "买方签署完成"), SUPPLY_SIGNED(2,
                "销售方签署完成"), COMPLETED(3, "双方签署已完成");

        private final Integer code;

        private final String desc;

        private static final Map<Integer, PurchaseContractDef.CommonSignState> MAPPING;

        static {
            {
                Map<Integer, PurchaseContractDef.CommonSignState> mapping = new HashMap<>();
                for (PurchaseContractDef.CommonSignState value : PurchaseContractDef.CommonSignState.values()) {
                    mapping.put(value.getCode(), value);
                }
                MAPPING = mapping;
            }
        }

        CommonSignState(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static PurchaseContractDef.CommonSignState from(final Integer code) {
            PurchaseContractDef.CommonSignState type = MAPPING.get(code);
            Assert.notNull(type, "wrong sign state: " + code);
            return type;
        }

        public boolean match(final Integer code) {
            return this.code.equals(code);
        }

    }

    static void main(String[] args) {
        // 草稿双方未签署 65536
        // System.out.println(BusinessContractDef.SET_STATE.apply(
        //         OrderDef.Status.DRAFT.getCode(),
        //         BusinessContractDef.CommonSignState.UNSIGNED
        //                 .getCode()));
        //草稿 买方签署完成 65537
        // System.out.println(BusinessContractDef.SET_STATE.apply(
        //         OrderDef.Status.DRAFT.getCode(),
        //         BusinessContractDef.CommonSignState.BUYER_SIGNED
        //                 .getCode()));
        // 待发起-买方签署 589825
        System.out.println(BusinessContractDef.SET_STATE.apply(
                OrderDef.Status.TO_BE_INITIATED.getCode(),
                BusinessContractDef.CommonSignState.BUYER_SIGNED
                        .getCode()));
        //  待发起-双方未签署 589824
        System.out.println(BusinessContractDef.SET_STATE.apply(
                OrderDef.Status.TO_BE_INITIATED.getCode(),
                BusinessContractDef.CommonSignState.UNSIGNED
                        .getCode()));
        // 458752 作废中-双方未签署
        System.out.println(BusinessContractDef.SET_STATE.apply(
                OrderDef.Status.INVALIDING.getCode(),
                BusinessContractDef.CommonSignState.UNSIGNED
                        .getCode()));
        // 458753 作废中-买方签署完成
        System.out.println(BusinessContractDef.SET_STATE.apply(
                OrderDef.Status.INVALIDING.getCode(),
                BusinessContractDef.CommonSignState.BUYER_SIGNED
                        .getCode()));

        System.out.println(131071 >> 16);
    }

}
